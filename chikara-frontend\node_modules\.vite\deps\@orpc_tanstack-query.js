import {
  skipToken,
  streamedQuery
} from "./chunk-YONSKLQS.js";
import {
  isAsyncIteratorObject,
  stringifyJSON,
  toArray
} from "./chunk-3W6KOZID.js";
import "./chunk-G3PMV62Z.js";

// ../node_modules/@orpc/tanstack-query/dist/index.mjs
function generateOperationKey(path, state = {}) {
  return [path, {
    ...state.input !== void 0 ? { input: state.input } : {},
    ...state.type !== void 0 ? { type: state.type } : {},
    ...state.fnOptions !== void 0 ? { fnOptions: state.fnOptions } : {}
  }];
}
function createGeneralUtils(path) {
  return {
    key(options) {
      return generateOperationKey(path, options);
    }
  };
}
function experimental_liveQuery(queryFn) {
  return async (context) => {
    const stream = await queryFn(context);
    let last;
    for await (const chunk of stream) {
      if (context.signal.aborted) {
        break;
      }
      last = { chunk };
      context.client.setQueryData(context.queryKey, chunk);
    }
    if (!last) {
      throw new Error(
        `Live query for ${stringifyJSON(context.queryKey)} did not yield any data. Ensure the query function returns an AsyncIterable with at least one chunk.`
      );
    }
    return last.chunk;
  };
}
var OPERATION_CONTEXT_SYMBOL = Symbol("ORPC_OPERATION_CONTEXT");
function createProcedureUtils(client, options) {
  const utils = {
    call: client,
    queryKey(...[optionsIn = {}]) {
      const queryKey = optionsIn.queryKey ?? generateOperationKey(options.path, { type: "query", input: optionsIn.input });
      return queryKey;
    },
    queryOptions(...[optionsIn = {}]) {
      const queryKey = utils.queryKey(optionsIn);
      return {
        queryFn: ({ signal }) => {
          if (optionsIn.input === skipToken) {
            throw new Error("queryFn should not be called with skipToken used as input");
          }
          return client(optionsIn.input, {
            signal,
            context: {
              [OPERATION_CONTEXT_SYMBOL]: {
                key: queryKey,
                type: "query"
              },
              ...optionsIn.context
            }
          });
        },
        enabled: optionsIn.input !== skipToken,
        ...optionsIn,
        queryKey
      };
    },
    experimental_streamedKey(...[optionsIn = {}]) {
      const queryKey = optionsIn.queryKey ?? generateOperationKey(options.path, { type: "streamed", input: optionsIn.input, fnOptions: optionsIn.queryFnOptions });
      return queryKey;
    },
    experimental_streamedOptions(...[optionsIn = {}]) {
      const queryKey = utils.experimental_streamedKey(optionsIn);
      return {
        enabled: optionsIn.input !== skipToken,
        queryFn: streamedQuery({
          queryFn: async ({ signal }) => {
            if (optionsIn.input === skipToken) {
              throw new Error("queryFn should not be called with skipToken used as input");
            }
            const output = await client(optionsIn.input, {
              signal,
              context: {
                [OPERATION_CONTEXT_SYMBOL]: {
                  key: queryKey,
                  type: "streamed"
                },
                ...optionsIn.context
              }
            });
            if (!isAsyncIteratorObject(output)) {
              throw new Error("streamedQuery requires an event iterator output");
            }
            return output;
          },
          ...optionsIn.queryFnOptions
        }),
        ...optionsIn,
        queryKey
      };
    },
    experimental_liveKey(...[optionsIn = {}]) {
      const queryKey = optionsIn.queryKey ?? generateOperationKey(options.path, { type: "live", input: optionsIn.input });
      return queryKey;
    },
    experimental_liveOptions(...[optionsIn = {}]) {
      const queryKey = utils.experimental_liveKey(optionsIn);
      return {
        enabled: optionsIn.input !== skipToken,
        queryFn: experimental_liveQuery(async ({ signal }) => {
          if (optionsIn.input === skipToken) {
            throw new Error("queryFn should not be called with skipToken used as input");
          }
          const output = await client(optionsIn.input, {
            signal,
            context: {
              [OPERATION_CONTEXT_SYMBOL]: {
                key: queryKey,
                type: "live"
              },
              ...optionsIn.context
            }
          });
          if (!isAsyncIteratorObject(output)) {
            throw new Error("liveQuery requires an event iterator output");
          }
          return output;
        }),
        ...optionsIn,
        queryKey
      };
    },
    infiniteKey(optionsIn) {
      const queryKey = optionsIn.queryKey ?? generateOperationKey(options.path, {
        type: "infinite",
        input: optionsIn.input === skipToken ? skipToken : optionsIn.input(optionsIn.initialPageParam)
      });
      return queryKey;
    },
    infiniteOptions(optionsIn) {
      const queryKey = utils.infiniteKey(optionsIn);
      return {
        queryFn: ({ pageParam, signal }) => {
          if (optionsIn.input === skipToken) {
            throw new Error("queryFn should not be called with skipToken used as input");
          }
          return client(optionsIn.input(pageParam), {
            signal,
            context: {
              [OPERATION_CONTEXT_SYMBOL]: {
                key: queryKey,
                type: "infinite"
              },
              ...optionsIn.context
            }
          });
        },
        enabled: optionsIn.input !== skipToken,
        ...optionsIn,
        queryKey
      };
    },
    mutationKey(...[optionsIn = {}]) {
      const mutationKey = optionsIn.mutationKey ?? generateOperationKey(options.path, { type: "mutation" });
      return mutationKey;
    },
    mutationOptions(...[optionsIn = {}]) {
      const mutationKey = utils.mutationKey(optionsIn);
      return {
        mutationFn: (input) => client(input, {
          context: {
            [OPERATION_CONTEXT_SYMBOL]: {
              key: mutationKey,
              type: "mutation"
            },
            ...optionsIn.context
          }
        }),
        ...optionsIn,
        mutationKey
      };
    }
  };
  return utils;
}
function createRouterUtils(client, options = {}) {
  const path = toArray(options.path);
  const generalUtils = createGeneralUtils(path);
  const procedureUtils = createProcedureUtils(client, { path });
  const recursive = new Proxy({
    ...generalUtils,
    ...procedureUtils
  }, {
    get(target, prop) {
      const value = Reflect.get(target, prop);
      if (typeof prop !== "string") {
        return value;
      }
      const nextUtils = createRouterUtils(client[prop], { ...options, path: [...path, prop] });
      if (typeof value !== "function") {
        return nextUtils;
      }
      return new Proxy(value, {
        get(_, prop2) {
          return Reflect.get(nextUtils, prop2);
        }
      });
    }
  });
  return recursive;
}
export {
  OPERATION_CONTEXT_SYMBOL,
  OPERATION_CONTEXT_SYMBOL as TANSTACK_QUERY_OPERATION_CONTEXT_SYMBOL,
  createGeneralUtils,
  createProcedureUtils,
  createRouterUtils,
  createRouterUtils as createTanstackQueryUtils,
  generateOperationKey
};
//# sourceMappingURL=@orpc_tanstack-query.js.map
