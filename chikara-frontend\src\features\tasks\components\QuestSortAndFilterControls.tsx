import { SlidersH<PERSON>zontal, Arrow<PERSON><PERSON>Down, X, ChevronDown } from "lucide-react";
import { useMemo } from "react";
import type { QuestFilterOption, QuestSortOption, SortDirection } from "../hooks/useQuestSortAndFilter";
import type { QuestGiver } from "@/types/quest";

interface QuestSortAndFilterControlsProps {
    sortOption: QuestSortOption;
    setSortOption: (option: QuestSortOption) => void;
    sortDirection: SortDirection;
    setSortDirection: (direction: SortDirection) => void;
    filterOptions: QuestFilterOption;
    setFilterOptions: (options: QuestFilterOption) => void;
    questGivers: QuestGiver[];
    activeTab: string;
}

export default function QuestSortAndFilterControls({
    sortOption,
    setSortOption,
    sortDirection,
    setSortDirection,
    filterOptions,
    setFilterOptions,
    questGivers,
    activeTab,
}: QuestSortAndFilterControlsProps) {
    // Track if any filters are active
    const hasActiveFilters = useMemo(() => {
        return (
            (filterOptions.status && filterOptions.status !== "all") ||
            filterOptions.minLevel !== undefined ||
            filterOptions.maxLevel !== undefined ||
            filterOptions.questGiverId !== undefined ||
            filterOptions.hasItemRewards === true ||
            filterOptions.hasTalentPointRewards === true ||
            (filterOptions.location !== undefined && filterOptions.location !== "any")
        );
    }, [filterOptions]);

    // Clear all filters
    const clearAllFilters = () => {
        setFilterOptions({
            status: "all",
        });
    };

    // Toggle sort direction
    const setAscOrDesc = () => {
        setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    };

    // Sort option labels
    const sortOptionLabels: Record<QuestSortOption, string> = {
        level: "Level",
        name: "Name",
        Yen_reward: "Yen Reward",
        XP_reward: "XP Reward",
        REP_reward: "REP Reward",
        quest_giver: "Quest Giver",
    };

    return (
        <div className="space-y-4">
            {/* Filter Controls Bar */}
            <div className="card bg-base-200 shadow-md">
                <div className="card-body p-3">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            {/* Sort Split Button */}
                            <div className="join">
                                <button className="btn btn-sm join-item gap-2" onClick={setAscOrDesc}>
                                    <ArrowUpDown className="w-4 h-4 hidden md:block" />
                                    <span>{sortOptionLabels[sortOption]}</span>
                                    <span className="badge badge-sm badge-neutral">
                                        {sortDirection === "asc" ? "↑" : "↓"}
                                    </span>
                                </button>
                                <div className="dropdown dropdown-bottom">
                                    <button className="btn btn-sm join-item px-2" tabIndex={0}>
                                        <ChevronDown className="w-3 h-3" />
                                    </button>
                                    <ul
                                        tabIndex={0}
                                        className="dropdown-content z-[1] menu p-2 shadow-lg bg-base-200 rounded-box w-52 mt-1 left-1/2 -translate-x-1/2 border border-base-300"
                                    >
                                        {Object.entries(sortOptionLabels).map(([value, label]) => (
                                            <li key={value}>
                                                <button
                                                    className={`justify-between ${sortOption === value ? "active" : ""}`}
                                                    onClick={() => {
                                                        setSortOption(value as QuestSortOption);
                                                        // Auto-close dropdown by removing focus
                                                        (document.activeElement as HTMLElement)?.blur();
                                                    }}
                                                >
                                                    <span>{label}</span>
                                                    {sortOption === value && (
                                                        <span className="text-xs opacity-70">
                                                            {sortDirection === "asc" ? "↑" : "↓"}
                                                        </span>
                                                    )}
                                                </button>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center gap-2">
                            {/* Clear All Button */}
                            {hasActiveFilters && (
                                <button className="btn btn-error btn-xs gap-1" onClick={clearAllFilters}>
                                    <X className="w-3 h-3" />
                                    Clear
                                </button>
                            )}

                            {/* Filter Dropdown */}
                            <div className="dropdown dropdown-bottom dropdown-end">
                                <button className="btn btn-sm gap-2 relative" tabIndex={0}>
                                    <SlidersHorizontal className="w-4 h-4" />
                                    Filters
                                    {hasActiveFilters && (
                                        <span className="absolute -top-1 -right-1 w-2 h-2 bg-warning rounded-full"></span>
                                    )}
                                    <ChevronDown className="w-3 h-3" />
                                </button>
                                <div
                                    tabIndex={0}
                                    className="dropdown-content z-[1] card card-compact w-full p-2 shadow-xl bg-base-200 border border-base-300 mt-1"
                                    style={{ width: "calc(100vw - 2rem)", maxWidth: "896px" }}
                                >
                                    <div className="card-body">
                                        <div className="space-y-4">
                                            {/* Status Filter */}
                                            <div className="form-control w-full">
                                                <label className="label py-1">
                                                    <span className="label-text text-sm font-medium">Status</span>
                                                </label>
                                                <select
                                                    disabled={activeTab === "complete"}
                                                    value={filterOptions.status || "all"}
                                                    className="select select-bordered select-sm w-full py-0"
                                                    onChange={(e) =>
                                                        setFilterOptions({
                                                            ...filterOptions,
                                                            status: e.target.value as
                                                                | "available"
                                                                | "in_progress"
                                                                | "complete"
                                                                | "ready_to_complete"
                                                                | "all",
                                                        })
                                                    }
                                                >
                                                    <option value="all">
                                                        {activeTab === "complete" ? "Completed" : "All Statuses"}
                                                    </option>
                                                    <option value="available">New</option>
                                                    <option value="in_progress">In Progress</option>
                                                    <option value="ready_to_complete">Ready to turn in</option>
                                                </select>
                                            </div>

                                            {/* Quest Giver Filter */}
                                            <div className="form-control w-full">
                                                <label className="label py-1">
                                                    <span className="label-text text-sm font-medium">Quest Giver</span>
                                                </label>
                                                <select
                                                    value={filterOptions.questGiverId || ""}
                                                    className="select select-bordered select-sm w-full py-0"
                                                    onChange={(e) =>
                                                        setFilterOptions({
                                                            ...filterOptions,
                                                            questGiverId: e.target.value
                                                                ? Number.parseInt(e.target.value)
                                                                : undefined,
                                                        })
                                                    }
                                                >
                                                    <option value="">All Givers</option>
                                                    {questGivers &&
                                                        questGivers
                                                            .filter((giver) => giver.shopType !== "gang")
                                                            .map((giver) => (
                                                                <option key={giver.id} value={giver.id}>
                                                                    {giver.name}
                                                                </option>
                                                            ))}
                                                </select>
                                            </div>

                                            {/* Location Filter */}
                                            <div className="form-control w-full">
                                                <label className="label py-1">
                                                    <span className="label-text text-sm font-medium">Location</span>
                                                </label>
                                                <select
                                                    value={filterOptions.location || "any"}
                                                    className="select select-bordered select-sm w-full py-0"
                                                    onChange={(e) =>
                                                        setFilterOptions({
                                                            ...filterOptions,
                                                            location: e.target.value as
                                                                | "church"
                                                                | "shrine"
                                                                | "mall"
                                                                | "alley"
                                                                | "school"
                                                                | "sewers"
                                                                | "themepark"
                                                                | "any",
                                                        })
                                                    }
                                                >
                                                    <option value="any">Any Location</option>
                                                    <option value="church">Church</option>
                                                    <option value="shrine">Shrine</option>
                                                    <option value="mall">Mall</option>
                                                    <option value="alley">Alley</option>
                                                    <option value="school">School</option>
                                                    <option value="sewers">Sewers</option>
                                                </select>
                                            </div>

                                            {/* Level Range */}
                                            <div className="form-control w-full">
                                                <label className="label py-1">
                                                    <span className="label-text text-sm font-medium">Level Range</span>
                                                    {(filterOptions.minLevel !== undefined ||
                                                        filterOptions.maxLevel !== undefined) && (
                                                        <button
                                                            className="btn btn-ghost btn-xs"
                                                            onClick={() =>
                                                                setFilterOptions({
                                                                    ...filterOptions,
                                                                    minLevel: undefined,
                                                                    maxLevel: undefined,
                                                                })
                                                            }
                                                        >
                                                            Reset
                                                        </button>
                                                    )}
                                                </label>
                                                <div className="join">
                                                    <input
                                                        type="number"
                                                        placeholder="Min"
                                                        value={filterOptions.minLevel || ""}
                                                        min="1"
                                                        className="input input-bordered input-sm join-item w-1/2"
                                                        onChange={(e) =>
                                                            setFilterOptions({
                                                                ...filterOptions,
                                                                minLevel: e.target.value
                                                                    ? Number.parseInt(e.target.value)
                                                                    : undefined,
                                                            })
                                                        }
                                                    />
                                                    <input
                                                        type="number"
                                                        placeholder="Max"
                                                        value={filterOptions.maxLevel || ""}
                                                        min="1"
                                                        className="input input-bordered input-sm join-item w-1/2"
                                                        onChange={(e) =>
                                                            setFilterOptions({
                                                                ...filterOptions,
                                                                maxLevel: e.target.value
                                                                    ? Number.parseInt(e.target.value)
                                                                    : undefined,
                                                            })
                                                        }
                                                    />
                                                </div>
                                            </div>

                                            {/* Reward Filters */}
                                            <div className="space-y-2">
                                                <span className="label-text text-sm font-medium">Reward Filters</span>
                                                <div className="space-y-2">
                                                    <div className="form-control">
                                                        <label className="label cursor-pointer py-1">
                                                            <span className="label-text text-sm">Has Item Rewards</span>
                                                            <input
                                                                type="checkbox"
                                                                checked={filterOptions.hasItemRewards === true}
                                                                className="checkbox checkbox-primary checkbox-sm"
                                                                onChange={(e) =>
                                                                    setFilterOptions({
                                                                        ...filterOptions,
                                                                        hasItemRewards: e.target.checked || undefined,
                                                                    })
                                                                }
                                                            />
                                                        </label>
                                                    </div>
                                                    <div className="form-control">
                                                        <label className="label cursor-pointer py-1">
                                                            <span className="label-text text-sm">
                                                                Has Talent Point Rewards
                                                            </span>
                                                            <input
                                                                type="checkbox"
                                                                checked={filterOptions.hasTalentPointRewards === true}
                                                                className="checkbox checkbox-primary checkbox-sm"
                                                                onChange={(e) =>
                                                                    setFilterOptions({
                                                                        ...filterOptions,
                                                                        hasTalentPointRewards:
                                                                            e.target.checked || undefined,
                                                                    })
                                                                }
                                                            />
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
