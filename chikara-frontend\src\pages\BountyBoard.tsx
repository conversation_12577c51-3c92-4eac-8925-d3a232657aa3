import { Callout } from "@/components/TestComponents/Callout";
import { UsersTable } from "@/components/UsersTable";
import { useState } from "react";
import toast from "react-hot-toast";
import Button from "@/components/Buttons/Button";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import { useActiveBounties } from "@/features/bounty/api/useActiveBounties";
import { usePlaceBounty } from "@/features/bounty/api/usePlaceBounty";
import { formatCurrency, getCurrencySymbol } from "@/utils/currencyHelpers";

function BountyBoard() {
    const { data, isLoading, error } = useActiveBounties();

    if (error) return <div>An error has occurred: {error.message}</div>;

    return (
        <div className="mb-8 pb-4 md:mx-auto md:mb-0 md:max-w-6xl md:pb-0">
            <Callout
                type="info"
                title="Here you will find students that have been placed on the bounty list."
                subtitle="You must defeat the student in a PvP battle and choose to 'Cripple' them in order to claim
                    the bounty reward!"
            />

            <PlaceBounty />
            <UsersTable data={data} isLoading={isLoading} type="bounties" />
        </div>
    );
}

export default BountyBoard;

const PlaceBounty = () => {
    const [studentID, setStudentID] = useState<string>("");
    const [amount, setAmount] = useState<string>("");
    const [reason, setReason] = useState<string>("");
    const { MIN_BOUNTY, BOUNTY_FEE } = useGameConfig();
    const { data: currentUser } = useFetchCurrentUser();

    const placeBountyMutation = usePlaceBounty(() => {
        setStudentID("");
        setAmount("");
        setReason("");
    });

    const handlePlaceBounty = (): void => {
        if (!studentID.trim()) {
            toast.error("Enter a valid Student ID");
            return;
        }
        if (!amount.trim()) {
            toast.error("Enter a reward amount");
            return;
        }
        if (!reason.trim()) {
            toast.error("Enter a bounty reason");
            return;
        }

        const amountNum = Number.parseInt(amount);
        const studentIDNum = Number.parseInt(studentID);

        if (isNaN(amountNum) || amountNum < MIN_BOUNTY) {
            toast.error(`Bounty rewards must be at least ${formatCurrency(MIN_BOUNTY)}`);
            return;
        }
        if (isNaN(studentIDNum) || studentIDNum <= 0) {
            toast.error("Enter a valid Student ID");
            return;
        }

        const bountyFee = BOUNTY_FEE + 1;
        if (amountNum * bountyFee > (currentUser?.cash || 0)) {
            toast.error(`You need more cash to place this bounty!`);
            return;
        }

        placeBountyMutation.mutate({
            amount: amountNum,
            targetId: studentIDNum,
            reason: reason,
        });
    };

    return (
        <div className="card card-border mb-6 border-2 border-error/30 bg-gradient-to-br from-error/10 to-warning/10 p-4 md:p-6">
            <h3 className="mb-4 font-display text-lg font-bold text-error md:text-xl">PLACE NEW BOUNTY</h3>

            <div className="grid gap-4 md:grid-cols-[1fr_1.5fr_2fr_auto] md:items-end">
                {/* Student ID Input */}
                <div className="form-control">
                    <label className="label pb-1" htmlFor="studentid">
                        <span className="label-text font-semibold text-xs uppercase">Student ID</span>
                        <span className="label-text-alt text-error">*</span>
                    </label>
                    <label className="input input-bordered input-sm flex items-center gap-2 border-2 focus-within:input-error md:input-md">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 16 16"
                            fill="currentColor"
                            className="size-4 opacity-70"
                        >
                            <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6ZM12.735 14c.618 0 1.093-.561.872-1.139a6.002 6.002 0 0 0-11.215 0c-.22.578.254 1.139.872 1.139h9.47Z" />
                        </svg>
                        <input
                            type="number"
                            name="studentid"
                            min={1}
                            id="studentid"
                            value={studentID}
                            className="grow bg-transparent"
                            placeholder="Enter ID"
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                setStudentID(e.target.value);
                            }}
                        />
                    </label>
                </div>

                {/* Reward Amount Input */}
                <div className="form-control">
                    <label className="label pb-1" htmlFor="amount">
                        <span className="label-text font-semibold text-xs uppercase">Reward</span>
                        <span className="label-text-alt">
                            <span className="text-error">*</span>
                            <span className="ml-1 text-warning text-sm">({BOUNTY_FEE * 100}% fee)</span>
                        </span>
                    </label>
                    <label className="input input-bordered input-sm flex items-center gap-2 border-2 focus-within:input-warning md:input-md">
                        <span className="text-warning">{getCurrencySymbol("yen")}</span>
                        <input
                            type="number"
                            name="amount"
                            id="amount"
                            value={amount}
                            min={MIN_BOUNTY}
                            className="grow bg-transparent"
                            placeholder={MIN_BOUNTY.toString()}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                setAmount(e.target.value);
                            }}
                        />
                    </label>
                </div>

                {/* Reason Input */}
                <div className="form-control md:col-span-1 flex flex-col">
                    <label className="label pb-1" htmlFor="reason">
                        <span className="label-text font-semibold text-xs uppercase">Reason</span>
                        <span className="label-text-alt text-error">*</span>
                    </label>
                    <input
                        type="text"
                        name="reason"
                        id="reason"
                        value={reason}
                        className="input input-bordered input-sm border-2 focus:input-error md:input-md"
                        placeholder="Enter bounty reason"
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            setReason(e.target.value);
                        }}
                    />
                </div>

                {/* Submit Button */}
                <button
                    className="btn btn-error btn-sm md:btn-md"
                    disabled={placeBountyMutation.isPending}
                    onClick={() => handlePlaceBounty()}
                >
                    {placeBountyMutation.isPending && <span className="loading loading-spinner loading-xs"></span>}
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="size-5">
                        <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                            clipRule="evenodd"
                        />
                    </svg>
                    Place Bounty
                </button>
            </div>

            {/* Total Cost Display */}
            {amount && !isNaN(Number(amount)) && Number(amount) >= MIN_BOUNTY && (
                <div className="mt-3 flex items-center justify-between rounded-lg bg-base-100/50 p-3">
                    <span className="text-sm font-medium">Total Cost (including fee):</span>
                    <span className="badge badge-warning badge-lg font-bold">
                        {formatCurrency(Number(amount) * (BOUNTY_FEE + 1))}
                    </span>
                </div>
            )}
        </div>
    );
};
