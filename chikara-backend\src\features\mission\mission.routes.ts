import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as MissionController from "./mission.controller.js";
import missionSchema from "./mission.validation.js";

export const missionRouter = {
    /**
     * Get list of available missions for today
     */
    getList: isLoggedInAuth.handler(async ({ context }) => {
        const response = await MissionController.missionList(context.user.id);
        return handleResponse(response);
    }),

    /**
     * Get current mission details
     */
    getCurrent: isLoggedInAuth.handler(async ({ context }) => {
        const response = await MissionController.currentMission(context.user.currentMission || null);
        return handleResponse(response);
    }),

    /**
     * Start a mission
     */
    start: canMakeStateChangesAuth.input(missionSchema.start).handler(async ({ input, context }) => {
        const response = await MissionController.startMission(context.user.id, input.id);
        return handleResponse(response);
    }),

    /**
     * Cancel current mission
     */
    cancel: isLoggedInAuth.handler(async ({ context }) => {
        const response = await MissionController.cancelMission(context.user.id);
        return handleResponse(response);
    }),
};
