import { Button } from "@/components/ui/button";
import { useGetUserProperties } from "../api/useGetUserProperties";
import { useSellProperty } from "../api/usePropertyMutations";
import { UserProperty } from "../types/property";
import { Home, DollarSign, Calendar, Settings } from "lucide-react";
import Spinner from "@/components/Spinners/Spinner";

interface UserPropertyCardProps {
    userProperty: UserProperty;
    onSell: (_propertyId: number) => void;
    isSellingProperty: boolean;
}

const UserPropertyCard = ({
    userProperty,
    onSell,
    isSellingProperty,
}: UserPropertyCardProps) => {
    const { property } = userProperty;
    const sellPrice = Math.floor(property.cost * 0.2); // 20% of original cost

    return (
        <div className="bg-slate-800/50 border border-slate-700/50 rounded-xl p-6 backdrop-blur-sm">
            <div className="flex items-start gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-500/20">
                    <Home className="h-6 w-6 text-indigo-400" />
                </div>
                <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-lg font-semibold text-white">{property.name}</h3>
                    </div>
                    <p className="text-sm text-slate-300 mb-3">{property.description}</p>

                    <div className="flex items-center gap-4 mb-4">
                        <div className="flex items-center gap-1 text-green-400">
                            <DollarSign className="h-4 w-4" />
                            <span className="font-medium">{property.cost.toLocaleString()}</span>
                        </div>
                        <div className="text-slate-400 text-sm">{property.slots} slots</div>
                        <div className="flex items-center gap-1 text-slate-400 text-sm">
                            <Calendar className="h-4 w-4" />
                            <span>Purchased {new Date(userProperty.purchaseDate).toLocaleDateString()}</span>
                        </div>
                    </div>

                    {property.buffs && Object.keys(property.buffs).length > 0 && (
                        <div className="mb-4">
                            <h4 className="text-sm font-medium text-slate-300 mb-2">Buffs:</h4>
                            <div className="flex flex-wrap gap-2">
                                {Object.entries(property.buffs).map(([buff, value]) => (
                                    <span
                                        key={buff}
                                        className="px-2 py-1 text-xs bg-blue-500/20 text-blue-400 rounded-full"
                                    >
                                        {buff}:{" "}
                                        {typeof value === "number"
                                            ? `${(((value as number) - 1) * 100).toFixed(0)}%`
                                            : String(value)}
                                    </span>
                                ))}
                            </div>
                        </div>
                    )}

                    <div className="flex gap-2">
                        <Button
                            variant="destructive"
                            size="sm"
                            disabled={isSellingProperty}
                            className="flex items-center gap-1"
                            onClick={() => onSell(property.id)}
                        >
                            <DollarSign className="h-4 w-4" />
                            Sell for ${sellPrice.toLocaleString()}
                        </Button>
                        <Button disabled variant="outline" size="sm" className="flex items-center gap-1">
                            <Settings className="h-4 w-4" />
                            Customize
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export const UserPropertyList = () => {
    const { data: userProperties, isLoading, error } = useGetUserProperties();
    const sellProperty = useSellProperty();

    const handleSell = (propertyId: number) => {
        sellProperty.mutate({ propertyId });
    };

    if (isLoading) {
        return (
            <div className="flex justify-center items-center py-8">
                <Spinner />
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-8">
                <p className="text-red-400">Failed to load your properties</p>
            </div>
        );
    }

    if (!userProperties || userProperties.length === 0) {
        return (
            <div className="text-center py-8">
                <p className="text-slate-400">You don&apos;t own any properties yet</p>
                <p className="text-slate-500 text-sm mt-1">Purchase a property to get started!</p>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <h2 className="text-xl font-semibold text-white mb-4">Your Properties</h2>
            <div className="grid gap-4">
                {userProperties.map((userProperty) => (
                    <UserPropertyCard
                        key={userProperty.id}
                        userProperty={userProperty}
                        isSellingProperty={sellProperty.isPending}
                        onSell={handleSell}
                    />
                ))}
            </div>
        </div>
    );
};
