---
type: "always_apply"
---

# Chikara Academy Frontend: Rules & Overview

## Tech Stack

- **Runtime:** Bun (Node.js compatible)
- **Language:** TypeScript
- **Framework:** React 19+ with JSX
- **Build Tool:** Vite 7+ with <PERSON>act plugin
- **Styling:** Tailwind CSS 4+ with shadcn + radix primitives
- **State Management:** Zustand with React Tracked
- **Data Fetching:** TanStack Query with oRPC client (Type-safe RPC)
- **Routing:** React Router 7
- **Real-time:** Socket.io client
- **Authentication system:** Better-Auth
- **Testing:** Vitest
- **PWA:** Vite PWA plugin with service worker
- **Code Style:** ESLint + Prettier

## Project Structure

```
chikara-frontend/
├── public/ # Static assets and PWA manifest
├── service-worker/ # Firebase messaging service worker
├── src/
│ ├── app/ # App-level configuration and providers
│ ├── assets/ # Static assets (images, fonts, sounds)
│ ├── components/ # Reusable UI components
│ ├── constants/ # App constants and configuration
│ ├── features/ # Feature modules
│ ├── helpers/ # Utility functions and helpers
│ ├── hooks/ # Custom React hooks
│ ├── lib/ # Core libraries and configurations
│ ├── pages/ # Route components
│ ├── test/ # Test utilities and setup
│ ├── types/ # TypeScript type definitions
│ ├── utils/ # Pure utility functions
│ ├── App.tsx # Main app component
│ ├── index.tsx # App entry point
│ └── SocketManager.tsx # Socket.io connection manager
└── tsconfig.json # TypeScript configuration
```

## Feature Organization

Each feature in `src/features/` follows this structure:

```
feature-name/
├── __tests__/ # Vitest unit tests
├── components/ # Feature-specific components
├── hooks/ # Feature-specific hooks
├── types/ # Feature-specific types
├── utils/ # Feature-specific utilities
├── index.ts # Feature exports
└── FeaturePage.tsx # Main feature page component
```

**Feature Rules:**

- Features should not directly import from other features
- Use `src/lib/`, `src/helpers/`, or `src/hooks/` for cross-feature functionality
- Avoid usage of barrel files

## State Management (Zustand + React Tracked)

**Store Types:**

- **persistStore:** Persistent data across sessions (localStorage)
- **sessionStore:** Session-only data (sessionStorage)
- **normalStore:** In-memory state
- **socketStore:** Real-time socket state
- **authStore:** Authentication state (persistent)

**Store Rules:**

- Use `createTrackedSelector` for automatic re-render optimization
- Prefer React Query for server state over Zustand

## API Integration (oRPC + TanStack Query)

- **API Client:** Always import and use `api` from `@/helpers/api.ts` instead of calling `orpc` directly
- **Type Safety:** End-to-end TypeScript from backend to frontend
- **Authentication:** Automatic cookie-based auth with error handling

**Query Pattern:**

```typescript
import { api } from "@/helpers/api";

const { data, isLoading, error } = useQuery(
    api.user.getUserInfo.queryOptions({
        input: { userId: 123 },
        staleTime: 30000,
    })
);

const mutation = useMutation(
    api.user.updateProfileDetails.mutationOptions({
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: api.user.getUserInfo.key(),
            });
        },
    })
);
```

### API Guidelines:

- Use `useQuery()` with `api.*.queryOptions()` for type-safe queries
- Use `useMutation()` with `api.*.mutationOptions()` for type-safe mutations
- Create separate hook files in feature/api directories for all query and mutation calls

**Query Key Invalidation:**

```typescript
import { api } from "@/helpers/api";

// Invalidate all queries for a feature
queryClient.invalidateQueries({ queryKey: api.user.key() });

// Invalidate specific query with input
queryClient.invalidateQueries({
    queryKey: api.user.getUserInfo.key({ input: { userId: 123 } }),
});

// Invalidate only regular queries (not infinite queries)
queryClient.invalidateQueries({
    queryKey: api.user.key({ type: "query" }),
});
```

## Component Architecture

**Component Types:**

- **Pages:** Route-level components in `src/pages/`
- **Features:** Feature-specific components in `src/features/`
- **UI Components:** Reusable components in `src/components/`
- **Layout Components:** App structure in `src/components/Layout/`

## Styling

**Approach:**

- **Utility-First:** Tailwind CSS for styling
- **Component Library:** Radix UI for accessible primitives
- **Custom Components:** shadcn/ui pattern for complex components
- **Dark Mode:** Dark mode only design

## Real-time Features (Socket.io)

**Socket Management:**

- **Connection:** Managed by `SocketManager.tsx`
- **State:** Stored in `socketStore`
- **Events:** Feature-specific socket handlers

## Testing

- **Unit Tests:** Vitest for component and utility testing
- Use proper test utilities from `src/test/`

## Code Style

**TypeScript Rules:**

- Prefer explicit types over `any`
- Prefer to fix the root cause of the problem over using type assertions

**Import Rules:**

- Use `@/` alias for src imports

## PWA Features

**Service Worker:**

- **Caching:** Offline-first caching strategy
- **Push Notifications:** Firebase messaging integration
- **App Updates:** Automatic app update handling

## Development Commands

```bash
bun dev              # Start development server
bun type-check       # TypeScript checking
```

## Error Handling

**Error Types:**

- **Global API Errors:** Handled by query client with global error handling
- **Component Errors:** Caught by error boundaries
- **Authentication Errors:** Automatic logout and redirect
