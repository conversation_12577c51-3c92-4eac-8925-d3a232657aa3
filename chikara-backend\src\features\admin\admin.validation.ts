import { z } from "zod";

// ===============================================
// Query Parameter Schemas
// ===============================================

export const getUserInfoSchema = z.object({
    id: z.number().int().positive("User ID must be a positive integer"),
});

export const getEquippedValuesSchema = z.object({
    id: z.number().int().positive("User ID must be a positive integer"),
});

export const getActiveUsersStatsSchema = z.object({
    startDate: z.string().datetime("Start date must be a valid datetime"),
    endDate: z.string().datetime("End date must be a valid datetime"),
});

export const getRegistrationStatsSchema = z.object({
    startDate: z.string().datetime("Start date must be a valid datetime"),
    endDate: z.string().datetime("End date must be a valid datetime"),
});

export const getGangInfoSchema = z.object({
    id: z.number().int().positive("Gang ID must be a positive integer"),
});

export const fetchIconsSchema = z.object({
    path: z.string().min(1, "Path is required"),
});

// ===============================================
// Chat Moderation Schemas
// ===============================================

export const chatBanUserSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer"),
    timeMS: z.number().int().positive("Time in milliseconds must be positive"),
});

export const removeUserChatMessagesSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer"),
});

export const hideSingleMessageSchema = z.object({
    messageId: z.number().int().positive("Message ID must be a positive integer"),
});

export const unhideSingleMessageSchema = z.object({
    messageId: z.number().int().positive("Message ID must be a positive integer"),
});

export const deleteSingleMessageSchema = z.object({
    messageId: z.number().int().positive("Message ID must be a positive integer"),
});

// ===============================================
// User Management Schemas
// ===============================================

export const banUserSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer"),
    timeMS: z.number().int().positive("Time in milliseconds must be positive"),
});

export const jailUserSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer"),
    timeMS: z.number().int().positive("Time in milliseconds must be positive"),
    jailReason: z.string().min(1, "Jail reason is required"),
});

export const bailUserSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer"),
});

export const reviveUserSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer").optional(),
});

export const profileDetailsBanSchema = z.object({
    id: z.number().int().positive("User ID must be a positive integer"),
    timeMS: z.number().int().positive("Time in milliseconds must be positive"),
});

// ===============================================
// Item Management Schemas
// ===============================================

export const giveItemSchema = z.object({
    itemName: z.string().min(1, "Item name is required"),
    amount: z.number().int().positive("Amount must be positive").optional().default(1),
    id: z.number().int().positive("User ID must be a positive integer").optional(),
    message: z.string().optional(),
});

export const removeItemSchema = z.object({
    itemName: z.string().min(1, "Item name is required"),
    amount: z.number().int().positive("Amount must be positive").optional().default(1),
    id: z.number().int().positive("User ID must be a positive integer"),
    itemId: z.number().int().positive("Item ID must be a positive integer").optional(),
});

export const bulkCreateItemsSchema = z.object({
    items: z.array(z.any()).min(1, "At least one item is required"),
});

// ===============================================
// User Stats and Values Schemas
// ===============================================

export const updateUserValuesSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer"),
    type: z.string().min(1, "Type is required"),
    value: z.number().int("Value must be an integer"),
});

export const updateUserMoneySchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer"),
    method: z.string().min(1, "Method is required"),
    type: z.string().min(1, "Type is required"),
    value: z.number().int("Value must be an integer"),
});

export const updateUserStatsSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer"),
    targetStat: z.string().min(1, "Target stat is required"),
    value: z.number().int("Value must be an integer"),
    method: z.string().min(1, "Method is required"),
});

export const updateAdminNotesSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer"),
    notes: z.string(),
});

// ===============================================
// Roguelike Management Schemas
// ===============================================

export const resetUserRoguelikeDataSchema = z.object({
    id: z.number().int().positive("User ID must be a positive integer"),
});

export const updateUserRoguelikeDataSchema = z.object({
    id: z.number().int().positive("User ID must be a positive integer"),
    mapdata: z.any(), // Roguelike data can be complex
    level: z.number().int().positive("Level must be positive"),
});

export const updateUserRoguelikeBuffsSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer"),
    strBuff: z.number({ invalid_type_error: "Strength buff must be a number" }),
    defBuff: z.number({ invalid_type_error: "Defense buff must be a number" }),
});

// ===============================================
// Account Management Schemas
// ===============================================

export const updateUserAvatarSchema = z.object({
    id: z.number().int().positive("User ID must be a positive integer"),
    avatar: z.string().min(1, "Avatar is required"),
});

export const updateAccountDetailsSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer"),
    password: z.string().optional(),
    username: z.string().optional(),
    email: z.string().email().optional(),
});

// ===============================================
// System Management Schemas
// ===============================================

export const toggleMaintenanceSchema = z.object({
    enabled: z.boolean({ invalid_type_error: "Enabled must be a boolean" }),
});

export const sendAnnouncementMessageSchema = z.object({
    message: z.string().min(1, "Message is required"),
});

export const sendPatchNotesNotifySchema = z.object({
    id: z.number().int().positive("ID must be a positive integer"),
});

export const manualGangPayoutSchema = z.object({
    gangId: z.number().int().positive("Gang ID must be a positive integer"),
    amount: z.number().int().positive("Amount must be positive"),
});

export const createAuctionListingSchema = z.object({
    itemId: z.number().int().positive("Item ID must be a positive integer"),
});

export const sendTestPushNotificationSchema = z.object({
    message: z.string().min(1, "Message is required"),
    userId: z.number().int().positive("User ID must be a positive integer").optional(),
});

export const updateConfigValueSchema = z.object({
    key: z.string().min(1, "Config key is required"),
    value: z.any().refine((val) => val !== undefined && val !== null, "Config value cannot be undefined or null"),
    category: z.string().optional(),
    isPublic: z.boolean().optional().default(true),
});

// ===============================================
// Export all schemas
// ===============================================

const adminSchema = {
    getUserInfo: getUserInfoSchema,
    getEquippedValues: getEquippedValuesSchema,
    getActiveUsersStats: getActiveUsersStatsSchema,
    getRegistrationStats: getRegistrationStatsSchema,
    getGangInfo: getGangInfoSchema,
    fetchIcons: fetchIconsSchema,
    chatBanUser: chatBanUserSchema,
    removeUserChatMessages: removeUserChatMessagesSchema,
    hideSingleMessage: hideSingleMessageSchema,
    unhideSingleMessage: unhideSingleMessageSchema,
    deleteSingleMessage: deleteSingleMessageSchema,
    banUser: banUserSchema,
    jailUser: jailUserSchema,
    bailUser: bailUserSchema,
    reviveUser: reviveUserSchema,
    profileDetailsBan: profileDetailsBanSchema,
    giveItem: giveItemSchema,
    removeItem: removeItemSchema,
    bulkCreateItems: bulkCreateItemsSchema,
    updateUserValues: updateUserValuesSchema,
    updateUserMoney: updateUserMoneySchema,
    updateUserStats: updateUserStatsSchema,
    updateAdminNotes: updateAdminNotesSchema,
    resetUserRoguelikeData: resetUserRoguelikeDataSchema,
    updateUserRoguelikeData: updateUserRoguelikeDataSchema,
    updateUserRoguelikeBuffs: updateUserRoguelikeBuffsSchema,
    updateUserAvatar: updateUserAvatarSchema,
    updateAccountDetails: updateAccountDetailsSchema,
    toggleMaintenance: toggleMaintenanceSchema,
    sendAnnouncementMessage: sendAnnouncementMessageSchema,
    sendPatchNotesNotify: sendPatchNotesNotifySchema,
    manualGangPayout: manualGangPayoutSchema,
    createAuctionListing: createAuctionListingSchema,
    sendTestPushNotification: sendTestPushNotificationSchema,
    updateConfigValue: updateConfigValueSchema,
};

export default adminSchema;
