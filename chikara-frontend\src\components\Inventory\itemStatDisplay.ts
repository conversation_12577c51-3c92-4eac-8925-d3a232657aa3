import type { Item, ItemEffect } from "@/types/item";

interface MetabolismTalent {
    modifier: number;
}

interface StatModifiers {
    defence?: number;
    strength?: number;
    dexterity?: number;
    endurance?: number;
    lifesteal?: number;
    health?: number;
}

const addUpgradeModifiers = (value: number, upgradeLevel: number): number => {
    const multiplierThreshold = 3.0;

    const modifyValue = (val: number): number => {
        const upgradeLevelModifier = 1 + upgradeLevel * 0.05;
        return parseFloat((val * upgradeLevelModifier).toFixed(0));
    };

    const modifyMultiplier = (multiplier: number): number => {
        const basePercentage = multiplier - 1;
        const upgradeLevelModifier = upgradeLevel * 0.05;
        const newPercentage = basePercentage + basePercentage * upgradeLevelModifier;
        return parseFloat((1 + newPercentage).toFixed(2));
    };

    if (upgradeLevel && upgradeLevel > 0) {
        return value < multiplierThreshold ? modifyMultiplier(value) : modifyValue(value);
    }
    return value;
};

const formatStat = (
    value: number,
    label: string,
    upgradeLevel: number,
    hidePlus: boolean,
    showDiff: boolean,
    isModifier?: boolean
): string | null => {
    const modifiedValue = Math.round(addUpgradeModifiers(value, upgradeLevel));
    if (showDiff) {
        const prevValue = Math.round(addUpgradeModifiers(value, upgradeLevel - 1));
        const diff = parseFloat(Math.abs(modifiedValue - prevValue).toFixed(2));

        if (!diff || diff <= 0) return null;
        return `+${diff}${isModifier ? "%" : ""}`;
    }
    return `${modifiedValue > 0 && !hidePlus ? "+" : ""}${modifiedValue} ${label}`;
};

const addStatIfValid = (
    arr: (string | null)[],
    value: number | null | undefined,
    upgradeLevel: number | null,
    label: string,
    hidePlus: boolean,
    showDiff?: boolean,
    isModifier?: boolean
): void => {
    if (value && value !== 0 && upgradeLevel !== null) {
        arr.push(formatStat(value, label, upgradeLevel, hidePlus, showDiff || false, isModifier));
    }
};

export const itemStatDisplay = (
    item: Item | null,
    metabolismTalent: MetabolismTalent | null,
    upgradeLevel: number,
    hidePlus = false,
    showDiff = false
): (string | null)[] => {
    if (!item) return [];
    const arr: (string | null)[] = [];
    const { itemType, armour, itemEffects, energy, actionPoints, health, damage } = item;

    // Extract stat modifiers from itemEffects
    const statModifierEffects = (itemEffects || []).filter(
        (effect: ItemEffect) => effect.effectGroup === "statModifiers"
    );
    const parsedModifiers: StatModifiers = {};

    // Convert itemEffects to the expected StatModifiers format
    statModifierEffects.forEach((effect: ItemEffect) => {
        if (effect.effectKey && effect.effectValue !== undefined) {
            const key = effect.effectKey as keyof StatModifiers;
            parsedModifiers[key] = effect.effectValue;
        }
    });

    const statLabels: Record<keyof StatModifiers, string> = {
        defence: "% DEF",
        strength: "% STR",
        dexterity: "% DEX",
        endurance: "% END",
        lifesteal: "% LIFE STEAL",
        health: " HP",
    };

    // Handle weapon stats
    if (["weapon", "ranged", "offhand"].includes(itemType)) {
        addStatIfValid(arr, damage, upgradeLevel, "DMG", hidePlus, showDiff);
    }

    // Handle armour stats
    addStatIfValid(arr, armour, upgradeLevel, "ARMOR", hidePlus, showDiff);

    // Handle stat modifiers
    if (Object.keys(parsedModifiers).length > 0) {
        for (const [key, label] of Object.entries(statLabels)) {
            const modifierKey = key as keyof StatModifiers;
            if (modifierKey in parsedModifiers) {
                const modifierValue = parsedModifiers[modifierKey];
                if (modifierValue !== undefined) {
                    const value = key === "health" && itemType === "finger" ? modifierValue : (modifierValue - 1) * 100;
                    addStatIfValid(arr, value, upgradeLevel, label, hidePlus, showDiff, true);
                }
            }
        }
    }

    // Handle consumable stats
    if (itemType === "consumable") {
        addStatIfValid(arr, energy, null, "ENERGY", hidePlus);
        addStatIfValid(arr, actionPoints, null, "AP", hidePlus);

        if (health && health !== 0) {
            const baseHealth = metabolismTalent ? Math.round(health * metabolismTalent.modifier) : health;
            addStatIfValid(arr, baseHealth, null, "HP", hidePlus);
        }
    }
    return arr;
};
