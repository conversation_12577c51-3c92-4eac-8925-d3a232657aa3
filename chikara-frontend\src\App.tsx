import { sendTokenToServer } from "@/app/firebase";
import { useAuthStore, useNormalStore, usePersistStore } from "@/app/store/stores";
import ErrorBoundary from "@/components/Layout/ErrorBoundary";
import MaintenanceMode from "@/components/MaintenanceMode";
import LoadingSpinner from "@/components/Spinners/Spinner";
import ToastManager from "@/components/ToastManager";
import Tooltips from "@/components/Tooltips";
import { useNotificationHandler } from "@/hooks/useNotificationHandler";
import { useFetchGameConfig } from "@/hooks/api/useFetchGameConfig";
import { api } from "@/helpers/api";
import {
    AllCommunityModule,
    ClientSideRowModelModule,
    ModuleRegistry,
    provideGlobalGridOptions,
} from "ag-grid-community";
import posthog from "posthog-js";
import { Suspense, useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";
import SocketManager from "./SocketManager";

// ---------AG GRID---------
ModuleRegistry.registerModules([ClientSideRowModelModule, AllCommunityModule]);
provideGlobalGridOptions({ theme: "legacy" });
// ----------------------------

function App() {
    const location = useLocation();
    const { refreshPagePrompt, setRefreshPagePrompt, isInMaintenance } = useNormalStore();
    const queryClient = useQueryClient();

    const { messagingToken } = usePersistStore();
    const authed = useAuthStore((state) => state.authed);

    // Use game config hook with local storage as initial data
    useFetchGameConfig();

    // Force refetch game config on app load
    useEffect(() => {
        queryClient.invalidateQueries({
            queryKey: api.user.getGameConfig.key(),
        });
    }, [queryClient]);

    // Handle socket notifications
    useNotificationHandler();

    useEffect(() => {
        posthog.capture("$pageview");
    }, [location.pathname]);

    useEffect(() => {
        if (refreshPagePrompt) {
            setRefreshPagePrompt(false);
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if (authed && messagingToken) {
            sendTokenToServer(messagingToken);
        }
    }, [authed, messagingToken]);

    // If the app is in maintenance mode, show the maintenance component
    if (isInMaintenance) {
        return <MaintenanceMode />;
    }
    return (
        <>
            <SocketManager />
            <ErrorBoundary>
                <Suspense
                    fallback={
                        <div className="size-full flex items-center justify-center">
                            <LoadingSpinner />
                        </div>
                    }
                >
                    <Outlet />
                </Suspense>
            </ErrorBoundary>
            <ToastManager />
            <Tooltips />
        </>
    );
}

export default App;
