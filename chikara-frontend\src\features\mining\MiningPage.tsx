import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Clock, Target, Play, RotateCcw, Star } from "lucide-react";
import { cn } from "@/lib/utils";
import { Difficulty, GameState } from "@/features/mining/types/mining";
import { useMiningSession, useStartMining, useProcessSwing, useCancelMining } from "@/features/mining/api";
import useGetUserSkills from "@/hooks/api/useGetUserSkills";

interface DifficultyConfig {
    name: string;
    energyCost: number;
    maxHits: number;
    speed: number;
    perfectZoneSize: number;
    goodZoneSize: number;
    oreMultiplier: number;
    rareChance: number;
    requiredLevel: number;
}

const difficulties: Record<Difficulty, DifficultyConfig> = {
    easy: {
        name: "Shallow Mine",
        energyCost: 2,
        maxHits: 8,
        speed: 1,
        perfectZoneSize: 12,
        goodZoneSize: 35,
        oreMultiplier: 1,
        rareChance: 0.1,
        requiredLevel: 1,
    },
    medium: {
        name: "Deep Mine",
        energyCost: 4,
        maxHits: 10,
        speed: 1.3,
        perfectZoneSize: 10,
        goodZoneSize: 28,
        oreMultiplier: 1.5,
        rareChance: 0.2,
        requiredLevel: 15,
    },
    hard: {
        name: "Crystal Cavern",
        energyCost: 6,
        maxHits: 12,
        speed: 1.6,
        perfectZoneSize: 8,
        goodZoneSize: 24,
        oreMultiplier: 2,
        rareChance: 0.35,
        requiredLevel: 30,
    },
    expert: {
        name: "Mythril Depths",
        energyCost: 8,
        maxHits: 15,
        speed: 2,
        perfectZoneSize: 5,
        goodZoneSize: 15,
        oreMultiplier: 3,
        rareChance: 0.5,
        requiredLevel: 45,
    },
};

// Reusable Components
interface StatCardProps {
    icon: React.ComponentType<{ className?: string }>;
    value: string | number;
    iconColor?: string;
}

const StatCard = ({ icon: Icon, value, iconColor = "text-gray-400" }: StatCardProps) => (
    <div className="bg-gray-800/50 rounded-lg p-2 text-center border border-purple-900/30">
        <Icon className={cn("size-4 mx-auto mb-1", iconColor)} />
        <div className="text-white text-sm font-medium">{value}</div>
    </div>
);

interface IconTextProps {
    icon: React.ComponentType<{ className?: string }>;
    text: string | number;
    iconColor?: string;
    textColor?: string;
}

const IconText = ({ icon: Icon, text, iconColor = "text-gray-400", textColor = "text-gray-300" }: IconTextProps) => (
    <div className="flex items-center gap-1">
        <Icon className={cn("size-3", iconColor)} />
        <span className={cn("text-xs", textColor)}>{text}</span>
    </div>
);

interface ActionButtonProps {
    onClick: () => void;
    disabled?: boolean;
    variant?: "primary" | "secondary";
    children: React.ReactNode;
    fullWidth?: boolean;
}

const ActionButton = ({
    onClick,
    disabled = false,
    variant = "primary",
    children,
    fullWidth = false,
}: ActionButtonProps) => {
    const baseStyles = "py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2";
    const widthStyles = fullWidth ? "w-full" : "";

    const variantStyles = {
        primary: disabled
            ? "bg-gray-700 text-gray-400 cursor-not-allowed"
            : "bg-purple-600 hover:bg-purple-700 text-white",
        secondary: "bg-gray-700 hover:bg-gray-600 text-white",
    };

    return (
        <button disabled={disabled} className={cn(baseStyles, widthStyles, variantStyles[variant])} onClick={onClick}>
            {children}
        </button>
    );
};

interface StarPopupProps {
    show: boolean;
    position: { x: number; y: number };
}

const StarPopup = ({ show, position }: StarPopupProps) => {
    if (!show) return null;

    return (
        <div
            className={cn("absolute pointer-events-none", show ? "opacity-100" : "opacity-0")}
            style={{
                left: `${position.x}%`,
                top: `${position.y}%`,
                transform: `translate(-50%, -50%)`,
                animation: show ? "starBurst 1.2s ease-out forwards" : "none",
                zIndex: 9999,
            }}
        >
            {/* Main star with burst animation */}
            <div className="relative">
                <Star className="size-8 text-yellow-400 fill-yellow-400 drop-shadow-lg" />

                {/* Pulsing background effect */}
                <div className="absolute inset-0 animate-ping opacity-75">
                    <Star className="size-8 text-yellow-300 fill-yellow-300" />
                </div>

                {/* Additional sparkle effects */}
                <div className="absolute -top-1 -right-1 animate-bounce delay-150">
                    <Star className="size-3 text-white fill-white opacity-80" />
                </div>
                <div className="absolute -bottom-1 -left-1 animate-bounce delay-300">
                    <Star className="size-2 text-yellow-200 fill-yellow-200 opacity-60" />
                </div>
            </div>

            {/* Perfect Hit text */}
            <div
                className="absolute top-full left-1/2 -translate-x-1/2 mt-2"
                style={{
                    animation: show ? "fadeInUp 0.6s ease-out 0.2s both" : "none",
                }}
            >
                {/* <span className="text-xs font-bold text-yellow-400 bg-gray-900/90 px-2 py-1 rounded-full shadow-lg whitespace-nowrap border border-yellow-500/30">
                    Perfect Hit!
                </span> */}
            </div>
        </div>
    );
};

export default function MiningPanel() {
    const [selectedDifficulty, setSelectedDifficulty] = useState<Difficulty>("easy");
    const [gameState, setGameState] = useState<GameState>("menu");

    // Minigame mechanics
    const [indicatorPosition, setIndicatorPosition] = useState(0);
    const [targetZone, setTargetZone] = useState(50);
    const [isProcessingMiss, setIsProcessingMiss] = useState(false);

    // Star popup animation state
    const [showStarPopup, setShowStarPopup] = useState(false);
    const [starPosition, setStarPosition] = useState({ x: 50, y: 50 });

    // Get user skills including mining level
    const { data: userSkills, isLoading: skillsLoading } = useGetUserSkills(["mining"]);
    const playerMiningLevel = userSkills?.mining?.level || 1;

    // API hooks
    const {
        data: miningSession,
        isLoading: sessionLoading,
        refetch: refetchSession,
    } = useMiningSession({
        retry: false,
        refetchOnWindowFocus: false,
    });

    const startMiningMutation = useStartMining({
        onSuccess: (data) => {
            setGameState("playing");
            setIndicatorPosition(0);
            setTargetZone(Math.random() * 60 + 20); // Initial target position
            setIsProcessingMiss(false);
        },
        onError: (error) => {
            console.error("Failed to start mining:", error);
            // Could add toast notification here
        },
    });

    const processSwingMutation = useProcessSwing({
        onSuccess: (data) => {
            // Handle hit quality for visual feedback
            if (data.hitQuality === "perfect") {
                setStarPosition({ x: indicatorPosition, y: 30 });
                setShowStarPopup(true);
            }

            // Set next target position from server
            setTargetZone(data.nextTargetPosition);
            setIndicatorPosition(0);

            // Check if session is complete
            if (data.sessionComplete) {
                setGameState("completed");
            }
        },
        onError: (error) => {
            console.error("Failed to process swing:", error);
            // Could add toast notification here
        },
    });

    const cancelMiningMutation = useCancelMining({
        onSuccess: () => {
            setGameState("menu");
            setIndicatorPosition(0);
        },
        onError: (error) => {
            console.error("Failed to cancel mining:", error);
            // Could add toast notification here
        },
    });

    // Check for existing session on mount
    useEffect(() => {
        if (miningSession && !sessionLoading) {
            setGameState("playing");
            setIndicatorPosition(0);
            setTargetZone(Math.random() * 60 + 20);
        }
    }, [miningSession, sessionLoading]);

    // Get current difficulty config based on session or selected
    const getCurrentDifficultyConfig = () => {
        if (miningSession) {
            // Map server difficulty names back to our keys
            const difficultyMap: Record<string, Difficulty> = {
                "Shallow Mine": "easy",
                "Deep Mine": "medium",
                "Crystal Cavern": "hard",
                "Mythril Depths": "expert",
            };
            const mappedDifficulty = difficultyMap[miningSession.difficulty] || "easy";
            return difficulties[mappedDifficulty];
        }
        return difficulties[selectedDifficulty];
    };

    const currentDifficulty = getCurrentDifficultyConfig();

    // Helper functions
    const getMiningLevelRequired = (difficulty: Difficulty) => {
        return difficulties[difficulty].requiredLevel;
    };

    const handleAutoMiss = useCallback(() => {
        if (gameState !== "playing" || !miningSession || isProcessingMiss || processSwingMutation.isPending) return;

        setIsProcessingMiss(true);

        // Process auto-miss with server
        processSwingMutation.mutate({
            hitPosition: 100, // Position outside any zone
            targetPosition: targetZone,
        });

        // Reset the processing flag after server response
        setTimeout(() => setIsProcessingMiss(false), 100);
    }, [gameState, miningSession, isProcessingMiss, processSwingMutation, targetZone]);

    // Indicator movement
    useEffect(() => {
        let interval: NodeJS.Timeout;
        if (gameState === "playing" && !isProcessingMiss && miningSession) {
            interval = setInterval(() => {
                setIndicatorPosition((prev) => {
                    const newPos = prev + miningSession.speed;
                    // If indicator reaches the end, trigger auto-miss
                    if (newPos >= 100) {
                        handleAutoMiss();
                        return 0; // Reset immediately
                    }
                    return newPos;
                });
            }, 50);
        }
        return () => clearInterval(interval);
    }, [gameState, miningSession, handleAutoMiss, isProcessingMiss]);

    const startMining = useCallback(() => {
        startMiningMutation.mutate({ difficulty: selectedDifficulty });
    }, [selectedDifficulty, startMiningMutation]);

    const mine = useCallback(() => {
        if (gameState !== "playing" || !miningSession || isProcessingMiss || processSwingMutation.isPending) return;

        processSwingMutation.mutate({
            hitPosition: indicatorPosition,
            targetPosition: targetZone,
        });
    }, [gameState, miningSession, indicatorPosition, targetZone, isProcessingMiss, processSwingMutation]);

    // Auto-hide star popup after animation
    useEffect(() => {
        if (showStarPopup) {
            const timer = setTimeout(() => {
                setShowStarPopup(false);
            }, 1200);
            return () => clearTimeout(timer);
        }
    }, [showStarPopup]);

    const resetGame = () => {
        if (miningSession) {
            cancelMiningMutation.mutate({});
        } else {
            setGameState("menu");
        }
    };

    // Add CSS animation styles
    useEffect(() => {
        const style = document.createElement("style");
        style.textContent = `
            @keyframes starBurst {
                0% {
                    transform: translate(-50%, -50%) scale(0.3);
                    opacity: 0;
                }
                30% {
                    transform: translate(-50%, -50%) scale(1.4);
                    opacity: 1;
                }
                70% {
                    transform: translate(-50%, -50%) scale(1.1);
                    opacity: 1;
                }
                100% {
                    transform: translate(-50%, -50%) scale(0.8);
                    opacity: 0;
                }
            }

            @keyframes fadeInUp {
                0% {
                    opacity: 0;
                    transform: translateY(20px);
                }
                100% {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
        return () => {
            document.head.removeChild(style);
        };
    }, []);

    // Render States
    const renderMenuState = () => (
        <div className="space-y-4">
            {/* Player Stats */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <IconText icon={Pickaxe} text={`Mining Lvl ${playerMiningLevel}`} />
                </div>
            </div>

            {/* Difficulty Selection */}
            <div className="space-y-3">
                <h3 className="text-white font-semibold">Select Mining Location</h3>

                {Object.entries(difficulties).map(([key, config]) => {
                    const difficulty = key as Difficulty;
                    const levelRequired = getMiningLevelRequired(difficulty);
                    const levelMet = playerMiningLevel >= levelRequired;

                    return (
                        <button
                            key={difficulty}
                            disabled={!levelMet}
                            className={cn(
                                "w-full p-3 rounded-lg border transition-colors text-left",
                                selectedDifficulty === difficulty
                                    ? "bg-purple-900/30 border-purple-500"
                                    : levelMet
                                      ? "bg-gray-800/50 border-gray-700 hover:border-gray-600"
                                      : "bg-gray-800/30 border-gray-800 opacity-50 cursor-not-allowed"
                            )}
                            onClick={() => setSelectedDifficulty(difficulty)}
                        >
                            <div className="flex items-center justify-between mb-1">
                                <h4 className="text-white font-medium">{config.name}</h4>
                                <div className="flex items-center gap-2">
                                    <IconText icon={Clock} text={`${config.maxHits} Hits`} />
                                </div>
                            </div>

                            <div className="flex items-center justify-between text-xs">
                                <span className="text-gray-400">
                                    Ore Multiplier: {config.oreMultiplier}x • Rare Chance:{" "}
                                    {Math.round(config.rareChance * 100)}%
                                </span>
                                {!levelMet && (
                                    <span className="text-red-400">Mining Level {levelRequired} required</span>
                                )}
                            </div>
                        </button>
                    );
                })}
            </div>

            {/* Start Button */}
            <ActionButton fullWidth disabled={startMiningMutation.isPending} onClick={startMining}>
                <Play className="size-5" />
                {startMiningMutation.isPending ? "Starting..." : "Start Mining"}
            </ActionButton>

            {/* Mining Tips */}
            <div className="bg-gray-800/50 rounded-lg p-3 border border-purple-900/30">
                <h4 className="text-white font-medium mb-2">Mining Tips</h4>
                <ul className="text-sm text-gray-300 space-y-1">
                    <li>• Time your clicks when the indicator is in the target zone</li>
                    <li>• Perfect hits give bonus ore and build your streak</li>
                    <li>• Higher streaks increase rare ore chances</li>
                    <li>• Harder difficulties offer better rewards but cost more energy</li>
                </ul>
            </div>
        </div>
    );

    const renderPlayingState = () => {
        if (!miningSession) return null;

        return (
            <div className="space-y-4">
                {/* Game Stats */}
                <div className="grid grid-cols-4 gap-2">
                    <StatCard
                        icon={Pickaxe}
                        value={`${currentDifficulty.maxHits - miningSession.swingsRemaining}/${currentDifficulty.maxHits}`}
                    />
                    <StatCard icon={Target} value={miningSession.streak} iconColor="text-purple-400" />
                    <StatCard icon={Trophy} value={miningSession.experienceGained} iconColor="text-blue-400" />
                    <StatCard icon={Star} value={miningSession.maxStreak} iconColor="text-yellow-400" />
                </div>

                {/* Mining Game */}
                <div className="bg-gray-800/50 rounded-lg p-4 border border-purple-900/30">
                    <div className="relative h-20 bg-gray-900 rounded-lg mb-4">
                        {/* Target Zone */}
                        <div
                            className="absolute top-0 h-full bg-green-500/30 border-x-2 border-green-500"
                            style={{
                                left: `${targetZone - miningSession.goodZoneSize / 2}%`,
                                width: `${miningSession.goodZoneSize}%`,
                            }}
                        />
                        <div
                            className="absolute top-0 h-full bg-green-500/50 border-x-2 border-green-400"
                            style={{
                                left: `${targetZone - miningSession.perfectZoneSize / 2}%`,
                                width: `${miningSession.perfectZoneSize}%`,
                            }}
                        />

                        {/* Indicator */}
                        <div
                            className="absolute top-0 w-1 h-full bg-white shadow-lg transition-all duration-75"
                            style={{ left: `${indicatorPosition}%` }}
                        />

                        {/* Star Popup */}
                        <StarPopup show={showStarPopup} position={starPosition} />
                    </div>

                    <ActionButton
                        fullWidth
                        disabled={miningSession.swingsRemaining <= 0 || processSwingMutation.isPending}
                        onClick={mine}
                    >
                        <Pickaxe className="size-5" />
                        {processSwingMutation.isPending ? "Mining..." : "Mine"}
                    </ActionButton>
                </div>

                {/* Ore Collection Display */}
                {miningSession.oreCollected.length > 0 && (
                    <div className="bg-gray-800/50 rounded-lg p-3 border border-purple-900/30">
                        <h4 className="text-white font-medium mb-2">Ores Collected</h4>
                        <div className="space-y-1">
                            {miningSession.oreCollected.map((ore, index) => (
                                <div key={index} className="flex items-center justify-between text-sm">
                                    <span className="text-gray-300">
                                        Item {ore.itemId} x{ore.quantity}
                                    </span>
                                    <span
                                        className={cn(
                                            "font-medium",
                                            ore.hitQuality === "perfect"
                                                ? "text-green-400"
                                                : ore.hitQuality === "good"
                                                  ? "text-yellow-400"
                                                  : "text-gray-400"
                                        )}
                                    >
                                        {ore.hitQuality}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* Cancel Button */}
                <ActionButton
                    fullWidth
                    variant="secondary"
                    disabled={cancelMiningMutation.isPending}
                    onClick={resetGame}
                >
                    <RotateCcw className="size-4" />
                    {cancelMiningMutation.isPending ? "Cancelling..." : "Cancel Mining"}
                </ActionButton>
            </div>
        );
    };

    const renderCompletedState = () => {
        if (!miningSession) return null;

        return (
            <div className="space-y-4">
                {/* Results Summary */}
                <div className="bg-gray-800/50 rounded-lg p-4 border border-purple-900/30 text-center">
                    <Trophy className="size-12 text-yellow-400 mx-auto mb-3" />
                    <h3 className="text-white text-xl font-bold mb-2">Mining Session Complete!</h3>

                    <div className="grid grid-cols-3 gap-4 mb-4">
                        <div>
                            <div className="text-2xl font-bold text-blue-400">{miningSession.experienceGained}</div>
                            <div className="text-sm text-gray-400">Mining EXP</div>
                        </div>
                        <div>
                            <div className="text-2xl font-bold text-purple-400">{miningSession.maxStreak}</div>
                            <div className="text-sm text-gray-400">Best Streak</div>
                        </div>
                        <div>
                            <div className="text-2xl font-bold text-green-400">{miningSession.oreCollected.length}</div>
                            <div className="text-sm text-gray-400">Ores Found</div>
                        </div>
                    </div>

                    {/* Ore Collection Summary */}
                    {miningSession.oreCollected.length > 0 && (
                        <div className="bg-gray-900/50 rounded-lg p-3 mb-4">
                            <h4 className="text-white font-medium mb-2">Final Haul</h4>
                            <div className="space-y-1">
                                {miningSession.oreCollected.map((ore, index) => (
                                    <div key={index} className="flex items-center justify-between text-sm">
                                        <span className="text-gray-300">
                                            Item {ore.itemId} x{ore.quantity}
                                        </span>
                                        <span
                                            className={cn(
                                                "font-medium",
                                                ore.hitQuality === "perfect"
                                                    ? "text-green-400"
                                                    : ore.hitQuality === "good"
                                                      ? "text-yellow-400"
                                                      : "text-gray-400"
                                            )}
                                        >
                                            {ore.hitQuality}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                {/* Action Buttons */}
                <div className="grid grid-cols-1 gap-3">
                    <ActionButton variant="secondary" onClick={resetGame}>
                        <RotateCcw className="size-4" />
                        Back to Menu
                    </ActionButton>
                </div>
            </div>
        );
    };

    // Show loading state
    if (sessionLoading || skillsLoading) {
        return (
            <div className="space-y-4">
                <div className="bg-gray-800/50 rounded-lg p-4 border border-purple-900/30 text-center">
                    <div className="text-white">Loading mining data...</div>
                </div>
            </div>
        );
    }

    // Main render logic
    if (gameState === "menu") return renderMenuState();
    if (gameState === "playing") return renderPlayingState();
    if (gameState === "completed") return renderCompletedState();
    return null;
}
