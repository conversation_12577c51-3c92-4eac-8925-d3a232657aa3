import Button from "@/components/Buttons/Button";
import { DisplayItem } from "@/components/DisplayItem";
import { Progress } from "@/components/ui/progress";
import { getNextMidnightDate, formatTimeToNow } from "@/helpers/dateHelpers";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import { useMissionList, type Mission } from "@/features/missions/api/useMissionList";
import { useCurrentMission } from "@/features/missions/api/useCurrentMission";
import { useStartMission } from "@/features/missions/api/useStartMission";
import { useCancelMission } from "@/features/missions/api/useCancelMission";
import clsx from "clsx";
import { useState } from "react";
import { CountdownTimer } from "../components/Layout/CountdownTimer";
import { Callout } from "../components/TestComponents/Callout";
import type { User } from "@/types/user";
import { formatCurrency } from "@/utils/currencyHelpers";
import { Target } from "lucide-react";

interface Tab {
    id: number;
    name: string;
    current: boolean;
}

const convertToHours = (milliseconds: number): number => {
    const hours = Math.floor(milliseconds / (60 * 60 * 1000));
    return hours;
};

function Missions() {
    const { isLoading, data } = useMissionList();
    const { data: currentUser } = useFetchCurrentUser();
    const { data: currentMission } = useCurrentMission(!!currentUser?.currentMission);
    const [selectedTier, setSelectedTier] = useState<number>(1);

    const { MISSION_TIER_REQ_LEVELS, MISSION_TIER_REQ_HOURS } = useGameConfig();

    const currentTab = (tabname: number): boolean => {
        if (selectedTier === tabname) {
            return true;
        } else {
            return false;
        }
    };

    const startMission = useStartMission();
    const cancelMission = useCancelMission();

    const handleStartMission = (missionId: number): void => {
        startMission.mutate({ id: missionId });
    };

    const handleCancelMission = (): void => {
        cancelMission.mutate({});
    };

    const getProgressBarValue = (reqHours: number, totalHours: number): number => {
        const percentage = (totalHours / reqHours) * 100;
        return percentage;
    };

    const currentTierReqHours = MISSION_TIER_REQ_HOURS[selectedTier - 1];
    const currentTierReqLevel = MISSION_TIER_REQ_LEVELS[selectedTier - 1];

    const selectedMissions = data?.filter((m: Mission) => m.tier === selectedTier) || [];

    const midnightDate = getNextMidnightDate();

    const totalMissionHours = currentUser?.user_achievements?.totalMissionHours || 0;

    if (isLoading) return null;

    return (
        <div className="flex flex-col px-4 text-shadow sm:px-6 md:mx-auto md:max-w-6xl lg:px-8">
            <Callout
                type="info"
                title="You cannot access any other gameplay activities whilst you're on a mission."
            ></Callout>

            <div className="mb-0.5 ml-auto flex gap-1 text-xs">
                <p className="text-center text-gray-400">New missions in:</p>
                <div data-testid="countdown-timer" className="text-custom-yellow">
                    <CountdownTimer showHours targetDate={midnightDate} showSeconds={false} />
                </div>
            </div>

            {currentUser?.currentMission && currentMission ? (
                <div className="mb-4 sm:flex sm:items-center">
                    <div className="mx-auto mt-3 flex w-fit flex-col rounded-lg border-2 border-indigo-600 bg-gray-800 px-10 py-2.5 lg:block lg:px-14">
                        <div className="flex justify-center gap-7">
                            <div className="my-auto flex flex-col">
                                <p className="text-center text-[0.6rem] text-stroke-sm uppercase dark:text-slate-200">
                                    {" "}
                                    Current Mission{" "}
                                </p>
                                <h1 className="text-center font-normal text-gray-900 text-stroke-sm text-xl leading-6 lg:text-2xl dark:text-slate-400">
                                    <span className="text-custom-yellow">{currentMission?.missionName}</span>
                                </h1>
                            </div>
                            <div className="my-auto flex flex-col gap-1">
                                <p className="text-center text-[0.6rem] text-stroke-sm uppercase dark:text-slate-200">
                                    {" "}
                                    Rewards{" "}
                                </p>

                                <div className="text-center font-normal text-green-400 text-stroke-sm leading-6 lg:text-xl">
                                    {currentMission.minCashReward > 0 && (
                                        <p className="text-green-500">
                                            {formatCurrency(currentMission.minCashReward)} ~{" "}
                                            {formatCurrency(currentMission.maxCashReward)}
                                        </p>
                                    )}
                                    {currentMission.minExpReward > 0 && (
                                        <p className="text-blue-500">
                                            {currentMission.minExpReward} EXP ~ {"  "}
                                            {currentMission.maxExpReward} EXP
                                        </p>
                                    )}
                                    {currentMission.item && (
                                        <div className="flex w-full">
                                            <div className="mx-auto flex gap-1">
                                                <DisplayItem item={currentMission.item} height="h-6 w-6" />
                                                <p className="my-auto text-base text-pink-400">
                                                    x{currentMission.itemRewardQuantity}
                                                </p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="my-auto flex flex-col">
                                <p className="text-center text-[0.6rem] text-stroke-sm uppercase dark:text-slate-200">
                                    {" "}
                                    Finishes in{" "}
                                </p>

                                <p className="text-center font-normal text-green-400 text-stroke-sm leading-6 lg:text-2xl">
                                    {formatTimeToNow(currentUser?.missionEnds)}
                                </p>
                            </div>
                            <Button
                                className="font-medium! text-base! mx-auto! mt-1! lg:block! hidden! text-stroke-sm"
                                variant="destructive"
                                onClick={() => handleCancelMission()}
                            >
                                Cancel Mission
                            </Button>
                        </div>
                        <Button
                            className="font-medium! text-base! mx-auto! mt-2! lg:hidden! h-9! text-stroke-sm"
                            variant="destructive"
                            onClick={() => handleCancelMission()}
                        >
                            Cancel Mission
                        </Button>
                    </div>
                </div>
            ) : null}
            <TierTabs
                currentTab={currentTab}
                setSelectedTier={setSelectedTier}
                currentLevel={currentUser?.level}
                missionTierLevelReqs={MISSION_TIER_REQ_LEVELS}
            />
            {totalMissionHours < currentTierReqHours || (currentUser?.level ?? 0) < currentTierReqLevel ? (
                <div className="mt-3 min-h-24 w-full rounded-lg border border-gray-600 bg-slate-800 p-3 text-center text-lg">
                    <p
                        className={clsx(
                            (currentUser?.level ?? 0) < currentTierReqLevel
                                ? "text-red-500"
                                : "text-green-500 opacity-75",
                            "text-stroke-sm"
                        )}
                    >
                        Requires level {currentTierReqLevel}
                    </p>

                    {totalMissionHours < currentTierReqHours && (
                        <div className="mx-auto mt-2 flex flex-col rounded-md border border-gray-600 bg-slate-900 p-2 md:w-3/4">
                            <p className="mx-auto mb-1 text-base text-red-500 text-stroke-sm md:text-lg">
                                Requires {currentTierReqHours} total mission hours completed
                            </p>
                            <Progress
                                barClassName="bg-blue-600"
                                className="mx-auto mt-1 w-3/4"
                                value={getProgressBarValue(currentTierReqHours, totalMissionHours)}
                            />
                            <p className="mt-1 text-gray-200">
                                {totalMissionHours} / {currentTierReqHours}
                            </p>
                        </div>
                    )}
                </div>
            ) : (
                <MissionsTable
                    missions={selectedMissions}
                    handleStartMission={handleStartMission}
                    currentUser={currentUser}
                />
            )}
        </div>
    );
}

export default Missions;

interface MissionsTableProps {
    missions: Mission[] | undefined;
    handleStartMission: (missionId: number) => void;
    currentUser: User | undefined;
}

const MissionsTable = ({ missions, handleStartMission, currentUser }: MissionsTableProps) => {
    return (
        <div className="-mx-4 mt-3 overflow-hidden bg-white ring-1 ring-gray-300 sm:mx-0 md:mt-0 md:min-h-90 dark:bg-slate-800 dark:ring-gray-600 rounded-lg">
            <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
                <thead className="text-gray-900 dark:bg-gray-800 dark:text-gray-200 dark:text-stroke-sm ">
                    <tr>
                        <th
                            scope="col"
                            className="py-3.5 pr-3 pl-4 text-left font-semibold text-sm sm:pl-6 dark:font-normal"
                        >
                            Mission
                        </th>
                        <th
                            scope="col"
                            className="hidden px-3 py-3.5 text-left font-semibold text-sm lg:table-cell dark:font-normal"
                        >
                            Duration
                        </th>
                        <th
                            scope="col"
                            className="table-cell px-3 py-3.5 text-center font-semibold text-sm dark:font-normal"
                        >
                            Rewards
                        </th>

                        <th scope="col" className="relative py-3.5 pr-4 pl-3 sm:pr-6">
                            <span className="sr-only">Select</span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {missions?.map((mission: Mission, i: number) => {
                        const isLocked = currentUser ? currentUser.level < mission.levelReq : true;
                        const isOnMission = Boolean(currentUser?.missionEnds && currentUser.missionEnds > 0);
                        const canStart = !isLocked && !isOnMission;
                        return (
                            <tr key={mission.id} className="h-24">
                                <td
                                    className={clsx(
                                        i === 0 ? "" : "border-transparent border-t",
                                        "relative py-4 pl-4 text-sm text-stroke-sm sm:pr-3 sm:pl-6"
                                    )}
                                >
                                    <div className="flex-1">
                                        <h3 className="card-title text-warning text-lg mb-1">{mission.missionName}</h3>
                                        <p className="text-sm text-base-content/70">{mission.description}</p>
                                    </div>
                                    {i !== 0 ? (
                                        <div className="-top-px absolute right-0 left-6 h-px bg-gray-200 dark:bg-gray-600" />
                                    ) : null}
                                </td>
                                <td
                                    className={clsx(
                                        i === 0 ? "" : "border-gray-200 border-t dark:border-gray-600",
                                        "hidden max-w-96 px-3 py-3.5 text-center text-custom-yellow text-sm text-stroke-sm lg:table-cell lg:text-lg"
                                    )}
                                >
                                    {convertToHours(mission.duration)} hrs
                                </td>
                                <td
                                    className={clsx(
                                        i === 0 ? "" : "border-gray-200 border-t dark:border-gray-600",
                                        "table-cell px-3 py-3.5 text-center font-medium font-body text-stroke-sm text-xs md:text-sm"
                                    )}
                                >
                                    {mission.minCashReward > 0 && (
                                        <p className="text-green-500">
                                            {formatCurrency(mission.minCashReward)} ~{" "}
                                            {formatCurrency(mission.maxCashReward)}
                                        </p>
                                    )}
                                    {mission.minExpReward > 0 && (
                                        <p className="text-blue-500">
                                            {mission.minExpReward} EXP ~ {"  "}
                                            {mission.maxExpReward} EXP
                                        </p>
                                    )}
                                    {mission.itemReward && (
                                        <div className="flex w-full">
                                            <div className="mx-auto flex gap-1">
                                                <DisplayItem item={mission.itemReward} height="h-10 w-10" />
                                                <p className="my-auto text-base text-pink-400">
                                                    x{mission.itemRewardQuantity}
                                                </p>
                                            </div>
                                        </div>
                                    )}
                                </td>

                                <td
                                    className={clsx(
                                        i === 0 ? "" : "border-transparent border-t",
                                        "relative py-3.5 pr-4 pl-3 text-right font-medium text-sm sm:pr-6"
                                    )}
                                >
                                    <p className="-mt-2 mb-1 text-center text-base text-custom-yellow lg:hidden">
                                        {" "}
                                        {convertToHours(mission.duration)} hrs
                                    </p>
                                    <button
                                        disabled={!canStart}
                                        className={clsx(
                                            "btn btn-sm",
                                            canStart ? "btn-primary text-white text-stroke-sm" : "btn-disabled"
                                        )}
                                        onClick={() => canStart && handleStartMission(mission.id)}
                                    >
                                        <Target className="w-4 h-4 mr-1" />
                                        Start
                                    </button>
                                    {i !== 0 ? (
                                        <div className="-top-px absolute right-6 left-0 h-px bg-gray-200 dark:bg-gray-600" />
                                    ) : null}
                                </td>
                            </tr>
                        );
                    })}
                </tbody>
            </table>
        </div>
    );
};

interface TierTabsProps {
    currentTab: (tabname: number) => boolean;
    setSelectedTier: (tier: number) => void;
    currentLevel: number | undefined;
    missionTierLevelReqs: readonly number[];
}

const TierTabs = ({ currentTab, setSelectedTier, currentLevel, missionTierLevelReqs }: TierTabsProps) => {
    const tabs: Tab[] = [
        { id: 1, name: "I", current: currentTab(1) },
        { id: 2, name: "II", current: currentTab(2) },
        { id: 3, name: "III", current: currentTab(3) },
        { id: 4, name: "IV", current: currentTab(4) },
        { id: 5, name: "V", current: currentTab(5) },
    ];

    return (
        <div role="tablist" className="tabs tabs-box tabs-lg bg-base-200 -mx-5 lg:mx-0 lg:mb-2">
            {tabs.map((tab: Tab) => {
                const isLocked = missionTierLevelReqs[tab.id - 1] > (currentLevel || 0);

                return (
                    <button
                        key={tab.id}
                        role="tab"
                        className={clsx("tab flex-1 gap-2", tab.current && "tab-active", isLocked && "opacity-50")}
                        onClick={() => setSelectedTier(tab.id)}
                    >
                        <span className="font-bold text-sm">Tier {tab.name}</span>
                        {isLocked && (
                            <div className="badge badge-error badge-xs">Lv.{missionTierLevelReqs[tab.id - 1]}</div>
                        )}
                    </button>
                );
            })}
        </div>
    );
};
