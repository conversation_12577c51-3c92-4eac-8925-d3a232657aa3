import enhancedCircularFrame from "@/assets/icons/frames/SmallCircle/enhancedCircle.png";
import legendaryCircularFrame from "@/assets/icons/frames/SmallCircle/legendaryCircle.png";
import militaryCircularFrame from "@/assets/icons/frames/SmallCircle/militaryCircle.png";
import noviceCircularFrame from "@/assets/icons/frames/SmallCircle/noviceCircle.png";
import specialistCircularFrame from "@/assets/icons/frames/SmallCircle/specialistCircle.png";
import standardCircularFrame from "@/assets/icons/frames/SmallCircle/standardCircle.png";
import enhancedFrame from "@/assets/icons/frames/enhanced.png";
import legendaryFrame from "@/assets/icons/frames/legendary.png";
import militaryFrame from "@/assets/icons/frames/military.png";
import noviceFrame from "@/assets/icons/frames/novice.png";
import specialFrame from "@/assets/icons/frames/special.png";
import specialistFrame from "@/assets/icons/frames/specialist.png";
import standardFrame from "@/assets/icons/frames/standard.png";
import type { InventoryItem, Item } from "@/types/item";
import { ItemTypes } from "@/types/item";
import { capitaliseFirstLetter } from "./capitaliseFirstLetter";

const addUpgradeModifiers = (value: number, upgradeLevel: number) => {
    const multiplierThreshold = 2.0;

    const modifyValue = (val: number) => {
        const upgradeLevelModifier = 1 + upgradeLevel * 0.05;
        return Number.parseFloat((val * upgradeLevelModifier).toFixed(0));
    };

    const modifyMultiplier = (multiplier: number) => {
        const basePercentage = multiplier - 1;
        const upgradeLevelModifier = upgradeLevel * 0.05;
        const newPercentage = basePercentage + basePercentage * upgradeLevelModifier;
        return Number.parseFloat((1 + newPercentage).toFixed(2));
    };

    if (upgradeLevel && upgradeLevel > 0) {
        return value < multiplierThreshold ? modifyMultiplier(value) : modifyValue(value);
    }
    return value;
};

export const getItemStatWithUpgrades = (item: InventoryItem, stat: string) => {
    const selectedStat = item?.item?.[stat as keyof Item] || 0;
    const upgradeLevel = item?.upgradeLevel || 0;
    const finalStat = Math.round(addUpgradeModifiers(selectedStat as number, upgradeLevel));
    return finalStat;
};

export const getItemFrame = (item: Item) => {
    if (!item) return noviceFrame;
    const rarity = item?.rarity;
    const itemType = item?.itemType;

    if (itemType === "special") {
        return specialFrame;
    }

    const itemFrames = {
        novice: noviceFrame,
        standard: standardFrame,
        enhanced: enhancedFrame,
        specialist: specialistFrame,
        military: militaryFrame,
        legendary: legendaryFrame,
    };

    return itemFrames[rarity] || noviceFrame;
};

export const getItemTypeIconImage = (item: Item) => {
    const itemTypeIcons: Record<ItemTypes, string> = {
        consumable: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/BtQtGhY.png`,
        junk: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/jj5xKEL.png`,
        weapon: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/7lgSBLb.png`,
        ranged: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/ZRsIRkW.png`,
        crafting: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/fnl0MPd.png`,
        offhand: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/7lgSBLb.png`,
        special: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/HtQLZLq.png`,
        quest: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/ss0T88X.png`,
        shield: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/glwRa3F.png`,
        finger: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/OjYDhSo.png`,
        head: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Jqt5Wj4.png`,
        chest: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Jqt5Wj4.png`,
        hands: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Jqt5Wj4.png`,
        legs: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Jqt5Wj4.png`,
        feet: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Jqt5Wj4.png`,
        recipe: "",
        upgrade: "",
        pet: "",
        pet_food: "",
    };

    return itemTypeIcons[item?.itemType] || `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/36vcU8I.png`;
};

export function getItemTypeIcon(item: Item) {
    const itemType = item?.itemType;
    const icons = {
        [ItemTypes.weapon]: "⚔️",
        [ItemTypes.ranged]: "🏹",
        [ItemTypes.shield]: "🛡️",
        [ItemTypes.head]: "👑",
        [ItemTypes.chest]: "👕",
        [ItemTypes.hands]: "🧤",
        [ItemTypes.legs]: "👖",
        [ItemTypes.feet]: "🥾",
        [ItemTypes.finger]: "💍",
        [ItemTypes.offhand]: "🗡️",
        [ItemTypes.consumable]: "🧪",
        [ItemTypes.crafting]: "⚒️",
        [ItemTypes.junk]: "🗑️",
        [ItemTypes.quest]: "📜",
        [ItemTypes.special]: "✨",
        [ItemTypes.recipe]: "📚",
        [ItemTypes.upgrade]: "⬆️",
        [ItemTypes.pet]: "🦙",
        [ItemTypes.pet_food]: "🍼",
    };

    return icons[itemType as keyof typeof icons] || "🔮";
}

export const getItemTypeFrame = (item: Item) => {
    const rarity = item?.rarity;
    const circularFrames = {
        novice: noviceCircularFrame,
        standard: standardCircularFrame,
        military: militaryCircularFrame,
        enhanced: enhancedCircularFrame,
        specialist: specialistCircularFrame,
        legendary: legendaryCircularFrame,
    };
    const bgImg = circularFrames[rarity] || `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/2kirNPd.png`;
    return {
        backgroundImage: `url(${bgImg})`,
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
    };
};

export const getItemType = (itemType: ItemTypes) => {
    if (itemType === "hands") return "Gloves";
    if (itemType === "finger") return "Ring";
    return capitaliseFirstLetter(itemType);
};
