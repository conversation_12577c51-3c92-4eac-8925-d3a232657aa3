import gameConfig from "../../../../config/gameConfig.js";
import * as ShrineHelper from "../../../../features/shrine/shrine.helpers.js";
import { db } from "../../../../lib/db.js";
import { getTomorrow } from "../../../../utils/dateHelpers.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";

async function getCirculatingYen(): Promise<number> {
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    // Get sum of bank balances for active non-admin users
    const bankBalanceResult = await db.user.aggregate({
        _sum: {
            bank_balance: true,
        },
        where: {
            userType: {
                not: "admin",
            },
            last_activity: {
                gte: sevenDaysAgo,
            },
        },
    });

    // Get sum of cash for active non-admin users
    const cashResult = await db.user.aggregate({
        _sum: {
            cash: true,
        },
        where: {
            userType: {
                not: "admin",
            },
            last_activity: {
                gte: sevenDaysAgo,
            },
        },
    });

    const totalYenInBanks = bankBalanceResult._sum.bank_balance || 0;
    const totalYenInCash = cashResult._sum.cash || 0;

    return totalYenInBanks + totalYenInCash;
}

async function generateDonationGoalForDate(date: Date, announce = false): Promise<void> {
    const circulatingYen = (await getCirculatingYen()) || 325_000;
    const percentOfCirculatingYen = gameConfig.SHRINE_GOAL_CIRCULATING_PERCENT || 0.06;
    const donationGoal = Math.round((circulatingYen * percentOfCirculatingYen) / 1000) * 1000;
    const randomBuffs = ShrineHelper.getRandomDailyBuffs();

    try {
        // Create new shrine goal using Prisma
        await db.shrine_goal.create({
            data: {
                donationGoal,
                goalDate: date,
                buffRewards: randomBuffs,
                donationAmount: 0, // Default value
                goalReached: false, // Default value
            },
        });

        if (announce) {
            setTimeout(() => ShrineHelper.announceDonationGoalReset(donationGoal), 360_000);
        }

        logger.info(`Stored new daily donation goal for ${date}`);
    } catch (error) {
        LogErrorStack({ message: `Error storing daily donation goal for ${date}: `, error });
        throw error;
    }
}

async function processShrineGoal(): Promise<void> {
    const tomorrow = getTomorrow();

    logger.profile("processShrineGoal");
    try {
        await generateDonationGoalForDate(tomorrow, true);
    } catch (error) {
        LogErrorStack({ message: "Failed to generate tomorrows daily donation goal:", error });
    }
    logger.profile("processShrineGoal");
}

export { processShrineGoal, generateDonationGoalForDate };
