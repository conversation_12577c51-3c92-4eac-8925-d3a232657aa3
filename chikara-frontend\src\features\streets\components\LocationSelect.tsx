import alley from "@/assets/images/roguelike/alley.webp";
import church from "@/assets/images/roguelike/churchalt.png";
import mall from "@/assets/images/roguelike/mallalt.png";
import school from "@/assets/images/roguelike/schoolalt.png";
import sewers from "@/assets/images/roguelike/sewers.webp";
import shrine from "@/assets/images/roguelike/shrinealt.png";
import IconButton from "@/components/Buttons/IconButton";
import NumberPicker from "@/components/NumberPicker";
import { Callout } from "@/components/TestComponents/Callout";
import ToggleSwitch from "@/components/ToggleSwitch";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useIsQuestCompleted from "@/hooks/api/useIsQuestCompleted";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";
import { usePersistStore } from "../../../app/store/stores";
import { useBeginRun } from "../api/useRoguelikeMutations";
import { type User } from "@/types/user";

interface Building {
    image: string;
    name: string;
    posX: string;
    posY: string;
    minimumZone: number | null;
    hidden?: boolean;
    disabled?: boolean;
    dropTypes: string;
}

interface LocationSelectProps {
    currentMap: User["roguelikeMap"];
}

export default function LocationSelect({ currentMap }: LocationSelectProps) {
    const [chosenLevel, setChosenLevel] = useState<number>(0);
    const [editEnabled, setEditEnabled] = useState<boolean>(false);
    const [roguelikeLevelSet, setRoguelikeLevelSet] = useState<boolean>(false);
    const { data: currentUser } = useFetchCurrentUser();
    const {
        CHURCH_MINIMUM_ZONE_LVL,
        MALL_MINIMUM_ZONE_LVL,
        SHRINE_MINIMUM_ZONE_LVL,
        ALLEY_MINIMUM_ZONE_LVL,
        SEWERS_MINIMUM_ZONE_LVL,
    } = useGameConfig();
    const isSewersUnlocked = useIsQuestCompleted("The Suspicious Figure");
    const { persistedZone, setPersistedZone } = usePersistStore();
    const beginRunMutation = useBeginRun();

    const handlePersistZone = () => {
        if (persistedZone) {
            setPersistedZone(null);
        } else {
            setPersistedZone(chosenLevel);
        }
    };

    useEffect(() => {
        if (!persistedZone) {
            if (currentUser && !roguelikeLevelSet) {
                if (currentMap?.mapComplete) {
                    if (currentUser.roguelikeLevel) {
                        setChosenLevel(currentUser.roguelikeLevel + 1);
                    } else {
                        setChosenLevel(currentUser.roguelikeHighscore);
                    }
                } else {
                    setChosenLevel(currentUser.roguelikeHighscore);
                }
                setRoguelikeLevelSet(true);
            }
        }
    }, [currentUser]);

    useEffect(() => {
        if (persistedZone) {
            if (chosenLevel !== persistedZone) {
                setChosenLevel(persistedZone);
            }
        }
    }, [persistedZone]);

    const startAdventure = (loc: string) => {
        beginRunMutation.mutate({
            location: loc.toLowerCase(),
            level: chosenLevel,
        });
    };

    const churchMinZone = CHURCH_MINIMUM_ZONE_LVL;
    const mallMinZone = MALL_MINIMUM_ZONE_LVL;
    const shrineMinZone = SHRINE_MINIMUM_ZONE_LVL;
    const alleyMinZone = ALLEY_MINIMUM_ZONE_LVL;
    const sewersMinZone = SEWERS_MINIMUM_ZONE_LVL;

    const buildings: Building[] = [
        {
            image: school,
            name: "School",
            posX: "top-[72%] md:top-[70%]",
            posY: "left-[18%] md:left-[25%]",
            minimumZone: 1,
            dropTypes: "Food, Crafting Materials",
        },
        {
            image: church,
            name: "Church",
            posX: "top-[65%] md:top-[48%]",
            posY: "left-[65%] md:left-[44%]",
            minimumZone: churchMinZone,
            hidden: (currentUser?.roguelikeHighscore ?? 0) < churchMinZone,
            disabled: chosenLevel < churchMinZone,
            dropTypes: "Ores",
        },
        {
            image: mall,
            name: "Mall",
            posX: "top-[25%] md:top-[63%]",
            posY: "left-[15%] md:left-[84%]",
            minimumZone: mallMinZone,
            hidden: (currentUser?.roguelikeHighscore ?? 0) < mallMinZone,
            disabled: chosenLevel < mallMinZone,
            dropTypes: "Food, Crafting Materials",
        },
        {
            image: shrine,
            name: "Shrine",
            posX: "top-[9%] md:top-[8%]",
            posY: "left-[35%] md:left-[27%]",
            minimumZone: shrineMinZone,
            hidden: (currentUser?.roguelikeHighscore ?? 0) < shrineMinZone,
            disabled: chosenLevel < shrineMinZone,
            dropTypes: "Gems, Food",
        },
        {
            image: alley,
            name: "Alley",
            posX: "top-[30%]",
            posY: "left-[65%]",
            minimumZone: alleyMinZone,
            hidden: (currentUser?.roguelikeHighscore ?? 0) < alleyMinZone,
            disabled: chosenLevel < alleyMinZone,
            dropTypes: "Ores, Gems",
        },

        {
            image: sewers,
            name: "Sewers",
            posX: "top-[46%] md:top-[60%]",
            posY: "left-[10%] md:left-[8%]",
            minimumZone: isSewersUnlocked ? sewersMinZone : null,
            hidden: !isSewersUnlocked,
            disabled: chosenLevel < sewersMinZone,
            dropTypes: "Gems, Crafting Materials",
        },
    ];

    const getDifficultyColour = (diff: number | null): string => {
        if (!diff) return "text-gray-400";
        if (diff < 6) return "text-sky-400";
        if (diff >= 6 && diff < 12) return "text-amber-400";
        if (diff >= 12) return "text-red-400";
        return "text-gray-400";
    };

    const handleInputChange = (value: number) => {
        setChosenLevel(value);
        if (persistedZone && persistedZone !== value) {
            setPersistedZone(value);
        }
    };

    return (
        <div className="md:-mt-24 relative size-full md:px-24">
            <div className="relative h-24 flex-col bg-[url(https://d13cmcqz8qkryo.cloudfront.net/static/backgrounds/exterior/city/street1Day.webp)] bg-center bg-cover px-6 py-4 text-center text-gray-200 text-sm text-stroke-sm md:h-48 md:py-12">
                <div className="absolute inset-0 size-full backdrop-blur-[1.5px] backdrop-brightness-[0.85]"></div>
                <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 w-fit rounded-md border-gray-800/50 bg-gray-800/50 px-4 py-1 backdrop-blur-xs">
                    <p className="z-10 my-auto text-center text-3xl uppercase font-display">Streets</p>
                </div>
            </div>
            <Callout
                type="warning"
                title="Streets is an endless adventure mode set in the perilous corners of Tokyo."
                subtitle="Defeat will reset your current map progress."
            />
            <div className="relative mt-2 flex flex-col items-start ml-4 lg:ml-2 text-gray-200 text-stroke-s-sm text-xl md:grid md:grid-cols-3">
                <div className="flex text-custom-yellow">
                    <span className="text-base md:text-xl font-display">Zone {editEnabled ? null : chosenLevel}</span>
                    {(currentUser?.roguelikeHighscore ?? 0) > 1 && !editEnabled && (
                        <>
                            <IconButton
                                className="ml-2 hidden size-7 md:flex"
                                iconClassName="h-[1.1rem] w-[1.1rem] fill-white/65 ml-0.5"
                                icon="edit"
                                onClick={() => setEditEnabled(true)}
                            />
                            <IconButton
                                className="ml-2 size-6 md:hidden"
                                iconClassName="h-[0.85rem] w-[0.85rem] fill-white/65 ml-0.5"
                                icon="edit"
                                onClick={() => setEditEnabled(true)}
                            />
                        </>
                    )}
                    {editEnabled && (
                        <div className="mb-1 flex gap-4 md:mb-0">
                            <NumberPicker
                                currentValue={chosenLevel}
                                className="ml-2"
                                handleInputChange={handleInputChange}
                                maxValue={currentUser?.roguelikeHighscore}
                                minValue={1}
                                valueChangeAmount={1}
                                textColour="text-custom-yellow"
                            />
                            <ToggleSwitch
                                className="rounded-md border border-gray-700/50 bg-black/25 px-2 py-0.5"
                                label="Stay on this Zone?"
                                value={persistedZone}
                                onChange={handlePersistZone}
                            />
                        </div>
                    )}
                </div>
            </div>
            <div className="mx-auto my-2 grid w-full grid-cols-2 gap-x-4 gap-y-2 px-4 pb-4 md:grid-cols-3 md:px-0">
                {buildings?.map((building) => (
                    <div
                        key={building.name}
                        className={cn(
                            "z-10 col-span-1 flex w-full flex-col rounded-2xl border border-gray-600/75 bg-slate-900"
                        )}
                    >
                        <img
                            src={building.image}
                            alt=""
                            className={cn(
                                building.hidden ? "brightness-0" : "brightness-[1.15]",
                                "mx-auto h-36 w-auto md:h-48"
                            )}
                        />
                        <div className="-mt-4 z-10 my-auto rounded-lg px-4 text-center">
                            <p className="text-2xl text-indigo-500 text-stroke-s-md font-accent">
                                {building.hidden ? "???" : building.name}
                            </p>
                            <p className="text-gray-300 dark:text-stroke-s-sm">
                                Zones{" "}
                                {building.minimumZone ? (
                                    <span className={cn(getDifficultyColour(building.minimumZone))}>
                                        {building.minimumZone}+
                                    </span>
                                ) : (
                                    <span className={cn(getDifficultyColour(building.minimumZone))}>Unknown</span>
                                )}
                            </p>
                            <p className="text-gray-300 text-stroke-s-sm text-sm">
                                Drops: <span className="text-green-500">{building.dropTypes}</span>
                            </p>
                        </div>
                        <button
                            disabled={building.disabled}
                            type="button"
                            className="skyblueButtonBGSVG m-auto my-3 flex items-center justify-center rounded-xl px-10 py-4 font-lili text-lg text-stroke-s-sm uppercase shadow-xs hover:brightness-90 disabled:brightness-50 md:mt-3 dark:text-slate-200 dark:hover:text-white"
                            onClick={() => (building.disabled ? null : startAdventure(building.name))}
                        >
                            Start
                        </button>
                    </div>
                ))}
            </div>
        </div>
    );
}
