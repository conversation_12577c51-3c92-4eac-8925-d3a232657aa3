@import url("https://fonts.googleapis.com/css2?family=Fredoka:wght@400;500;600;700&display=swap") layer(base);
@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap")
layer(base);
@import url("https://fonts.googleapis.com/css2?family=Luckiest+Guy&display=swap") layer(base);
@import url("https://fonts.googleapis.com/css2?family=Lilita+One&display=swap") layer(base);
@import url("https://fonts.googleapis.com/css2?family=Poetsen+One&display=swap") layer(base);

@import "tailwindcss";
@plugin "daisyui" {
    themes: dark --default;
}

@plugin "daisyui/theme" {
    name: "dark";
    default: true;
    color-scheme: "dark";
    /* --color-base-100: oklch(25.33% 0.016 252.42); */
    /* --color-base-100: oklch(28% 0.08 265); */
    --color-base-100: oklch(25.33% 0.05 252.42);
    --color-base-200: oklch(23.26% 0.014 253.1);
    --color-base-300: oklch(21.15% 0.012 254.09);
    --color-base-content: oklch(97.807% 0.029 256.847);
    --color-primary: oklch(62% 0.214 259.815);
    --color-primary-content: oklch(14.94% 0.031 39.947);
    --color-secondary: oklch(72.537% 0.177 2.72);
    --color-secondary-content: oklch(14.507% 0.035 2.72);
    --color-accent: oklch(85% 0.199 91.936);
    --color-accent-content: oklch(14.258% 0.033 299.844);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(55% 0.18 15);
    --color-error-content: oklch(85% 0.12 15);
    --radius-selector: 0.5rem;
    --radius-field: 0.5rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 2px;
    --depth: 1;
    --noise: 1;
}

@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/aspect-ratio';
@plugin '@tailwindcss/typography';

@custom-variant dark (&:is(.dark *));

@theme {
    --breakpoint-*: initial;
    --breakpoint-xs: 361px;
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1534px;
    --breakpoint-3xl: 1921px;

    --font-display:
        "Poetsen One", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
        "Noto Color Emoji";
    --font-body:
        "Fredoka", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
        "Noto Color Emoji";
    --font-accent: "Luckiest Guy", cursive;
    --font-lili: Lilita One;
    --font-lili--font-feature-settings: "ss01";

    --color-custom-yellow: #ffd700;

    --animate-meteor: meteor 5s infinite linear;
    --animate-marquee: marquee var(--duration) linear;

    @keyframes meteor {
        0% {
            transform: rotate(215deg) translateX(0);
            opacity: 1;
        }
        70% {
            opacity: 1;
        }
        100% {
            transform: rotate(215deg) translateX(-500px);
            opacity: 0;
        }
    }
    @keyframes marquee {
        0% {
            transform: translateX(200%);
            opacity: 1;
        }
        100% {
            transform: translateX(-100%);
            opacity: 1;
        }
    }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentcolor); /* Default border color */
        color: var(--color-gray-200); /* Default text color */
    }
}

@utility text-stroke-0 {
    text-shadow: none;
}

@utility stroke-sm {
    text-shadow: 1px 2px 10px rgba(0, 0, 0, 1);

    &:after {
        content: attr(data-text);
        /* translate: -50% 0; */
        position: absolute;
        left: 0;

        z-index: -1;

        /* visible stroke is 1/2 px because of alignment */
        -webkit-text-stroke: 5px black;
    }
}

@utility text-stroke-sm {
    text-shadow:
        -1px -1px 0 black,
        1px -1px 0 black,
        -1px 1px 0 black,
        1px 1px 0 black,
        1px 2px 1px rgba(0, 0, 0, 1);
}

@utility text-stroke-md {
    text-shadow:
        -2px -2px 0 black,
        2px -2px 0 black,
        -2px 2px 0 black,
        2px 2px 0 black,
        1px 2px 1px rgba(0, 0, 0, 1);
}

@utility text-stroke-lg {
    text-shadow:
        -3px -3px 0 black,
        3px -3px 0 black,
        -3px 3px 0 black,
        3px 3px 0 black,
        1px 2px 1px rgba(0, 0, 0, 1);
}

@utility text-stroke-s-sm {
    text-shadow:
        -1px -1px 0 black,
        1px -1px 0 black,
        -1px 1px 0 black,
        1px 1px 0 black,
        1px 2px 6px rgba(0, 0, 0, 1);
}

@utility text-stroke-s-md {
    text-shadow:
        -2px -2px 0 black,
        2px -2px 0 black,
        -2px 2px 0 black,
        2px 2px 0 black,
        1px 2px 10px rgba(0, 0, 0, 1);
}

@utility text-stroke-light-sm {
    text-shadow:
        -1px -1px 0 white,
        1px -1px 0 white,
        -1px 1px 0 white,
        1px 1px 0 white;
}

@utility text-stroke-light-md {
    text-shadow:
        -2px -2px 0 white,
        2px -2px 0 white,
        -2px 2px 0 white,
        2px 2px 0 white;
}

@utility text-stroke-light-lg {
    text-shadow:
        -3px -3px 0 white,
        3px -3px 0 white,
        -3px 3px 0 white,
        3px 3px 0 white;
}

@layer utilities {
    @font-face {
        font-family: "Lexend";
        src:
            local("Lexend"),
            url(./assets/fonts/lexend.woff2) format("woff2");
    }

    body {
        margin: 0;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        color: #31404b;
    }

    body {
        @apply font-body;
    }

    @media only screen and (max-width: 768px) {
        .ag-theme-quartz-dark .ag-root-wrapper {
            border-radius: 0 !important;
            border-left: 0px !important;
            border-right: 0px !important;
        }
        .ag-paging-description {
            font-size: 13px;
        }

        .ag-paging-row-summary-panel {
            display: none;
        }

        .ag-paging-page-summary-panel {
            margin: 0 auto !important;
        }
    }

    @media only screen and (max-width: 768px) {
        .ag-theme-quartz-dark {
        }
    }
}

input {
    color-scheme: dark;
}

#number-slider {
    color-scheme: light !important;
    cursor: pointer;
}

.noscrollbar::-webkit-scrollbar {
    width: 0px;
}

::-webkit-scrollbar {
    width: 2px;
}

/* @media only screen and (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 0px;
  }
} */

::-webkit-scrollbar-track {
    border-radius: 100vh;
    background: #192124;
}

::-webkit-scrollbar-thumb {
    background: #474747;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
