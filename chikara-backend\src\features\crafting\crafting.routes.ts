import * as CraftingAdmin from "./crafting.admin.js";
import * as CraftingController from "./crafting.controller.js";
import craftingSchema from "./crafting.validation.js";
import { isLoggedInAuth, canMakeStateChangesAuth, adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";

export const craftingRouter = {
    /**
     * Get user's crafting queue
     */
    getCraftingQueue: isLoggedInAuth.handler(async ({ context }) => {
        const response = await CraftingController.getCraftingQueue(context.user.id);
        return handleResponse(response);
    }),

    /**
     * Get available recipes for user
     */
    getRecipes: isLoggedInAuth.handler(async ({ context }) => {
        const response = await CraftingController.recipeList(context.user.id);
        return handleResponse(response);
    }),

    /**
     * Start crafting an item
     */
    craftItem: canMakeStateChangesAuth.input(craftingSchema.craftItem).handler(async ({ input, context }) => {
        const response = await CraftingController.craftItem(context.user, input);
        return handleResponse(response);
    }),

    /**
     * Complete a crafting operation
     */
    completeCraft: canMakeStateChangesAuth.input(craftingSchema.completeCraft).handler(async ({ input, context }) => {
        const response = await CraftingController.completeCraft(context.user.id, input.id);
        return handleResponse(response);
    }),

    /**
     * Cancel a crafting operation
     */
    cancelCraft: canMakeStateChangesAuth.input(craftingSchema.cancelCraft).handler(async ({ input, context }) => {
        const response = await CraftingController.cancelCraft(context.user.id, input.id);
        return handleResponse(response);
    }),
};

// ===============================================
// Admin Routes
// ===============================================

export const craftingAdminRouter = {
    /**
     * Get admin recipe list (includes all recipes)
     */
    getAdminRecipeList: adminAuth.handler(async ({ context }) => {
        const response = await CraftingController.recipeList(context.user.id, true);
        return handleResponse(response);
    }),

    /**
     * Create a new recipe (admin only)
     */
    createRecipe: adminAuth.input(craftingSchema.createRecipe).handler(async ({ input }) => {
        const response = await CraftingAdmin.createRecipe(input);
        return handleResponse(response);
    }),

    /**
     * Edit an existing recipe (admin only)
     */
    editRecipe: adminAuth.input(craftingSchema.editRecipe).handler(async ({ input }) => {
        const response = await CraftingAdmin.editRecipe(input);
        return handleResponse(response);
    }),

    /**
     * Delete a recipe (admin only)
     */
    deleteRecipe: adminAuth.input(craftingSchema.deleteRecipe).handler(async ({ input }) => {
        const response = await CraftingAdmin.deleteRecipe(input.id);
        return handleResponse(response);
    }),
};
