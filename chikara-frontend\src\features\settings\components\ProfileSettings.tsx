import uploadImageIcon from "@/assets/icons/uploadimageicon.svg";
import defaultAvatar from "@/assets/images/defaultAvatar.png";
import { User } from "@/types/user";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import useUpdateProfile from "../api/useUpdateProfile";
import ImageCropper from "./ImageCropper";
import { ImagePlus } from "lucide-react";

interface ProfileSettingsProps {
    currentUser?: User;
}

interface SelectedFile {
    blob: Blob;
    name: string;
}

export default function ProfileSettings({ currentUser }: ProfileSettingsProps) {
    const [selectedAvatar, setSelectedAvatar] = useState<File | null>(null);
    const [avatarPreview, setAvatarPreview] = useState<string | undefined>();
    const [selectedBanner, setSelectedBanner] = useState<SelectedFile | null>(null);
    const [selectedCrop, setSelectedCrop] = useState<File | null>(null);
    const [bannerPreview, setBannerPreview] = useState<string | null>(null);
    const [openCropper, setOpenCropper] = useState<boolean>(false);
    const [username, setUsername] = useState<string>(currentUser?.username || "");
    const [description, setDescription] = useState<string>(currentUser?.about || "");
    const updateProfileMutation = useUpdateProfile();

    // TODO - Implement this
    const isProfileBannerQuestComplete = true;

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>): void => {
        e.preventDefault();

        if (username.length > 17) {
            toast.error("Student name is too long!");
            return;
        }
        if (username.length < 4) {
            toast.error("Student name is too short!");
            return;
        }
        if (description.length > 500) {
            toast.error("Description is too long!");
            return;
        }

        // Create the input object for ORPC
        const updateData: {
            username: string;
            about: string;
            avatar?: File;
            banner?: Blob;
        } = {
            username,
            about: description,
        };

        if (selectedAvatar) {
            if (currentUser?.userType === "admin" && import.meta.env.MODE !== "development") {
                toast.error("Don't change admin avatars");
                return;
            }
            updateData.avatar = selectedAvatar;
        }

        if (selectedBanner) {
            updateData.banner = selectedBanner.blob;
        }

        updateProfileMutation.mutate(updateData);
    };

    useEffect(() => {
        if (!selectedAvatar) {
            if (currentUser?.avatar) {
                setAvatarPreview(`/${currentUser?.avatar}`);
            } else {
                setAvatarPreview(defaultAvatar);
            }

            return;
        }
        const objectUrl = URL.createObjectURL(selectedAvatar);
        setAvatarPreview(objectUrl);

        // free memory when ever this component is unmounted
        return () => URL.revokeObjectURL(objectUrl);
    }, [selectedAvatar]);

    const uploadBanner = (e: React.ChangeEvent<HTMLInputElement>): void => {
        e.preventDefault();
        if (e.target.files && e.target.files[0]) {
            setSelectedCrop(e.target.files[0]);
            setOpenCropper(true);
        }
    };

    useEffect(() => {
        if (!selectedBanner) {
            if (currentUser?.profileBanner) {
                setBannerPreview(`/${currentUser?.profileBanner}`);
            } else {
                setBannerPreview(uploadImageIcon);
            }

            return;
        }
        // const objectUrl = URL.createObjectURL(selectedBanner);
        // setBannerPreview(objectUrl);

        // // free memory when ever this component is unmounted
        // return () => URL.revokeObjectURL(objectUrl);
    }, [selectedBanner]);

    return (
        <form className="divide-y divide-gray-200 lg:col-span-9 dark:divide-gray-600" onSubmit={handleSubmit}>
            <div className="px-4 py-2 sm:p-6 md:py-6 lg:pb-8">
                <div>
                    <h2 className="font-medium text-gray-900 text-lg text-stroke-sm leading-6 dark:text-gray-200">
                        Profile Settings
                    </h2>
                    <div className="alert mt-2">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="stroke-current shrink-0 h-6 w-6"
                            fill="none"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                            />
                        </svg>

                        <span className="text-sm text-amber-500">
                            {" "}
                            This information will be displayed publicly so be careful what you share.
                        </span>
                    </div>
                </div>

                <div className="mt-6 flex flex-col lg:flex-row">
                    <div className="grow space-y-6">
                        <div>
                            <label
                                htmlFor="username"
                                className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                            >
                                Student Name
                            </label>
                            <div className="mt-1 flex rounded-md shadow-xs">
                                <input
                                    value={username}
                                    type="text"
                                    name="username"
                                    id="username"
                                    autoComplete="username"
                                    className="block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200"
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                        setUsername(e.target.value);
                                    }}
                                />
                            </div>
                        </div>

                        <div>
                            <label
                                htmlFor="about"
                                className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                            >
                                Profile Description
                            </label>
                            <div className="mt-1">
                                <textarea
                                    id="about"
                                    name="about"
                                    rows={3}
                                    maxLength={500}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200"
                                    value={description}
                                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                                        setDescription(e.target.value);
                                    }}
                                />
                            </div>
                            <p className="mt-2 text-gray-500 text-sm dark:font-normal dark:text-gray-400">
                                Brief description to be shown on your profile.
                            </p>
                        </div>
                    </div>

                    <div className="mt-6 grow lg:mt-0 lg:ml-6 lg:shrink-0 lg:grow-0">
                        <p
                            className="mb-3 block font-bold text-gray-700 text-xs uppercase tracking-wide md:text-center dark:font-normal dark:text-gray-300"
                            aria-hidden="true"
                        >
                            Avatar
                        </p>
                        <div className="mt-1 lg:hidden">
                            <div className="flex items-center">
                                <div
                                    className="inline-block size-12 shrink-0 overflow-hidden rounded-full"
                                    aria-hidden="true"
                                >
                                    <img className="size-full rounded-full" src={avatarPreview} alt="" />
                                </div>
                                <div className="ml-5 rounded-md shadow-xs">
                                    <div className="group relative flex items-center justify-center rounded-md border border-gray-300 px-3 py-2 focus-within:ring-2 focus-within:ring-light-blue-500 focus-within:ring-offset-2 hover:bg-gray-50 dark:border-blue-600 dark:bg-blue-700 dark:text-stroke-sm">
                                        <label
                                            htmlFor="user-photo-mobile"
                                            className="pointer-events-none relative font-medium text-gray-700 text-sm leading-4 dark:font-normal dark:text-white"
                                        >
                                            {/* MOBILE */}
                                            <span>Change Avatar</span>
                                            <span className="sr-only"> user photo</span>
                                        </label>
                                        <input
                                            id="user-photo-mobile"
                                            name="user-photo-mobile"
                                            type="file"
                                            accept="image/*"
                                            className="absolute size-full cursor-pointer rounded-md border-gray-300 opacity-0"
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                if (e.target.files && e.target.files[0]) {
                                                    setSelectedAvatar(e.target.files[0]);
                                                }
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="relative hidden overflow-hidden rounded-full lg:block">
                            <img className="relative size-40 rounded-full" src={avatarPreview} alt="" />
                            <label
                                htmlFor="user-photo"
                                className="absolute inset-0 flex size-full items-center justify-center bg-black/75 font-medium text-sm text-white opacity-0 focus-within:opacity-100 hover:opacity-100"
                            >
                                {/* DESKTOP */}
                                <span>Change</span>
                                <span className="sr-only"> user photo</span>
                                <input
                                    type="file"
                                    id="user-photo"
                                    name="user-photo"
                                    accept="image/*"
                                    className="absolute inset-0 size-full cursor-pointer rounded-md border-gray-300 opacity-0"
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                        if (e.target.files && e.target.files[0]) {
                                            setSelectedAvatar(e.target.files[0]);
                                        }
                                    }}
                                />
                            </label>
                        </div>
                    </div>
                </div>

                <div className="mt-6 grid grid-cols-12 gap-6">
                    <div className="col-span-12">
                        <div className="mt-8">
                            <h3 className="text-xs uppercase mb-2 flex items-center gap-2">
                                <ImagePlus className="size-3" />
                                Profile Banner
                                {!isProfileBannerQuestComplete && (
                                    <span className="badge badge-warning badge-sm">Quest Required</span>
                                )}
                            </h3>

                            <div className="relative bg-base-200">
                                <label
                                    htmlFor="profile-banner"
                                    className={
                                        selectedBanner || currentUser?.profileBanner
                                            ? "block cursor-pointer group"
                                            : "flex flex-col items-center justify-center h-40 border-2 border-dashed border-gray-600 rounded-lg cursor-pointer hover:bg-primary/5 transition-colors group"
                                    }
                                >
                                    {selectedBanner || currentUser?.profileBanner ? (
                                        <div className="relative overflow-hidden rounded-lg">
                                            <img
                                                src={bannerPreview}
                                                alt="Profile banner"
                                                className="w-full h-40 object-cover"
                                            />
                                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                                <div className="text-white text-center">
                                                    <ImagePlus className="size-8 mx-auto mb-1" />
                                                    <span className="text-sm font-medium">Change Banner</span>
                                                </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <>
                                            <ImagePlus className="size-12 text-accent/40 group-hover:text-accent/60 transition-colors" />
                                            <span className="mt-2 text-sm text-base-content/60 group-hover:text-base-content/80 transition-colors">
                                                Click to upload banner image
                                            </span>
                                        </>
                                    )}

                                    <input
                                        disabled={!isProfileBannerQuestComplete}
                                        type="file"
                                        accept="image/*"
                                        id="profile-banner"
                                        className="hidden"
                                        onChange={uploadBanner}
                                    />
                                </label>
                            </div>

                            {selectedBanner?.name && (
                                <div className="badge badge-info badge-sm mt-2 gap-1">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        className="inline-block w-4 h-4 stroke-current"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth="2"
                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                        />
                                    </svg>
                                    {selectedBanner.name}
                                </div>
                            )}
                        </div>

                        <ImageCropper
                            src={selectedCrop}
                            setPreview={setBannerPreview}
                            setSelected={setSelectedBanner}
                            open={openCropper}
                            setOpen={setOpenCropper}
                            setSrc={setSelectedCrop}
                        />
                    </div>
                </div>
            </div>

            <div className="mt-4 flex justify-end p-4 sm:px-6">
                <button
                    type="submit"
                    className="ml-5 inline-flex w-1/5 justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2"
                >
                    Save
                </button>
            </div>
        </form>
    );
}
