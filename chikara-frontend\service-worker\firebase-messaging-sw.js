import { initializeApp } from "firebase/app";
import { getMessaging, onBackgroundMessage } from "firebase/messaging/sw";
import { clientsClaim } from "workbox-core";
import { precacheAndRoute } from "workbox-precaching";

precacheAndRoute(self.__WB_MANIFEST);

// Initialize the Firebase app in the service worker by passing in
// your app's Firebase config object.
const firebaseConfig = {
    apiKey: "AIzaSyCQ-QeWMrBZw8v9idybo_cFwAIlDO6j-z0",
    authDomain: "chikara-academy.firebaseapp.com",
    projectId: "chikara-academy",
    storageBucket: "chikara-academy.appspot.com",
    messagingSenderId: "75175802639",
    appId: "1:75175802639:web:a1cc5e6073f185ec46707a",
};

// Retrieve an instance of Firebase Messaging so that it can handle background
// messages.
const firebaseApp = initializeApp(firebaseConfig);
const messaging = getMessaging(firebaseApp);

onBackgroundMessage(messaging, (payload) => {
    console.log("[firebase-messaging-sw.js] Received background message", payload);
    // // Customize notification here
    // const notificationTitle = "Background Message Title";
    // const notificationOptions = {
    //   body: "Background Message body.",
    //   icon: "/firebase-logo.png",
    // };

    // self.registration.showNotification(notificationTitle, notificationOptions);
});

self.skipWaiting();
clientsClaim();
