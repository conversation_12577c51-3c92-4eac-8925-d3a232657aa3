import {
  require_prop_types
} from "./chunk-J32HX4QX.js";
import {
  require_react_dom
} from "./chunk-SVFZZGYF.js";
import {
  require_react
} from "./chunk-YUJ2LLIH.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// ../node_modules/react-portal/es/PortalCompat.js
var import_react_dom3 = __toESM(require_react_dom());

// ../node_modules/react-portal/es/Portal.js
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var import_react_dom = __toESM(require_react_dom());

// ../node_modules/react-portal/es/utils.js
var canUseDOM = !!(typeof window !== "undefined" && window.document && window.document.createElement);

// ../node_modules/react-portal/es/Portal.js
var _createClass = /* @__PURE__ */ function() {
  function defineProperties(target, props) {
    for (var i = 0; i < props.length; i++) {
      var descriptor = props[i];
      descriptor.enumerable = descriptor.enumerable || false;
      descriptor.configurable = true;
      if ("value" in descriptor) descriptor.writable = true;
      Object.defineProperty(target, descriptor.key, descriptor);
    }
  }
  return function(Constructor, protoProps, staticProps) {
    if (protoProps) defineProperties(Constructor.prototype, protoProps);
    if (staticProps) defineProperties(Constructor, staticProps);
    return Constructor;
  };
}();
function _classCallCheck(instance, Constructor) {
  if (!(instance instanceof Constructor)) {
    throw new TypeError("Cannot call a class as a function");
  }
}
function _possibleConstructorReturn(self, call) {
  if (!self) {
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  }
  return call && (typeof call === "object" || typeof call === "function") ? call : self;
}
function _inherits(subClass, superClass) {
  if (typeof superClass !== "function" && superClass !== null) {
    throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
  }
  subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
}
var Portal = function(_React$Component) {
  _inherits(Portal4, _React$Component);
  function Portal4() {
    _classCallCheck(this, Portal4);
    return _possibleConstructorReturn(this, (Portal4.__proto__ || Object.getPrototypeOf(Portal4)).apply(this, arguments));
  }
  _createClass(Portal4, [{
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      if (this.defaultNode) {
        document.body.removeChild(this.defaultNode);
      }
      this.defaultNode = null;
    }
  }, {
    key: "render",
    value: function render() {
      if (!canUseDOM) {
        return null;
      }
      if (!this.props.node && !this.defaultNode) {
        this.defaultNode = document.createElement("div");
        document.body.appendChild(this.defaultNode);
      }
      return import_react_dom.default.createPortal(this.props.children, this.props.node || this.defaultNode);
    }
  }]);
  return Portal4;
}(import_react.default.Component);
Portal.propTypes = {
  children: import_prop_types.default.node.isRequired,
  node: import_prop_types.default.any
};
var Portal_default = Portal;

// ../node_modules/react-portal/es/LegacyPortal.js
var import_react2 = __toESM(require_react());
var import_react_dom2 = __toESM(require_react_dom());
var import_prop_types2 = __toESM(require_prop_types());
var _createClass2 = /* @__PURE__ */ function() {
  function defineProperties(target, props) {
    for (var i = 0; i < props.length; i++) {
      var descriptor = props[i];
      descriptor.enumerable = descriptor.enumerable || false;
      descriptor.configurable = true;
      if ("value" in descriptor) descriptor.writable = true;
      Object.defineProperty(target, descriptor.key, descriptor);
    }
  }
  return function(Constructor, protoProps, staticProps) {
    if (protoProps) defineProperties(Constructor.prototype, protoProps);
    if (staticProps) defineProperties(Constructor, staticProps);
    return Constructor;
  };
}();
function _classCallCheck2(instance, Constructor) {
  if (!(instance instanceof Constructor)) {
    throw new TypeError("Cannot call a class as a function");
  }
}
function _possibleConstructorReturn2(self, call) {
  if (!self) {
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  }
  return call && (typeof call === "object" || typeof call === "function") ? call : self;
}
function _inherits2(subClass, superClass) {
  if (typeof superClass !== "function" && superClass !== null) {
    throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
  }
  subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
}
var Portal2 = function(_React$Component) {
  _inherits2(Portal4, _React$Component);
  function Portal4() {
    _classCallCheck2(this, Portal4);
    return _possibleConstructorReturn2(this, (Portal4.__proto__ || Object.getPrototypeOf(Portal4)).apply(this, arguments));
  }
  _createClass2(Portal4, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      this.renderPortal();
    }
  }, {
    key: "componentDidUpdate",
    value: function componentDidUpdate(props) {
      this.renderPortal();
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      import_react_dom2.default.unmountComponentAtNode(this.defaultNode || this.props.node);
      if (this.defaultNode) {
        document.body.removeChild(this.defaultNode);
      }
      this.defaultNode = null;
      this.portal = null;
    }
  }, {
    key: "renderPortal",
    value: function renderPortal(props) {
      if (!this.props.node && !this.defaultNode) {
        this.defaultNode = document.createElement("div");
        document.body.appendChild(this.defaultNode);
      }
      var children = this.props.children;
      if (typeof this.props.children.type === "function") {
        children = import_react2.default.cloneElement(this.props.children);
      }
      this.portal = import_react_dom2.default.unstable_renderSubtreeIntoContainer(this, children, this.props.node || this.defaultNode);
    }
  }, {
    key: "render",
    value: function render() {
      return null;
    }
  }]);
  return Portal4;
}(import_react2.default.Component);
var LegacyPortal_default = Portal2;
Portal2.propTypes = {
  children: import_prop_types2.default.node.isRequired,
  node: import_prop_types2.default.any
};

// ../node_modules/react-portal/es/PortalCompat.js
var Portal3 = void 0;
if (import_react_dom3.default.createPortal) {
  Portal3 = Portal_default;
} else {
  Portal3 = LegacyPortal_default;
}
var PortalCompat_default = Portal3;

// ../node_modules/react-portal/es/PortalWithState.js
var import_react3 = __toESM(require_react());
var import_prop_types3 = __toESM(require_prop_types());
var _createClass3 = /* @__PURE__ */ function() {
  function defineProperties(target, props) {
    for (var i = 0; i < props.length; i++) {
      var descriptor = props[i];
      descriptor.enumerable = descriptor.enumerable || false;
      descriptor.configurable = true;
      if ("value" in descriptor) descriptor.writable = true;
      Object.defineProperty(target, descriptor.key, descriptor);
    }
  }
  return function(Constructor, protoProps, staticProps) {
    if (protoProps) defineProperties(Constructor.prototype, protoProps);
    if (staticProps) defineProperties(Constructor, staticProps);
    return Constructor;
  };
}();
function _classCallCheck3(instance, Constructor) {
  if (!(instance instanceof Constructor)) {
    throw new TypeError("Cannot call a class as a function");
  }
}
function _possibleConstructorReturn3(self, call) {
  if (!self) {
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  }
  return call && (typeof call === "object" || typeof call === "function") ? call : self;
}
function _inherits3(subClass, superClass) {
  if (typeof superClass !== "function" && superClass !== null) {
    throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
  }
  subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
}
var KEYCODES = {
  ESCAPE: 27
};
var PortalWithState = function(_React$Component) {
  _inherits3(PortalWithState2, _React$Component);
  function PortalWithState2(props) {
    _classCallCheck3(this, PortalWithState2);
    var _this = _possibleConstructorReturn3(this, (PortalWithState2.__proto__ || Object.getPrototypeOf(PortalWithState2)).call(this, props));
    _this.portalNode = null;
    _this.state = { active: !!props.defaultOpen };
    _this.openPortal = _this.openPortal.bind(_this);
    _this.closePortal = _this.closePortal.bind(_this);
    _this.wrapWithPortal = _this.wrapWithPortal.bind(_this);
    _this.handleOutsideMouseClick = _this.handleOutsideMouseClick.bind(_this);
    _this.handleKeydown = _this.handleKeydown.bind(_this);
    return _this;
  }
  _createClass3(PortalWithState2, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      if (this.props.closeOnEsc) {
        document.addEventListener("keydown", this.handleKeydown);
      }
      if (this.props.closeOnOutsideClick) {
        document.addEventListener("click", this.handleOutsideMouseClick);
      }
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      if (this.props.closeOnEsc) {
        document.removeEventListener("keydown", this.handleKeydown);
      }
      if (this.props.closeOnOutsideClick) {
        document.removeEventListener("click", this.handleOutsideMouseClick);
      }
    }
  }, {
    key: "openPortal",
    value: function openPortal(e) {
      if (this.state.active) {
        return;
      }
      if (e && e.nativeEvent) {
        e.nativeEvent.stopImmediatePropagation();
      }
      this.setState({ active: true }, this.props.onOpen);
    }
  }, {
    key: "closePortal",
    value: function closePortal() {
      if (!this.state.active) {
        return;
      }
      this.setState({ active: false }, this.props.onClose);
    }
  }, {
    key: "wrapWithPortal",
    value: function wrapWithPortal(children) {
      var _this2 = this;
      if (!this.state.active) {
        return null;
      }
      return import_react3.default.createElement(
        PortalCompat_default,
        {
          node: this.props.node,
          key: "react-portal",
          ref: function ref(portalNode) {
            return _this2.portalNode = portalNode;
          }
        },
        children
      );
    }
  }, {
    key: "handleOutsideMouseClick",
    value: function handleOutsideMouseClick(e) {
      if (!this.state.active) {
        return;
      }
      var root = this.portalNode && (this.portalNode.props.node || this.portalNode.defaultNode);
      if (!root || root.contains(e.target) || e.button && e.button !== 0) {
        return;
      }
      this.closePortal();
    }
  }, {
    key: "handleKeydown",
    value: function handleKeydown(e) {
      if (e.keyCode === KEYCODES.ESCAPE && this.state.active) {
        this.closePortal();
      }
    }
  }, {
    key: "render",
    value: function render() {
      return this.props.children({
        openPortal: this.openPortal,
        closePortal: this.closePortal,
        portal: this.wrapWithPortal,
        isOpen: this.state.active
      });
    }
  }]);
  return PortalWithState2;
}(import_react3.default.Component);
PortalWithState.propTypes = {
  children: import_prop_types3.default.func.isRequired,
  defaultOpen: import_prop_types3.default.bool,
  node: import_prop_types3.default.any,
  closeOnEsc: import_prop_types3.default.bool,
  closeOnOutsideClick: import_prop_types3.default.bool,
  onOpen: import_prop_types3.default.func,
  onClose: import_prop_types3.default.func
};
PortalWithState.defaultProps = {
  onOpen: function onOpen() {
  },
  onClose: function onClose() {
  }
};
var PortalWithState_default = PortalWithState;
export {
  PortalCompat_default as Portal,
  PortalWithState_default as PortalWithState
};
//# sourceMappingURL=react-portal.js.map
