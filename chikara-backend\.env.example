NODE_ENV=development
RESTRICT_ACCESS=false

FRONTEND_REDIRECT_URL=http://localhost:8080
APP_BASE_URL=https://localhost:3000

LOG_LEVEL="info"
SESSION_SECRET=
PROXY_ENABLED=true

# DB
DATABASE_URL=

# REDIS
REDIS_URL=redis://default:default@************:6385/0

# AUDIT LOG DB
XATA_BRANCH=develop
XATA_API_KEY=

# AUTH
BETTER_AUTH_SECRET=
BETTER_AUTH_URL=http://localhost:3000

# FILES/CDN
CLOUDFLARE_R2_ENDPOINT=
ENABLE_CDN_UPLOADS=true
CLOUDFLARE_R2_ACCESS_KEY_ID=
CLOUDFLARE_R2_SECRET_ACCESS_KEY=
CLOUDFLARE_R2_BUCKET_NAME=chikara-assets

AWS_SECRET_ACCESS_KEY=
AWS_ACCESS_KEY_ID=
AWS_REGION=eu-west-2

REQUEST_PROFILING=false

# DISCORD
DISCORD_WEBHOOK_URL=
DISCORD_BOT_TOKEN=
DISCORD_CLIENT_ID=
DISCORD_CLIENT_SECRET=

# GOOGLE OAUTH
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# AI
ACTIVE_LLM_API=groq
OPENAI_API_KEY=
GEMINI_API_KEY=
GROQ_API_KEY=