import * as CreatureController from "./creature.controller.js";
import { createCreatureSchema, deleteCreatureSchema, updateCreatureSchema } from "./creature.validation.js";
import { adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";

// ===============================================
// Admin Routes
// ===============================================
export const creatureAdminRouter = {
    // Get list of all creatures (admin only)
    list: adminAuth.handler(async () => {
        const response = await CreatureController.creatureList();
        return handleResponse(response);
    }),

    // Create new creature (admin only)
    create: adminAuth.input(createCreatureSchema).handler(async ({ input }) => {
        const response = await CreatureController.createCreature(input);
        return handleResponse(response);
    }),

    // Update existing creature (admin only)
    update: adminAuth.input(updateCreatureSchema).handler(async ({ input }) => {
        const response = await CreatureController.editCreature(input.id, input);
        return handleResponse(response);
    }),

    // Delete creature (admin only)
    delete: adminAuth.input(deleteCreatureSchema).handler(async ({ input }) => {
        const response = await CreatureController.deleteCreature(input.id);
        return handleResponse(response);
    }),
};
