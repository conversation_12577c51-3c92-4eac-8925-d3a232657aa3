export const defaultQuestObjectives = [
    {
        questId: 1,
        objectiveType: "UNIQUE_OBJECTIVE",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 2,
        objectiveType: "UNIQUE_OBJECTIVE",
        target: 1,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: 1,
        isRequired: true,
    },
    {
        questId: 3,
        objectiveType: "UNIQUE_OBJECTIVE",
        target: 2,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 4,
        objectiveType: "DEFEAT_NPC",
        target: 2,
        targetAction: null,
        quantity: 3,
        location: "school",
        description: "",
        creatureId: 2,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 5,
        objectiveType: "UNIQUE_OBJECTIVE",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 6,
        objectiveType: "ACQUIRE_ITEM",
        target: 172,
        targetAction: null,
        quantity: 3,
        location: "church",
        description: "",
        creatureId: null,
        itemId: 172,
        isRequired: true,
    },
    {
        questId: 6,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: 172,
        isRequired: true,
    },
    {
        questId: 7,
        objectiveType: "UNIQUE_OBJECTIVE",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    // {
    //     questId: 8,
    //     objectiveType: "roguelike_level",
    //     target: 10,
    //     targetAction: null,
    //     quantity: null,
    //     location: null,
    //     description: "",
    //     creatureId: null,
    //     itemId: null,
    //     isRequired: true,
    // },
    {
        questId: 9,
        objectiveType: "UNIQUE_OBJECTIVE",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 10,
        objectiveType: "ACQUIRE_ITEM",
        target: 157,
        targetAction: null,
        quantity: 3,
        location: "school",
        description: "",
        creatureId: null,
        itemId: 157,
        isRequired: true,
    },
    {
        questId: 10,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: 157,
        isRequired: true,
    },
    {
        questId: 11,
        objectiveType: "DEFEAT_NPC",
        target: 68,
        targetAction: null,
        quantity: 1,
        location: "church",
        description: "",
        creatureId: 68,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 12,
        objectiveType: "ACQUIRE_ITEM",
        target: 158,
        targetAction: null,
        quantity: 5,
        location: "sewers",
        description: "",
        creatureId: null,
        itemId: 158,
        isRequired: true,
    },
    {
        questId: 12,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 5,
        location: null,
        description: "",
        creatureId: null,
        itemId: 158,
        isRequired: true,
    },
    // {
    //     questId: 13,
    //     objectiveType: "roguelike_level",
    //     target: 20,
    //     targetAction: null,
    //     quantity: null,
    //     location: null,
    //     description: "",
    //     creatureId: null,
    //     itemId: null,
    //     isRequired: true,
    // },
    {
        questId: 14,
        objectiveType: "DEFEAT_NPC",
        target: null,
        targetAction: null,
        quantity: 8,
        location: "school",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 15,
        objectiveType: "DEFEAT_NPC",
        target: null,
        targetAction: null,
        quantity: 12,
        location: "church",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 16,
        objectiveType: "DEFEAT_NPC_WITH_LOW_DAMAGE",
        target: 25,
        targetAction: null,
        quantity: 6,
        location: "school",
        description: "",
        creatureId: 25,
        itemId: null,
        isRequired: true,
    },
    // {
    //     questId: 17,
    //     objectiveType: "roguelike_level",
    //     target: 30,
    //     targetAction: null,
    //     quantity: null,
    //     location: null,
    //     description: "",
    //     creatureId: null,
    //     itemId: null,
    //     isRequired: true,
    // },
    {
        questId: 18,
        objectiveType: "ACQUIRE_ITEM",
        target: 159,
        targetAction: null,
        quantity: 1,
        location: "school",
        description: "",
        creatureId: null,
        itemId: 159,
        isRequired: true,
    },
    {
        questId: 18,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: 159,
        isRequired: true,
    },
    {
        questId: 19,
        objectiveType: "DEFEAT_PLAYER",
        target: 5,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 20,
        objectiveType: "PVP_POST_BATTLE_CHOICE",
        target: null,
        targetAction: "mug",
        quantity: 2,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 21,
        objectiveType: "DEFEAT_PLAYER",
        target: 7,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 22,
        objectiveType: "PVP_POST_BATTLE_CHOICE",
        target: null,
        targetAction: "leave",
        quantity: 4,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 23,
        objectiveType: "DEFEAT_PLAYER",
        target: 10,
        targetAction: null,
        quantity: 5,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 24,
        objectiveType: "DEFEAT_PLAYER",
        target: 15,
        targetAction: null,
        quantity: 6,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 25,
        objectiveType: "PVP_POST_BATTLE_CHOICE",
        target: null,
        targetAction: "mug",
        quantity: 5,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 26,
        objectiveType: "DEFEAT_PLAYER",
        target: 20,
        targetAction: null,
        quantity: 5,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 27,
        objectiveType: "DEFEAT_PLAYER",
        target: 22,
        targetAction: null,
        quantity: 7,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 28,
        objectiveType: "PVP_POST_BATTLE_CHOICE",
        target: 24,
        targetAction: "cripple",
        quantity: 5,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 29,
        objectiveType: "DEFEAT_PLAYER",
        target: 25,
        targetAction: null,
        quantity: 7,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 30,
        objectiveType: "DEFEAT_PLAYER",
        target: 30,
        targetAction: null,
        quantity: 10,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 31,
        objectiveType: "PVP_POST_BATTLE_CHOICE",
        target: 28,
        targetAction: "leave",
        quantity: 8,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 32,
        objectiveType: "PVP_POST_BATTLE_CHOICE",
        target: 28,
        targetAction: "cripple",
        quantity: 8,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 33,
        objectiveType: "PVP_POST_BATTLE_CHOICE",
        target: 28,
        targetAction: "mug",
        quantity: 8,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    // {
    //     questId: 34,
    //     objectiveType: "roguelike_level",
    //     target: 3,
    //     targetAction: null,
    //     quantity: null,
    //     location: null,
    //     description: "",
    //     creatureId: null,
    //     itemId: null,
    //     isRequired: true,
    // },
    {
        questId: 35,
        objectiveType: "ACQUIRE_ITEM",
        target: 156,
        targetAction: null,
        quantity: 3,
        location: "mall",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 35,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: 156,
        isRequired: true,
    },
    {
        questId: 36,
        objectiveType: "DEFEAT_NPC",
        target: 19,
        targetAction: null,
        quantity: 4,
        location: "mall",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 37,
        objectiveType: "PLACE_BOUNTY",
        target: 500,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 38,
        objectiveType: "ACQUIRE_ITEM",
        target: 171,
        targetAction: null,
        quantity: 3,
        location: "shrine",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 38,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: 171,
        isRequired: true,
    },
    {
        questId: 39,
        objectiveType: "DEFEAT_NPC",
        target: null,
        targetAction: null,
        quantity: 7,
        location: "mall",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 40,
        objectiveType: "ACQUIRE_ITEM",
        target: 160,
        targetAction: null,
        quantity: 1,
        location: "shrine",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 40,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: 160,
        isRequired: true,
    },
    {
        questId: 41,
        objectiveType: "DEFEAT_NPC",
        target: 21,
        targetAction: null,
        quantity: 4,
        location: "mall",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 42,
        objectiveType: "ACQUIRE_ITEM",
        target: 175,
        targetAction: null,
        quantity: 3,
        location: "mall",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 42,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: 175,
        isRequired: true,
    },
    {
        questId: 43,
        objectiveType: "DEFEAT_NPC",
        target: null,
        targetAction: null,
        quantity: 5,
        location: "sewers",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 44,
        objectiveType: "DEFEAT_NPC",
        target: 69,
        targetAction: null,
        quantity: 1,
        location: "sewers",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 45,
        objectiveType: "DEFEAT_NPC_IN_TURNS",
        target: 3,
        targetAction: null,
        quantity: 6,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 46,
        objectiveType: "DEFEAT_NPC_WITH_LOW_DAMAGE",
        target: 15,
        targetAction: null,
        quantity: 5,
        location: "shrine",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 47,
        objectiveType: "DEFEAT_NPC",
        target: null,
        targetAction: null,
        quantity: 15,
        location: "shrine",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 48,
        objectiveType: "CRAFT_ITEM",
        target: 125,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 48,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: 125,
        isRequired: true,
    },
    {
        questId: 49,
        objectiveType: "CRAFT_ITEM",
        target: 198,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 49,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: 198,
        isRequired: true,
    },
    {
        questId: 50,
        objectiveType: "CRAFT_ITEM",
        target: 161,
        targetAction: null,
        quantity: 4,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 50,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 4,
        location: null,
        description: "",
        creatureId: null,
        itemId: 161,
        isRequired: true,
    },
    {
        questId: 51,
        objectiveType: "CRAFT_ITEM",
        target: 138,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 51,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: 138,
        isRequired: true,
    },
    {
        questId: 52,
        objectiveType: "CRAFT_ITEM",
        target: 196,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 52,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: 196,
        isRequired: true,
    },
    {
        questId: 53,
        objectiveType: "ACQUIRE_ITEM",
        target: 195,
        targetAction: null,
        quantity: 4,
        location: "church",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 53,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 4,
        location: null,
        description: "",
        creatureId: null,
        itemId: 195,
        isRequired: true,
    },
    {
        questId: 54,
        objectiveType: "CRAFT_ITEM",
        target: 176,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 54,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: 176,
        isRequired: true,
    },
    {
        questId: 55,
        objectiveType: "ACQUIRE_ITEM",
        target: 164,
        targetAction: null,
        quantity: 5,
        location: "sewers",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 56,
        objectiveType: "CRAFT_ITEM",
        target: 177,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 56,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: 177,
        isRequired: true,
    },
    {
        questId: 57,
        objectiveType: "CRAFT_ITEM",
        target: 202,
        targetAction: null,
        quantity: 4,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 57,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 4,
        location: null,
        description: "",
        creatureId: null,
        itemId: 202,
        isRequired: true,
    },
    {
        questId: 58,
        objectiveType: "CRAFT_ITEM",
        target: 209,
        targetAction: null,
        quantity: 2,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 58,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 2,
        location: null,
        description: "",
        creatureId: null,
        itemId: 209,
        isRequired: true,
    },
    {
        questId: 59,
        objectiveType: "CRAFT_ITEM",
        target: 149,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 59,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: 149,
        isRequired: true,
    },
    {
        questId: 60,
        objectiveType: "CRAFT_ITEM",
        target: 188,
        targetAction: null,
        quantity: 2,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 60,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 2,
        location: null,
        description: "",
        creatureId: null,
        itemId: 188,
        isRequired: true,
    },
    {
        questId: 61,
        objectiveType: "UNIQUE_OBJECTIVE",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 62,
        objectiveType: "DEFEAT_NPC",
        target: null,
        targetAction: null,
        quantity: 12,
        location: "sewers",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 63,
        objectiveType: "DEFEAT_NPC",
        target: null,
        targetAction: null,
        quantity: 14,
        location: "alley",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 64,
        objectiveType: "ACQUIRE_ITEM",
        target: 173,
        targetAction: null,
        quantity: 1,
        location: "alley",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 64,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: 173,
        isRequired: true,
    },
    {
        questId: 65,
        objectiveType: "ACQUIRE_ITEM",
        target: 163,
        targetAction: null,
        quantity: 6,
        location: "sewers",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 65,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 6,
        location: null,
        description: "",
        creatureId: null,
        itemId: 163,
        isRequired: true,
    },
    {
        questId: 66,
        objectiveType: "CRAFT_ITEM",
        target: 106,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 67,
        objectiveType: "ACQUIRE_ITEM",
        target: 165,
        targetAction: null,
        quantity: 5,
        location: "shrine",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 67,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 5,
        location: null,
        description: "",
        creatureId: null,
        itemId: 165,
        isRequired: true,
    },
    {
        questId: 68,
        objectiveType: "ACQUIRE_ITEM",
        target: 166,
        targetAction: null,
        quantity: 8,
        location: "sewers",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 68,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 8,
        location: null,
        description: "",
        creatureId: null,
        itemId: 166,
        isRequired: true,
    },
    {
        questId: 69,
        objectiveType: "DEFEAT_NPC",
        target: 70,
        targetAction: null,
        quantity: 1,
        location: "mall",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 70,
        objectiveType: "ACQUIRE_ITEM",
        target: 167,
        targetAction: null,
        quantity: 3,
        location: "school",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 70,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "",
        creatureId: null,
        itemId: 167,
        isRequired: true,
    },
    {
        questId: 71,
        objectiveType: "ACQUIRE_ITEM",
        target: 168,
        targetAction: null,
        quantity: 5,
        location: "church",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 71,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 5,
        location: null,
        description: "",
        creatureId: null,
        itemId: 168,
        isRequired: true,
    },
    {
        questId: 72,
        objectiveType: "PVP_POST_BATTLE_CHOICE",
        target: 30,
        targetAction: "cripple",
        quantity: 5,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 73,
        objectiveType: "UNIQUE_OBJECTIVE",
        target: 1,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 74,
        objectiveType: "UNIQUE_OBJECTIVE",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 75,
        objectiveType: "UNIQUE_OBJECTIVE",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 76,
        objectiveType: "UNIQUE_OBJECTIVE",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 77,
        objectiveType: "ACQUIRE_ITEM",
        target: 309,
        targetAction: null,
        quantity: 3,
        location: "shrine",
        description: "",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    // Resource Gathering Quest Objectives
    {
        questId: 78,
        objectiveType: "GATHER_RESOURCES",
        target: 200, // Copper Ore
        targetAction: "mining",
        quantity: 5,
        location: null,
        description: "Mine 5 Copper Ore",
        creatureId: null,
        itemId: 200,
        isRequired: true,
    },
    {
        questId: 79,
        objectiveType: "GATHER_RESOURCES",
        target: null, // Any item
        targetAction: "scavenging",
        quantity: 3,
        location: null,
        description: "Scavenge 3 items",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 80,
        objectiveType: "GATHER_RESOURCES",
        target: null, // Any item
        targetAction: "foraging",
        quantity: 4,
        location: null,
        description: "Forage 4 plants",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 81,
        objectiveType: "GATHER_RESOURCES",
        target: 202, // Silver Ore
        targetAction: "mining",
        quantity: 2,
        location: null,
        description: "Mine 2 Silver Ore",
        creatureId: null,
        itemId: 202,
        isRequired: true,
    },
    {
        questId: 81,
        objectiveType: "GATHER_RESOURCES",
        target: 203, // Gold Ore
        targetAction: "mining",
        quantity: 1,
        location: null,
        description: "Mine 1 Gold Ore",
        creatureId: null,
        itemId: 203,
        isRequired: true,
    },
    {
        questId: 82,
        objectiveType: "GATHER_RESOURCES",
        target: null, // Any item
        targetAction: "mining",
        quantity: 3,
        location: null,
        description: "Mine 3 ores",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 82,
        objectiveType: "GATHER_RESOURCES",
        target: null, // Any item
        targetAction: "scavenging",
        quantity: 2,
        location: null,
        description: "Scavenge 2 items",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    {
        questId: 82,
        objectiveType: "GATHER_RESOURCES",
        target: null, // Any item
        targetAction: "foraging",
        quantity: 2,
        location: null,
        description: "Forage 2 plants",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
];

export default defaultQuestObjectives;
