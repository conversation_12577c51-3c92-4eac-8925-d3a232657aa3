import gameConfig from "../../config/gameConfig.js";
import { Prisma } from "@prisma/client";

export default Prisma.defineExtension((client) => {
    return client.$extends({
        result: {
            quest: {
                xpReward: {
                    needs: { id: true, levelReq: true, xpReward: true },
                    compute(quest) {
                        const { levelReq, xpReward } = quest;
                        if (!xpReward || xpReward === 0 || levelReq === 0) {
                            return null;
                        }
                        return Math.round(
                            levelReq * gameConfig.XP_TO_LEVEL_MULTIPLIER * gameConfig.QUEST_XP_REWARD_MULTIPLIER
                        );
                    },
                },
            },
        },
    });
});
