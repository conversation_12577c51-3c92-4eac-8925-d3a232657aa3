import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { type AppRouterClient } from "@/lib/orpc";

export type Mission = Awaited<ReturnType<AppRouterClient["mission"]["getList"]>>[number];

export const useMissionList = (options = {}) => {
    return useQuery(
        api.missions.getList.queryOptions({
            staleTime: 60000, // 1 minute
            ...options,
        })
    );
};
