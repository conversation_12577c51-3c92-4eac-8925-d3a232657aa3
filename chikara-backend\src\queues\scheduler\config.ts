import gameConfig from "../../config/gameConfig.js";
import ms from "ms";

// Any values taken from gameConfig require a server restart to apply live changes
export function getSchedulerConfig() {
    return {
        tasks: {
            auctions: {
                checkExpiredAuctions: ms("5m"), // Every 5 minutes
            },

            cleanups: {
                cleanupExpiredStatusEffects: ms("1h"), // Hourly
                checkTimeoutBattles: ms("10m"), // Every 10 minutes
                cleanupExpiredScavengingSessions: ms("30m"), // Every 30 minutes
            },
            jobs: {
                processFirstJobPayments: "0 2 * * *", // 2AM UTC
                processSecondJobPayments: "0 8 * * *", // 8AM UTC
                processThirdJobPayments: "0 14 * * *", // 2PM UTC
                processFourthJobPayments: "0 20 * * *", // 8PM UTC
            },
            shops: {
                restockSundayShop: "30 11 * * SUN", // Sunday 11:30 AM UTC
                openSundayShop: "00 12 * * SUN", // Sunday 12:00 PM UTC
                closeSundayShop: "00 00 * * MON", // Monday 00:00 AM UTC
            },
            casino: {
                announceLotteryEnding: "30 17 * * *", //  5:30 PM UTC daily
                drawLotteryWinner: "00 18 * * *", //  6:00 PM UTC daily
                createDailyLottery: "05 18 * * *", // 6:05PM UTC daily
            },
            missions: {
                processDailyMissions: "50 23 * * *", // 11:50PM UTC daily
            },
            health: {
                processHealthIncreases: gameConfig.HEALING_TICK_INTERVAL,
                // processAPIncreases: "*/10 * * * *", // Every 10 minutes
                processAPIncreases: gameConfig.AP_TICK_INTERVAL, // Every 10 minutes
            },
            shrine: {
                processShrineGoal: "55 23 * * *", // 11:55 PM UTC daily
            },
            gangs: {
                processGangPayouts: "0 0 * * MON", // Monday at 00:00 UTC
                resetDailyEssenceLimit: "0 0 * * *", // Every day at 00:00 UTC
            },
            stats: {
                collectDailyStats: "5 0 * * *", // 00:05 UTC daily
                collectPlayerStats: "10 0 * * *", // 00:10 UTC daily
            },
            npc: {
                generateRandomNPCListings: "0 */5 * * *", // Every 5 hours
                placeRandomNPCBounty: "0 */2 * * *", // Every 2 hours
            },
            pets: {
                processHappinessDecay: "0 */4 * * *", // Every 4 hours
            },
        },
    };
}

// Export default for backward compatibility
export default getSchedulerConfig();
