# Chikara Academy Codebase Rules & Overview

## 1. Game Summary

Chikara Academy is an immersive persistent browser-based game (PBBG) set in a modern urban world with mystical elements. Players become martial artists, mastering their craft while navigating social dynamics, gang rivalries, and personal growth. The game blends strategic turn-based combat, deep character progression, dynamic social features, a player-driven economy, roguelike elements, and daily/long-term progression goals. Gameplay is GUI and image-based, with no active character control or explorable world.

## Root Level Structure

```
├── chikara-frontend/     # Main game client (React/Vite)
├── chikara-backend/      # Game server (Express/Bun)
├── admin-panel/          # Admin dashboard (React/Vite)
├── chikara-landing/      # Marketing site (Next.js)
├── assets/               # Game assets (images, icons, characters)
├── docs/                 # Documentation, rules and guides
└── turbo.json            # Turborepo configuration
```

## Technology Stack

## Build System

- **Monorepo**: Turborepo with Bun package manager
- **Package Manager**: Bun 1.2.19+ (preferred over npm/yarn)
- **Workspaces**: 4 main applications (frontend, backend, admin-panel, landing)

## Frontend Stack

- **Framework**: React 19+ with TypeScript
- **Build Tool**: Vite 7+
- **Styling**: Tailwind CSS 4+ with Radix UI components
- **State Management**: Zustand, React Tracked
- **Data Fetching**: TanStack Query with oRPC client
- **Routing**: React Router DOM 7+
- **Real-time**: Socket.io client

## Backend Stack

- **Runtime**: Bun (Node.js 21.2.0+ compatible)
- **Framework**: Express 5+ with TypeScript
- **Database**: MySQL with Prisma ORM 6.11+
- **Caching**: Redis 5+
- **Queue System**: BullMQ
- **Real-time**: Socket.io server
- **API**: oRPC for type-safe RPC
- **Authentication**: Better-Auth
- **Testing**: Vitest

## Admin Panel

- **Framework**: React 19+ with TypeScript
- **UI**: Radix UI, shadcn/ui components
- **Data Tables**: TanStack Table
- **Charts**: Recharts
- **Build**: Vite

---

## 1a. Gameplay Systems & Features

Chikara Academy features a wide range of interconnected gameplay systems:

- **Character Progression:** Leveling, stats (Strength, Dexterity, Intelligence, Endurance, Defence, Vitality), talents, skills, prestige
- **Combat System:** Turn-based PvP/PvE, status effects, buffs/debuffs, unique roguelike mode
- **Roguelike Mode:** Procedurally generated dungeons, boss battles, exclusive rewards
- **Questing:** Data-driven quests (main, daily, tutorial), quest chains, tracked progress
- **Crafting:** Recipe-based, time-gated, material gathering, crafting quests
- **Economy:** Multi-currency (Yen, Gang Creds, Classroom Points), shops, auction house, banking, jobs
- **Social Systems:** Gangs, friends/rivals, chat (global/gang/private), profile comments, private messaging
- **Property & Real Estate:** Property ownership, benefits, upgrades
- **Exploration:** District-based, dynamic nodes (battle, scavenging, mining, foraging, shops, encounters), travel system, node expiration
- **Other Notable Features:** Shrine system, casino, pet system, bounties, infirmary, jail, events, classroom/exams, leaderboards, missions, arcade minigames, settings page

---

## 1b. Stat System Overview

Chikara Academy uses six core combat stats, each with unique primary and secondary effects, supporting diverse builds and playstyles:

- **Strength (STR):** Melee damage, anti-evasion, armor penetration
- **Dexterity (DEX):** Ranged damage, critical hit chance, initiative
- **Intelligence (INT):** Tech ability power, stamina regen, control/debuffs
- **Endurance (END):** Stamina pool, evasion, buff duration, resource management
- **Defence (DEF):** Damage reduction, anti-crit, DoT resistance
- **Vitality (VIT):** Health pool, healing received, combat speed

Each stat has associated passive talents and combat abilities. For detailed effects and example builds, see [`docs/stats.md`](./stats.md).

---

## 2. Shared Principles

- **Type Safety:** End-to-end TypeScript across frontend and backend using oRPC
- **Modular Architecture:** Features are isolated; cross-feature logic goes in shared/core modules
- **Error Handling:** Centralized, with clear boundaries for global/component/API errors
- **Code Style:** Explicit types, ES Modules, DRY & KISS & YAGNI principles, ESLint + Prettier
- **Documentation:** JSDoc/TSDoc/Code comments for complex logic only
- **Authentication:** Better-Auth, Cookie-based, automatic error handling, middleware-driven
- **Real-time:** Socket.io for live features, managed by dedicated modules

## 3. Frontend Rules & Structure

### Tech Stack

- TypeScript, React 19+, Vite 7+, Tailwind CSS 4+, shadcn + Radix UI, Zustand + React Tracked, TanStack Query, oRPC, React Router 7, Socket.io, Better-Auth, Vitest, PWA

### Project Structure

```
chikara-frontend/
├── public/              # Static assets, PWA manifest
├── service-worker/      # Firebase messaging service worker
├── src/
│   ├── app/             # App-level config/providers
│   ├── assets/          # Images, fonts, sounds
│   ├── components/      # Reusable UI components
│   ├── constants/       # App constants/config
│   ├── features/        # Feature modules
│   ├── helpers/         # Utility functions
│   ├── hooks/           # Custom React hooks
│   ├── lib/             # Core libraries/config
│   ├── pages/           # Route components
│   ├── test/            # Test utilities
│   ├── types/           # TypeScript types
│   ├── utils/           # Pure utilities
│   ├── App.tsx          # Main app component
│   ├── index.tsx        # Entry point
│   └── SocketManager.tsx# Socket.io manager
└── tsconfig.json
```

### Feature Organization

- Each feature in `src/features/` has: `__tests__/`, `components/`, `hooks/`, `types/`, `utils/`, `index.ts`
- **Rules:**
    - No direct imports between features
    - Use `src/lib/`, `src/helpers/`, or `src/hooks/` for cross-feature logic
    - Avoid barrel files

### State Management

- **Zustand** stores: `persistStore`, `sessionStore`, `normalStore`, `socketStore`, `authStore`
- Uses `createTrackedSelector` for re-render optimization
- Prefer React Query for server state

### API Integration

- Always use `api` from `@/helpers/api.ts` (never call `orpc` directly)
- Use `useQuery()`/`useMutation()` with `api.*.queryOptions()`/`mutationOptions()`
- Place all query/mutation hooks in feature/api directories
- Invalidate queries using `queryClient.invalidateQueries()` with appropriate keys

### Component Types

- **Pages:** `src/pages/`
- **Features:** `src/features/`
- **UI Components:** `src/components/`
- **Layout:** `src/components/Layout/`

### Styling

- Tailwind CSS, Radix UI, shadcn/ui, dark mode only

### Real-time

- Socket.io connection managed by `SocketManager.tsx`, state in `socketStore`, feature-specific event handlers

### Testing

- Vitest for unit/component tests, use `src/test/` utilities

### Code Style

- Prefer explicit types, fix root causes over type assertions
- Use `@/` alias for imports

### PWA

- Offline-first caching, Firebase push, auto-updates

### Error Handling

- Global API errors: query client
- Component errors: error boundaries
- Auth errors: auto logout/redirect

---

## 3a. Admin Panel

- **Framework:** React 19+ with TypeScript
- **UI:** Radix UI, shadcn/ui components
- **Data Tables:** TanStack Table
- **Charts:** Recharts
- **Build:** Vite

---

## 4. Backend Rules & Structure

### Tech Stack

- Bun, TypeScript, Express.js, oRPC, MySQL (Prisma ORM), Redis, BullMQ, Socket.io, Winston logger, Vitest

### Project Structure

```
chikara-backend/
├── prisma/         # Prisma ORM
├── public/         # Static assets
├── src/
│   ├── config/     # Config
│   ├── data/       # Static data
│   ├── features/   # Feature modules
│   ├── lib/        # Shared libs
│   ├── middleware/ # Express middleware
│   ├── queues/     # BullMQ jobs
│   ├── core/       # Shared services
│   ├── __tests__/  # Unit test mocks
│   ├── types/      # TypeScript types
│   ├── utils/      # Utilities
│   ├── routes.ts   # Express routes
│   └── server.ts   # Main server
└── certs/          # SSL certs
```

### Feature Organization

- Each feature in `src/features/` has: `__tests__/`, `feature-name.routes.ts`, `feature-name.controller.ts`, `feature-name.repository.ts`, `feature-name.helpers.ts` (optional), `feature-name.validation.ts`, `feature-name.types.ts` (optional), `feature-name.constants.ts` (optional), `feature-name.admin.ts` (optional)
- **Guidelines:**
    - Single responsibility per file
    - Naming: `feature-name.file-type.ts`
    - Shared logic in `src/core/`, never import between features
    - Unit tests in `__tests__` alongside feature

### Database (Prisma)

- Schema: `prisma/schema.prisma`
- Always import extended Prisma client from `src/lib/db`
- Model naming: `snake_case` (tables), `camelCase` (fields)
- Custom methods: `src/lib/prisma/`
- Mocking: `src/lib/__mocks__/prisma.ts`

### API Design (oRPC)

- oRPC core: `src/lib/orpc.ts`, main router: `src/routes.ts`
- Feature-specific oRPC routers (e.g., `user.routes.ts`)
- Input validation: Zod schemas
- Auth: Middleware (`publicAuth`, `isLoggedInAuth`, `canMakeStateChangesAuth`, `adminAuth`)
- Error handling: `handleResponse()`, `ORPCError`
- Type safety: End-to-end with frontend

### Code Style

- TypeScript, ES Modules, async/await
- Logging: Winston logger (`logger`, `LogErrorStack` from `src/utils/log.js`)
- Constants: `UPPER_SNAKE_CASE`
- Types: `.types.ts` per feature
- Prefer functions over classes
- Feature isolation: only use logic from core or within feature

### Testing

- Vitest, prisma mocks from `src/_tests_/`, `.test.ts` suffix

### Utilities & Libraries

- Core: `log`, `cacheHelper`, `jsonHelper`
- oRPC: context creation, auth middleware, error handling, type safety
- Shared services: inventory, equipment, achievement
- Scheduler: BullMQ-based, for cleanups, health, missions, shops, auctions, casino, gangs, shrine

### Usage Guide

- Always use `logger` from utils/log.ts, never `console.log`
- Use shared services for cross-feature logic
- Use scheduler for background tasks
- Always import extended Prisma client from `src/lib/db.js`
- For oRPC: use proper auth, input validation, `handleResponse`, throw `ORPCError` for expected errors

---

## 5. Common Commands

### Development

```bash
# Start all services
bun dev

# Start specific service
bun dev:frontend
bun dev:backend
bun dev:admin

# Database operations (backend only)
bun run seed         # Seed database
```

### Build & Deploy

```bash
# Build all
bun build

# Build specific
bun build:frontend
bun build:backend
bun build:admin
```

### Testing

```bash
# Run all tests
bun test
```

### Code Quality

```bash
bun type-check     # TypeScript
```
