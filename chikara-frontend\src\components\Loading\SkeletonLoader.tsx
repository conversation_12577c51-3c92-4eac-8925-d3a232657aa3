import React from "react";
import { cn } from "@/lib/utils";

export interface SkeletonProps {
    className?: string;
    variant?: "text" | "circular" | "rectangular" | "rounded";
    width?: string | number;
    height?: string | number;
    animation?: "pulse" | "wave" | "none";
}

/**
 * Basic skeleton component for loading states
 */
export function Skeleton({
    className,
    variant = "rectangular",
    width,
    height,
    animation = "pulse",
    ...props
}: SkeletonProps & React.HTMLAttributes<HTMLDivElement>) {
    const baseClasses = cn(
        "bg-gray-200 dark:bg-gray-700",
        {
            "animate-pulse": animation === "pulse",
            "animate-wave": animation === "wave",
            "rounded-full": variant === "circular",
            "rounded": variant === "rounded",
            "rounded-none": variant === "rectangular",
            "rounded-md": variant === "text",
        },
        className
    );
    
    const style = {
        width: typeof width === "number" ? `${width}px` : width,
        height: typeof height === "number" ? `${height}px` : height,
    };
    
    return <div className={baseClasses} style={style} {...props} />;
}

/**
 * Text skeleton with multiple lines
 */
export function TextSkeleton({
    lines = 3,
    className,
    lastLineWidth = "75%",
}: {
    lines?: number;
    className?: string;
    lastLineWidth?: string;
}) {
    return (
        <div className={cn("space-y-2", className)}>
            {Array.from({ length: lines }).map((_, index) => (
                <Skeleton
                    key={index}
                    variant="text"
                    height="1rem"
                    width={index === lines - 1 ? lastLineWidth : "100%"}
                />
            ))}
        </div>
    );
}

/**
 * Avatar skeleton
 */
export function AvatarSkeleton({
    size = "md",
    className,
}: {
    size?: "sm" | "md" | "lg" | "xl";
    className?: string;
}) {
    const sizeClasses = {
        sm: "w-8 h-8",
        md: "w-12 h-12",
        lg: "w-16 h-16",
        xl: "w-24 h-24",
    };
    
    return (
        <Skeleton
            variant="circular"
            className={cn(sizeClasses[size], className)}
        />
    );
}

/**
 * Card skeleton with header, content, and actions
 */
export function CardSkeleton({
    showAvatar = false,
    showActions = false,
    className,
}: {
    showAvatar?: boolean;
    showActions?: boolean;
    className?: string;
}) {
    return (
        <div className={cn("p-4 border rounded-lg space-y-4", className)}>
            {/* Header */}
            <div className="flex items-center space-x-3">
                {showAvatar && <AvatarSkeleton size="sm" />}
                <div className="flex-1 space-y-2">
                    <Skeleton height="1rem" width="40%" />
                    <Skeleton height="0.75rem" width="60%" />
                </div>
            </div>
            
            {/* Content */}
            <div className="space-y-2">
                <Skeleton height="1rem" width="100%" />
                <Skeleton height="1rem" width="90%" />
                <Skeleton height="1rem" width="75%" />
            </div>
            
            {/* Actions */}
            {showActions && (
                <div className="flex space-x-2">
                    <Skeleton height="2rem" width="5rem" variant="rounded" />
                    <Skeleton height="2rem" width="5rem" variant="rounded" />
                </div>
            )}
        </div>
    );
}

/**
 * Table skeleton
 */
export function TableSkeleton({
    rows = 5,
    columns = 4,
    showHeader = true,
    className,
}: {
    rows?: number;
    columns?: number;
    showHeader?: boolean;
    className?: string;
}) {
    return (
        <div className={cn("w-full", className)}>
            {/* Header */}
            {showHeader && (
                <div className="grid gap-4 p-4 border-b" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
                    {Array.from({ length: columns }).map((_, index) => (
                        <Skeleton key={index} height="1rem" width="80%" />
                    ))}
                </div>
            )}
            
            {/* Rows */}
            {Array.from({ length: rows }).map((_, rowIndex) => (
                <div
                    key={rowIndex}
                    className="grid gap-4 p-4 border-b"
                    style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
                >
                    {Array.from({ length: columns }).map((_, colIndex) => (
                        <Skeleton
                            key={colIndex}
                            height="1rem"
                            width={`${60 + Math.random() * 30}%`}
                        />
                    ))}
                </div>
            ))}
        </div>
    );
}

/**
 * List skeleton
 */
export function ListSkeleton({
    items = 5,
    showAvatar = false,
    showActions = false,
    className,
}: {
    items?: number;
    showAvatar?: boolean;
    showActions?: boolean;
    className?: string;
}) {
    return (
        <div className={cn("space-y-3", className)}>
            {Array.from({ length: items }).map((_, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 border rounded">
                    {showAvatar && <AvatarSkeleton size="sm" />}
                    <div className="flex-1 space-y-1">
                        <Skeleton height="1rem" width="60%" />
                        <Skeleton height="0.75rem" width="40%" />
                    </div>
                    {showActions && (
                        <div className="flex space-x-2">
                            <Skeleton height="1.5rem" width="3rem" variant="rounded" />
                        </div>
                    )}
                </div>
            ))}
        </div>
    );
}

/**
 * Form skeleton
 */
export function FormSkeleton({
    fields = 4,
    showSubmit = true,
    className,
}: {
    fields?: number;
    showSubmit?: boolean;
    className?: string;
}) {
    return (
        <div className={cn("space-y-4", className)}>
            {Array.from({ length: fields }).map((_, index) => (
                <div key={index} className="space-y-2">
                    <Skeleton height="1rem" width="25%" />
                    <Skeleton height="2.5rem" width="100%" variant="rounded" />
                </div>
            ))}
            
            {showSubmit && (
                <div className="pt-4">
                    <Skeleton height="2.5rem" width="8rem" variant="rounded" />
                </div>
            )}
        </div>
    );
}

export default Skeleton;
