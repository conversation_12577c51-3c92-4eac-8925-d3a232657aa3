import { cn } from "@/lib/utils";
import { WarningIcon } from "./WarningIcon";

interface CalloutProps {
    type: "info" | "warning";
    title: string;
    subtitle?: string;
    children?: React.ReactNode;
    className?: string;
}

export function Callout({ type = "warning", title, subtitle, children, className }: CalloutProps) {
    return (
        <div
            role="alert"
            className={cn(className, "alert alert-soft mb-4", type === "warning" ? "alert-warning" : "alert-info")}
        >
            <WarningIcon color={type === "warning" ? "amber" : "blue"} className="size-8 flex-none" />
            <div>
                <span
                    className={cn(
                        "text-sm font-display",
                        type === "warning" ? "text-amber-500" : "text-gray-100 text-stroke-sm"
                    )}
                >
                    {title}
                </span>
                <p className="text-gray-300! text-xs">{subtitle}</p>
                {children && <div className={cn("prose mt-2 text-xs")}>{children}</div>}
            </div>
        </div>
    );
}
