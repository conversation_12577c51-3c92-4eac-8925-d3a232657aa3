import { redisClient } from "../../config/redisClient.js";
import * as sockets from "../../config/socket.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as StatusEffectService from "../../core/statuseffect.service.js";
import * as UserService from "../../core/user.service.js";
import * as BattleController from "../battle/battle.controller.js";
import * as devRepository from "../../repositories/dev.repository.js";
import { evolvePet } from "../pets/pets.controller.js";
import { addPetXp } from "../pets/pets.helpers.js";
import { ExtUserModel } from "../../lib/db.js";
import { NotificationTypes } from "../../types/notification.js";
// import * as AIUtil from "../../utils/ai.js";
// import { getNow } from "../../utils/dateHelpers.js";
import { LogErrorStack, logger } from "../../utils/log.js";
import type { Prisma } from "@prisma/client";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";

// interface ChatNotificationData {
//     message: string;
//     userId: number;
//     chatRoomId: number;
//     hidden: boolean;
//     createdAt: Date;
//     updatedAt: Date;
// }

export const SendNotification = (userId: number, type: NotificationTypes, details: string) => {
    sockets.SendNotification(userId, {
        type,
        details,
    });
    return { data: "Notification sent" };
};

export const TestEmail = async () => {
    // EmailHelpers.EmailUser(1, 1);
    // return { data: "Email sent" };
    return { error: "not implemented" };
};

export const AddXp = async (userId: number, xp: number | string) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    const newXP = await UserService.AddXPToUser(currentUser, Number.parseInt(xp.toString()));
    return { data: newXP };
};

export const FullHealEveryone = async () => {
    const users = await devRepository.findAllUsers();

    for (const user of users) {
        const maxHealth = await user.maxHealth;
        const updateData: Prisma.userUpdateInput = {
            hospitalisedUntil: null,
            hospitalisedHealingType: null,
            hospitalisedReason: null,
            jailedUntil: null,
            jailReason: null,
            currentHealth: maxHealth,
            energy: 100,
            actionPoints: 10,
        };
        await UserService.updateUser(user.id, updateData);
        // Clear all status effects/injuries
        await StatusEffectService.removeUserStatusEffects(user.id);
    }

    return { data: "All users healed" };
};

export const FullHeal = async (userId: number) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    const maxHealth = await currentUser.maxHealth;
    await UserService.updateUser(currentUser.id, {
        currentHealth: maxHealth,
        energy: 100,
        actionPoints: currentUser.maxActionPoints,
    });
    // Clear all status effects/injuries
    await StatusEffectService.removeUserStatusEffects(currentUser.id);
    return { data: "User healed" };
};

const getPlayerActiveBattleKey = (playerId: string | number) => `player:${playerId}:activeBattle`;

const resetUserState = async (user: ExtUserModel) => {
    const maxHealth = await user.maxHealth;
    await UserService.updateUser(user.id, {
        hospitalisedUntil: null,
        hospitalisedHealingType: null,
        hospitalisedReason: null,
        jailedUntil: null,
        jailReason: null,
        currentHealth: maxHealth,
        energy: 100,
        actionPoints: user.maxActionPoints,
    });
};

export const StartRandomPVPBattle = async (userId: number) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    const target = await devRepository.findRandomUser(userId);

    if (!target) {
        return { error: "No target found", statusCode: 400 };
    }

    await redisClient.del(getPlayerActiveBattleKey(currentUser.id));
    await redisClient.del(getPlayerActiveBattleKey(target.id));

    // Reset target state
    await resetUserState(target);

    // Reset current user state
    await resetUserState(currentUser);

    // Re-fetch users after updates before initiating battle
    const updatedCurrentUser = await UserRepository.getUserById(userId);
    const updatedTarget = await UserRepository.getUserById(target.id);

    if (!updatedCurrentUser || !updatedTarget) {
        return { error: "Failed to fetch updated user data", statusCode: 500 };
    }

    const result = await BattleController.initiatePVPBattle(updatedCurrentUser.id, updatedTarget.id);

    if ("error" in result) {
        return { error: result.error, statusCode: 400 };
    }

    return { data: result.data };
};

export const cleanupAllBattles = async () => {
    try {
        const keys = await redisClient.keys("battle:*");
        const playerKeys = await redisClient.keys("player:*:activeBattle");

        if (keys.length === 0 && playerKeys.length === 0) {
            logger.info("No battles to clean up");
            return { error: "No battles to clean up", statusCode: 400 };
        }

        const pipeline = redisClient.multi();

        for (const key of [...keys, ...playerKeys]) {
            pipeline.del(key);
        }

        await pipeline.exec();

        logger.info(`Cleaned up ${keys.length} battles`);

        return {
            data: {
                message: "Successfully cleaned up all battles",
                battlesRemoved: keys.length,
                playerBattlesRemoved: playerKeys.length,
            },
        };
    } catch (error) {
        LogErrorStack({ error });
        return { error: error instanceof Error ? error.message : String(error), statusCode: 500 };
    }
};

export const ResetQuests = async (userId: number) => {
    await devRepository.deleteQuestProgress(userId);
    return { data: "Quests reset" };
};

export const AddResources = async (userId: number) => {
    const updatedUser = await devRepository.addMultipleResources(userId);
    return { data: { message: "Resources added", user: updatedUser } };
};

export const AddStats = async (userId: number, amount = 1) => {
    await devRepository.incrementUserStats(userId, amount);
    return { data: "Stats added" };
};

export const AddAllItems = async (userId: number) => {
    const items = await ItemRepository.findAllItems();
    const promises = items.map((item) => {
        return devRepository.createUserItem(userId, item.id, 1);
    });
    await Promise.all(promises);
    return { data: "All items added" };
};

export const RemoveStats = async (userId: number, amount = 1) => {
    await devRepository.decrementUserStats(userId, amount);
    return { data: "Stats removed" };
};

export const StartRandomMap = async (userId: number) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    const randomZone = Math.floor(Math.random() * 100);
    const maxHealth = await currentUser.maxHealth;
    const updateData: Prisma.userUpdateInput = {
        hospitalisedUntil: null,
        hospitalisedHealingType: null,
        hospitalisedReason: null,
        jailedUntil: null,
        jailReason: null,
        currentHealth: maxHealth,
        energy: 100,
        actionPoints: 10,
        roguelikeMap: undefined,
        roguelikeHighscore: randomZone + 1,
    };

    await UserService.updateUser(currentUser.id, updateData);

    const locations = ["church", "shrine", "mall", "alley", "school"];
    const randomLoc = Math.floor(Math.random() * locations.length);

    return {
        data: {
            level: randomZone,
            location: locations[randomLoc],
            userId,
        },
    };
};

export const CompleteAllQuests = async (userId: number) => {
    const quests = await devRepository.findAllQuests();

    const promises = quests.map((quest) => {
        return devRepository.createQuestProgress(userId, quest.id, "complete");
    });

    await Promise.all(promises);

    const traderPromises = [];
    for (let i = 0; i < 6; i++) {
        traderPromises.push(devRepository.createTraderRep(userId, i + 1, 4));
    }

    await Promise.all(traderPromises);

    return { data: "All quests completed" };
};

export const SendAIChatMessage = async () => {
    return { data: "Not implemented" };
    // const messages = await devRepository.findRecentChatMessages(10);
    // if (!messages || messages.length < 5) {
    //     return { error: "Not enough recent messages!", statusCode: 400 };
    // }

    // const messagesJSON = JSON.stringify(messages);

    // const completion = await AIUtil.sendAIMessages([
    //     {
    //         role: "system",
    //         content:
    //             "You are Haruka Ito, the female school administrator for the MMORPG anime web game 'Chikara Academy', set in a fictional 'battle academy' in tokyo. I will send you the 10 latest chat messages from the games global chat, please only output a response that you will send in chat while roleplaying as the academy administrator. Try to reference the conversation history of the chat or direct your message to one of the players if possible. Keep your message under 140 characters and don't be too formal. Do not specifically say you are an administrator for the academy, it should be assumed. Use @username to refer to a player. Don't use hashtags. Speak like you're one of the other players and don't be too formal or overly nice. If the previous messages are over 40 minutes old then start a new discussion instead. If you choose to respond to a message from Hanoka Schimimi say it in a slightly mean tone. Do not discourage combat as this is a battle academy. Prioritise responding to any questions directed at yourself. Prioritise the most recent messages. Do not respond to any of your own messages. Here is the latest 5 chat messages:",
    //     },
    //     {
    //         role: "system",
    //         content: "Do not repeat yourself if you have sent a similar message previously.",
    //     },
    //     { role: "user", content: messagesJSON },
    // ]);

    // const { success, results: response } = completion;

    // if (success) {
    //     const chatNotificationData: ChatNotificationData = {
    //         message: response,
    //         userId: 5,
    //         chatRoomId: 1,
    //         hidden: false,
    //         createdAt: getNow(),
    //         updatedAt: getNow(),
    //     };

    //     await devRepository.createChatMessage(chatNotificationData);
    //     sockets.EmitGlobalChatMessage({
    //         ...chatNotificationData,
    //         user: {
    //             id: 5,
    //             avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/avatars/haruka.webp",
    //             username: "Haruka Ito",
    //             createdAt: "2024-04-17T23:47:02.000Z",
    //             userType: "admin",
    //             level: 30,
    //         },
    //     });
    // }

    // return { data: response };
};

export const RemoveAllEffects = async (userId: number) => {
    await devRepository.removeEffects(userId);
    return { data: "Success" };
};

export const AddRandomEffects = async (userId: number) => {
    try {
        // Import required modules
        const { db } = await import("../../lib/db.js");
        const UserHelper = await import("../user/user.helpers.js");

        // Get the user
        const user = await UserHelper.GetUserByIdWithAssociations(userId);
        if (!user) {
            return { error: "User not found" };
        }

        // Get 4 random status effects
        const randomEffects = await db.status_effect.findMany({
            where: { disabled: false },
            orderBy: { id: "asc" },
            take: 100,
        });

        if (!randomEffects || randomEffects.length === 0) {
            return { error: "No status effects found" };
        }

        // Shuffle the array to get random effects
        const shuffled = [...randomEffects].sort(() => 0.5 - Math.random());
        const selectedEffects = shuffled.slice(0, 4);

        // Apply each effect to the user
        const appliedEffects = [];
        for (const effect of selectedEffects) {
            const result = await StatusEffectService.ApplyStatusEffectToUser(user, effect);
            if (result) {
                appliedEffects.push({
                    name: effect.name,
                    tier: effect.tier,
                    duration: effect.duration,
                });
            }
        }

        return {
            data: {
                message: `Applied ${appliedEffects.length} random status effects to user`,
                effects: appliedEffects,
            },
        };
    } catch (error) {
        logger.error("Error adding random effects: " + error);
        return { error: "Error adding random effects" };
    }
};

export const AddItem = async (userId: number, itemId: number, quantity = 1) => {
    const itemExists = await ItemRepository.findItemById(itemId);
    if (!itemExists) {
        return { error: `Item with ID ${itemId} not found`, statusCode: 404 };
    }

    // Add the item to the user's inventory
    await InventoryService.AddItemToUser({
        userId,
        itemId,
        amount: quantity,
        isTradeable: true,
    });

    return {
        data: {
            message: `Added ${quantity} of item '${itemExists.name}' to user inventory`,
            item: {
                id: itemExists.id,
                name: itemExists.name,
                quantity,
            },
        },
    };
};

export const HatchEggs = async (userId: number) => {
    const eggs = await devRepository.findUserEggs(userId);
    if (!eggs || eggs.length === 0) {
        return { error: "No eggs found" };
    }

    for (const egg of eggs) {
        const evolutionData = {
            current: "egg",
            next: "baby",
            progress: 100,
            requiredEggProgress: 100,
        };

        await devRepository.updatePetProgress(userId, egg.id, evolutionData);
        await evolvePet(userId, egg.id);
    }
    return { data: eggs };
};

export const SetAllPetsHappiness = async (userId: number) => {
    try {
        const result = await devRepository.setAllPetsHappiness(userId);
        return {
            data: {
                message: `Set happiness to 100 for ${result.count} pets`,
                petsUpdated: result.count,
            },
        };
    } catch (error) {
        LogErrorStack({ error });
        return { error: error instanceof Error ? error.message : String(error), statusCode: 500 };
    }
};

export const AddXpToAllPets = async (userId: number, xpAmount = 100) => {
    try {
        const pets = await devRepository.findUserPets(userId);
        const updatedPets = [];

        for (const pet of pets) {
            await addPetXp(pet, xpAmount);
            updatedPets.push(pet);
        }

        return {
            data: {
                message: `Added ${xpAmount} XP to ${updatedPets.length} pets`,
                petsUpdated: updatedPets.length,
            },
        };
    } catch (error) {
        LogErrorStack({ error });
        return { error: error instanceof Error ? error.message : String(error), statusCode: 500 };
    }
};

export const DeleteExploreNodes = async (userId: number) => {
    try {
        const result = await devRepository.deleteExploreNodes(userId);
        return {
            data: {
                message: `Deleted ${result.count} explore nodes for user`,
                nodesDeleted: result.count,
            },
        };
    } catch (error) {
        LogErrorStack({ error });
        return { error: error instanceof Error ? error.message : String(error), statusCode: 500 };
    }
};
