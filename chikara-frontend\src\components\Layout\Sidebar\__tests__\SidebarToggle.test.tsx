import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import SidebarToggle from "../SidebarToggle";

describe("SidebarToggle", () => {
    it("renders with correct icon when collapsed", () => {
        const mockOnToggle = vi.fn();
        render(<SidebarToggle isCollapsed={true} onToggle={mockOnToggle} />);
        
        const button = screen.getByRole("button");
        expect(button).toBeInTheDocument();
        expect(button).toHaveAttribute("title", "Expand sidebar");
    });

    it("renders with correct icon when expanded", () => {
        const mockOnToggle = vi.fn();
        render(<SidebarToggle isCollapsed={false} onToggle={mockOnToggle} />);
        
        const button = screen.getByRole("button");
        expect(button).toBeInTheDocument();
        expect(button).toHaveAttribute("title", "Collapse sidebar");
    });

    it("calls onToggle when clicked", () => {
        const mockOnToggle = vi.fn();
        render(<SidebarToggle isCollapsed={false} onToggle={mockOnToggle} />);
        
        const button = screen.getByRole("button");
        fireEvent.click(button);
        
        expect(mockOnToggle).toHaveBeenCalledTimes(1);
    });
});
