import gameConfig from "../../config/gameConfig.js";
import * as AchievementHelpers from "../../core/achievement.service.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as NotificationService from "../../core/notification.service.js";
import * as StatusEffectService from "../../core/statuseffect.service.js";
import * as UserService from "../../core/user.service.js";
import * as CreatureHelpers from "../creature/creature.helpers.js";
import * as UniqueItemHelpers from "../item/uniqueitem.helpers.js";
import { ENCOUNTER_LOCATIONS, ENCOUNTER_TYPES, ROGUELIKE_BATTLE_TYPES } from "./roguelike.constants.js";
import * as RogueLikeHelpers from "./roguelike.helpers.js";
import type { CharacterDialogue, MapType } from "./roguelike.types.js";
import * as ShrineHelper from "../shrine/shrine.helpers.js";
import * as TalentHelper from "../talents/talents.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import type { ItemModel, StatusEffectModel, UserModel } from "../../lib/db.js";
import { NotificationTypes } from "../../types/notification.js";
import { LogErrorStack } from "../../utils/log.js";
import { emitItemDropped, emitEncounterCompleted } from "../../core/events/index.js";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";

const { ACTION_POINTS_REQUIRED, SCAVENGE_TIMEOUT_MS, DEFAULT_ENCOUNTER_JAIL_DURATION_MS, MAX_BUFF_VALUE_CAP } =
    gameConfig;

export const newRun = async (userId: number, level: number, location: ENCOUNTER_LOCATIONS) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found" };
    }
    let map = currentUser.roguelikeMap;
    if (map && !map.mapComplete) {
        return { error: "Already in a run" };
    }

    if (!Object.values(ENCOUNTER_LOCATIONS).includes(location)) {
        return { error: "Invalid location" };
    }

    if (!level) {
        return { error: "Missing level" };
    }

    if (level < 1 || level > currentUser.roguelikeHighscore) {
        return { error: "Invalid level" };
    }

    const currentMapComplete = map && map.mapComplete;

    const buffs = {
        strBuff: currentMapComplete ? (map?.strBuff ?? 1) : 1,
        defBuff: currentMapComplete ? (map?.defBuff ?? 1) : 1,
        dexBuff: currentMapComplete ? (map?.dexBuff ?? 1) : 1,
    };

    map = RogueLikeHelpers.GenerateMap({ hasScavengeNodes: currentUser.roguelikeLevel >= 15, buffs, location });

    await UserService.updateUser(currentUser.id, { roguelikeLevel: level, roguelikeMap: map });

    logAction({
        action: "ROGUELIKE_RUN_STARTED",
        userId: currentUser.id,
        info: {
            location: location,
            level: level,
        },
    });

    return { data: map };
};

export const getMap = async (userId: number) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found" };
    }
    return { data: currentUser.roguelikeMap };
};

export const Advance = async (userId: number, selectedNode: number) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found" };
    }

    const map = currentUser.roguelikeMap;

    if (!map) {
        return { error: "Map not found" };
    }

    if (!map.currentNodeComplete) {
        return { error: "You must complete the current encounter before advancing" };
    }

    const currentNode = map.nodes[map.playerLocation];
    if (!currentNode || !currentNode.edges) {
        return { error: "Invalid current node or node does not have edges" };
    }

    if (!currentNode.edges.includes(selectedNode)) {
        return { error: "Invalid node selected" };
    }

    if (currentUser.actionPoints < ACTION_POINTS_REQUIRED) {
        return { error: "Not enough action points" };
    }

    map.playerLocation = selectedNode;
    map.currentNodeComplete = false;
    map.currentNodeDialogue = null;
    const freeMovementTalent = await TalentHelper.UserHasFreeMovementTalent(currentUser.id);
    const skipApCost = freeMovementTalent && Math.random() < (freeMovementTalent.modifier ?? 0);

    const updateValues: { roguelikeMap: MapType; actionPoints?: number } = {
        roguelikeMap: map,
    };

    if (!skipApCost) {
        updateValues.actionPoints = currentUser.actionPoints - ACTION_POINTS_REQUIRED;
    }

    await UserService.updateUser(currentUser.id, updateValues);

    return { data: map };
};

const ApplyEncounterReward = async (dialogue: CharacterDialogue, currentUser: UserModel) => {
    if (dialogue.isItemDrop) {
        const droppedItemId = await RogueLikeHelpers.GetDropId(currentUser);

        if (droppedItemId && droppedItemId !== 0) {
            await InventoryService.AddItemToUser({
                userId: currentUser.id,
                itemId: droppedItemId,
                amount: 1,
                isTradeable: true,
            });
            const droppedItem = await ItemRepository.findItemById(droppedItemId);
            if (!droppedItem) {
                throw new Error("Item not found");
            }
            dialogue.rewards = droppedItem;

            // Emit item dropped event
            await emitItemDropped({
                userId: currentUser.id,
                itemId: droppedItemId,
                quantity: 1,
                source: "encounter",
            });

            logAction({
                action: "CHARACTER_ENCOUNTER_ITEM_DROP",
                userId: currentUser.id,
                info: {
                    itemId: droppedItemId,
                    itemName: droppedItem.name,
                },
            });

            return dialogue;
        }
    }
    dialogue.isItemDrop = false;

    if ((await UniqueItemHelpers.IsWealthItemEquipped(currentUser)) && typeof dialogue.rewards === "number") {
        dialogue.rewards = Math.round(dialogue.rewards * 1.25);
    }

    if (typeof dialogue.rewards === "number") {
        const updatedCash = currentUser.cash + dialogue.rewards;
        await UserService.updateUser(currentUser.id, { cash: updatedCash });

        logAction({
            action: "CHARACTER_ENCOUNTER_CASH_REWARD",
            userId: currentUser.id,
            info: {
                rewards: dialogue.rewards,
            },
        });
    }

    return dialogue;
};

interface ScavengeDetails {
    itemReward?: Omit<ItemModel, "createdAt" | "updatedAt">;
    itemQuantity?: number;
    success?: boolean;
    choice?: string;
    jailed?: boolean;
    jailDuration?: number;
    injury?: StatusEffectModel;
}

export const ChooseScavengeOption = async (userId: number, choice: number) => {
    if (choice !== 1 && choice !== 2) {
        return { error: "Invalid choice" };
    }

    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found" };
    }
    const map = currentUser.roguelikeMap;

    if (!map) {
        return { error: "Map not found" };
    }

    if (!map.currentNodeChoices) {
        return { error: "You must be on a scavenge node to choose an option" };
    }

    const selectedChoice = map.currentNodeChoices[choice - 1];

    const failChance = 0.3; // 30% chance of bad outcome
    const goodOutcome: boolean = Math.random() <= 1 - failChance;

    const details: ScavengeDetails | null = await (goodOutcome
        ? RogueLikeHelpers.GetScavengeItemDrop(currentUser, map, selectedChoice)
        : RogueLikeHelpers.GetScavengeBadOutcome(currentUser, map, selectedChoice));

    if (details) {
        details.success = goodOutcome;
        details.choice = selectedChoice;
    }

    map.currentNodeComplete = true;
    map.currentNodeChoices = null;

    await UserService.updateUser(currentUser.id, { roguelikeMap: map });

    return { data: details };
};

export const ActivateNode = async (userId: number) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found" };
    }
    const map = currentUser.roguelikeMap;

    if (!map) {
        return { error: "Map not found" };
    }

    const currentNode = map.nodes[map.playerLocation];

    if (map.currentNodeComplete || map.mapComplete) {
        return { error: "Already complete" };
    }

    let info:
        | {
              currentNodeChoices?: string[];
              scavengeValidUntil?: number;
              strBuff?: number;
              defBuff?: number;
              dexBuff?: number;
              hospitalised?: boolean;
              jailed?: boolean;
          }
        | CharacterDialogue = {};

    const updateValues: {
        scavengeValidUntil?: Date;
        currentHealth?: number;
        cash?: number;
        jailedUntil?: bigint;
        jailReason?: string;
        roguelikeMap?: MapType;
    } = {};

    switch (currentNode.encounterType) {
        case ENCOUNTER_TYPES.BATTLE: {
            const error = await CreatureHelpers.startRoguelikeNPCBattle(
                currentUser,
                ROGUELIKE_BATTLE_TYPES.NORMAL,
                map.location,
                currentUser.roguelikeLevel
            );
            if (error) {
                return { error };
            }
            map.currentNodeComplete = true;
            break;
        }
        case ENCOUNTER_TYPES.BOSS: {
            const error = await CreatureHelpers.startRoguelikeNPCBattle(
                currentUser,
                ROGUELIKE_BATTLE_TYPES.BOSS,
                map.location,
                currentUser.roguelikeLevel
            );
            if (error) {
                return { error };
            }
            map.currentNodeComplete = true;
            map.mapComplete = true;
            break;
        }
        case ENCOUNTER_TYPES.SCAVENGE: {
            const scavengeChoices = ["trash", "medical", "upgrade", "herb", "tech", "ore"];

            // Get the first random choice and remove it from the array
            const scavengeChoice1Index: number = Math.floor(Math.random() * scavengeChoices.length);
            const scavengeChoice1 = scavengeChoices[scavengeChoice1Index];
            scavengeChoices.splice(scavengeChoice1Index, 1); // Remove the chosen item

            // Get the second random choice from the remaining choices
            const scavengeChoice2 = scavengeChoices[Math.floor(Math.random() * scavengeChoices.length)];

            const scavengeValidUntil = Date.now() + SCAVENGE_TIMEOUT_MS;

            // updateValues.scavengeValidUntil = new Date(scavengeValidUntil);
            map.currentNodeChoices = [scavengeChoice1, scavengeChoice2];
            info.currentNodeChoices = map.currentNodeChoices;
            info.scavengeValidUntil = scavengeValidUntil;
            break;
        }
        case ENCOUNTER_TYPES.BUFF: {
            map.currentNodeComplete = true;
            const buffValues: number[] = [0.05, 0.06, 0.07, 0.08, 0.09, 0.1, 0.11, 0.12];
            const buffAmount: number = buffValues[Math.floor(Math.random() * buffValues.length)];
            const buffStatOptions = ["defBuff", "strBuff", "dexBuff"] as const;
            const buffStat = buffStatOptions[Math.floor(Math.random() * buffStatOptions.length)];
            map[buffStat] += buffAmount;
            if (map[buffStat] > MAX_BUFF_VALUE_CAP) {
                map[buffStat] = MAX_BUFF_VALUE_CAP;
            }
            info[buffStat] = buffAmount;

            logAction({
                action: "ROGUELIKE_BUFF",
                userId: currentUser.id,
                info: {
                    buffAmount: buffAmount,
                    buffStat: buffStat,
                },
            });
            break;
        }
        case ENCOUNTER_TYPES.CHARACTER: {
            map.currentNodeComplete = true;

            emitEncounterCompleted({ userId: currentUser.id, encounterId: 0, location: map.location });

            const mugChance: number = currentUser.roguelikeHighscore < 4 ? 0 : 0.2; // 20% chance of being mugged
            const goodOutcome: boolean = Math.random() <= 1 - mugChance;

            let dialogue: CharacterDialogue = RogueLikeHelpers.GetCharacterDialogue(
                goodOutcome,
                currentUser.roguelikeLevel
            );
            if (dialogue.character === "Apollo") {
                // 30% heal
                const heal: number = currentUser.health * 0.3;
                updateValues.currentHealth = Math.min(currentUser.currentHealth + heal, currentUser.health);
                map.currentNodeDialogue = dialogue;

                logAction({
                    action: "CHARACTER_ENCOUNTER_HEALED",
                    userId: currentUser.id,
                    info: {
                        heal: heal,
                    },
                });
                break;
            }
            if (dialogue.rewards) {
                if (goodOutcome) {
                    dialogue = await ApplyEncounterReward(dialogue, currentUser);
                } else if (typeof dialogue.rewards === "number") {
                    const mugAmount: number = Math.max(0, currentUser.cash - dialogue.rewards);
                    dialogue.rewards = Math.min(dialogue.rewards, currentUser.cash);
                    updateValues.cash = mugAmount;

                    logAction({
                        action: "CHARACTER_ENCOUNTER_MUGGED",
                        userId: currentUser.id,
                        info: {
                            rewards: dialogue.rewards,
                        },
                    });
                }
            }
            info = dialogue;
            map.currentNodeDialogue = dialogue;

            if (!goodOutcome) {
                const escapeArtistTalent = await TalentHelper.UserHasEscapeArtistTalent(currentUser.id);
                const jailChance: number = escapeArtistTalent ? 0.15 * (escapeArtistTalent.modifier ?? 1) : 0.15;
                if (Math.random() <= 0.05) {
                    // if mugged, 5% chance to be hospitalised
                    info.hospitalised = true;

                    const injury = await StatusEffectService.GetRandomInjury("Minor");
                    if (injury) {
                        await StatusEffectService.ApplyStatusEffectToUser(currentUser, injury);

                        NotificationService.NotifyUser(currentUser.id, NotificationTypes.hospitalised, {
                            reason: "character_encounter",
                            dialogue: dialogue,
                            injury: injury.name,
                            injuryTier: injury.tier,
                        });

                        logAction({
                            action: "CHARACTER_ENCOUNTER_INJURED",
                            userId: currentUser.id,
                            info: {
                                injury: injury.name,
                                injuryTier: injury.tier,
                            },
                        });
                    }
                } else if (1 - Math.random() < jailChance) {
                    // if mugged and not hospitalised, 15% chance of being jailed
                    const jailShrineBuffActive = (await ShrineHelper.dailyBuffIsActive("jail", currentUser.id)) || 1;
                    info.jailed = true;
                    updateValues.jailedUntil = BigInt(
                        Date.now() + DEFAULT_ENCOUNTER_JAIL_DURATION_MS * jailShrineBuffActive
                    );
                    updateValues.jailReason = "roguelike";

                    logAction({
                        action: "CHARACTER_ENCOUNTER_JAILED",
                        userId: currentUser.id,
                        info: {
                            jailDuration: DEFAULT_ENCOUNTER_JAIL_DURATION_MS * jailShrineBuffActive,
                        },
                    });
                }
            }
            break;
        }
        default: {
            LogErrorStack({ error: "Invalid node type activation: " + currentNode.encounterType });
            return { error: "Invalid node" };
        }
    }

    map.nodes[map.playerLocation].complete = true;
    updateValues.roguelikeMap = map;
    await UserService.updateUser(currentUser.id, updateValues);
    await AchievementHelpers.UpdateUserAchievement(currentUser.id, "roguelikeNodesCompleted");

    return { data: { info: info } };
};
