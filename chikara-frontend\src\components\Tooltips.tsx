import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import { cn } from "@/lib/utils";
import ItemTooltipContent from "./Tooltips/Item/ItemTooltipContent";

function Tooltips() {
    // "use no memo";
    return (
        <>
            <QuestRewardTooltip />
            <StatusEffectTooltip />
            <DefaultTooltip />
            <StatBarTooltip />
            <ShrineBuffTooltip />
            <DateTooltip />
            <ItemTooltip />
        </>
    );
}

export default Tooltips;

const ItemTooltip = () => {
    return (
        <Tooltip
            id="item-tooltip"
            className="z-200 border border-gray-600/50"
            opacity="1"
            style={{
                backgroundColor: "rgba(7, 6, 7, 0.97)",
                color: "#FFF",
                padding: 0,
            }}
            render={({ content, activeAnchor }) => {
                const item = activeAnchor?.getAttribute("data-tooltip-item") || null;
                let parsedItem = null;
                if (item) {
                    try {
                        parsedItem = JSON.parse(item);
                    } catch (e) {
                        console.error("Failed to parse item data:", e);
                    }
                }
                return <ItemTooltipContent item={parsedItem} />;
            }}
        />
    );
};

const DefaultTooltip = () => {
    return (
        <Tooltip
            id="default-tooltip"
            className="z-300"
            opacity="0.85"
            render={({ content }) => <span>{content}</span>}
            style={{
                backgroundColor: "rgba(7, 6, 7, 1)",
                color: "#FFF",
                marginTop: "5px",
            }}
        />
    );
};

const DateTooltip = () => {
    return (
        <Tooltip
            id="date-tooltip"
            className="z-200"
            opacity="0.85"
            render={({ content }) => <span>{content}</span>}
            style={{
                backgroundColor: "rgba(7, 6, 7, 1)",
                color: "#FFF",
            }}
        />
    );
};

const StatBarTooltip = () => {
    return (
        <Tooltip
            id="statbar-tooltip"
            className="z-200 border border-gray-600/50"
            opacity="1"
            style={{
                backgroundColor: "rgba(7, 6, 7, 0.97)",
                color: "#FFF",
                padding: 0,
            }}
            render={({ content, activeAnchor }) => (
                <div className="flex h-full w-40 flex-col px-4 py-2 text-center">
                    <p className={cn("text-blue-500 text-sm")}>{activeAnchor?.getAttribute("data-tooltip-statname")}</p>
                    {activeAnchor?.getAttribute("data-tooltip-statamount") && (
                        <p className="break-words">
                            <span className="text-custom-yellow">
                                {activeAnchor?.getAttribute("data-tooltip-statamount")}
                            </span>{" "}
                            {activeAnchor?.getAttribute("data-tooltip-statdesc")}
                        </p>
                    )}
                    {!activeAnchor?.getAttribute("data-tooltip-statamount") && (
                        <p className="break-words text-xs">{activeAnchor?.getAttribute("data-tooltip-statdesc")}</p>
                    )}
                </div>
            )}
        />
    );
};

const ShrineBuffTooltip = () => {
    return (
        <Tooltip
            id="shrinebuff-tooltip"
            className="z-200 border border-gray-600/50"
            opacity="1"
            style={{
                backgroundColor: "rgba(7, 6, 7, 0.97)",
                color: "#FFF",
                padding: 0,
            }}
            render={({ activeAnchor }) => {
                const buff1Name = activeAnchor?.getAttribute("data-tooltip-buff1name");
                const buff1Value = activeAnchor?.getAttribute("data-tooltip-buff1value");
                const buff1Desc = activeAnchor?.getAttribute("data-tooltip-buff1desc");
                const buff1Primary = activeAnchor?.getAttribute("data-tooltip-buff1primary") === "true";

                const buff2Name = activeAnchor?.getAttribute("data-tooltip-buff2name");
                const buff2Value = activeAnchor?.getAttribute("data-tooltip-buff2value");
                const buff2Desc = activeAnchor?.getAttribute("data-tooltip-buff2desc");
                const buff2Primary = activeAnchor?.getAttribute("data-tooltip-buff2primary") === "true";

                return (
                    <div className="w-64 px-4 py-2">
                        <p className="pb-2 text-center text-base text-custom-yellow">Active Shrine Buffs:</p>
                        <div className="space-y-2">
                            {buff1Name && (
                                <div className="text-sm">
                                    <div className="flex items-center justify-between">
                                        <span
                                            className={cn(
                                                "font-medium",
                                                buff1Primary ? "text-yellow-400" : "text-blue-400"
                                            )}
                                        >
                                            {buff1Name}
                                        </span>
                                        <span className="text-green-400 font-medium">{buff1Value}</span>
                                    </div>
                                    {buff1Desc && <p className="text-xs text-gray-300 mt-1">{buff1Desc}</p>}
                                </div>
                            )}

                            {buff2Name && (
                                <div className="text-sm">
                                    <div className="flex items-center justify-between">
                                        <span
                                            className={cn(
                                                "font-medium",
                                                buff2Primary ? "text-yellow-400" : "text-blue-400"
                                            )}
                                        >
                                            {buff2Name}
                                        </span>
                                        <span className="text-green-400 font-medium">{buff2Value}</span>
                                    </div>
                                    {buff2Desc && <p className="text-xs text-gray-300 mt-1">{buff2Desc}</p>}
                                </div>
                            )}
                        </div>
                    </div>
                );
            }}
        />
    );
};

const StatusEffectTooltip = () => {
    return (
        <Tooltip
            id="statuseffect-tooltip"
            className="z-400 mt-2 border border-gray-600/50 md:mt-0"
            opacity="1"
            style={{
                backgroundColor: "rgba(7, 6, 7, 0.97)",
                color: "#FFF",
                padding: 0,
            }}
            render={({ content, activeAnchor }) => {
                const effectName = activeAnchor?.getAttribute("data-effect-name");
                const effectDescription = activeAnchor?.getAttribute("data-effect-description");
                const effectTurns = activeAnchor?.getAttribute("data-effect-turns");
                const effectTier = activeAnchor?.getAttribute("data-effect-tier");
                const effectModifier = activeAnchor?.getAttribute("data-effect-modifier");
                const effectAmount = activeAnchor?.getAttribute("data-effect-amount");
                const effectEndsAt = activeAnchor?.getAttribute("data-effect-endsat");
                const effectSource = activeAnchor?.getAttribute("data-effect-source");

                return (
                    <div className="flex h-full w-40 flex-col px-4 py-2 text-center">
                        {(effectTier || effectSource) && (
                            <p className="text-red-500 text-xs uppercase leading-none">
                                {effectTier}
                                {effectSource && (
                                    <span className="ml-1 text-indigo-400">
                                        {effectSource.length > 7
                                            ? effectSource.substring(0, effectSource.length - 7)
                                            : effectSource}
                                    </span>
                                )}
                            </p>
                        )}
                        {effectName && (
                            <p className="my-1 text-base text-custom-yellow leading-none">
                                <span className="mr-0.5 text-sm text-yellow-200">
                                    {effectAmount ? `${effectAmount}x ` : ""}
                                </span>
                                {effectName}
                            </p>
                        )}
                        {effectModifier && <p className="text-indigo-300 text-sm text-stroke-sm">{effectModifier}</p>}
                        {effectDescription && <p className="text-red-500 text-sm">{effectDescription}</p>}
                        {effectTurns && (
                            <p className="break-words">
                                <span className="text-custom-yellow">{effectTurns}</span> turn
                                {Number(effectTurns) > 1 && "s"} remaining
                            </p>
                        )}
                        {effectEndsAt && (
                            <div>
                                <hr className="mt-1.5 mb-1 border-gray-600/75" />
                                <p className="text-base text-custom-yellow">{effectEndsAt}</p>
                            </div>
                        )}
                    </div>
                );
            }}
        />
    );
};

const QuestRewardTooltip = () => {
    return (
        <Tooltip
            id="questreward-tooltip"
            className="z-300"
            opacity="0.85"
            render={({ content }) => <span>{content}</span>}
            style={{
                backgroundColor: "rgba(7, 6, 7, 1)",
                color: "#FFF",
                marginTop: "5px",
                fontSize: "1rem",
            }}
        />
    );
};
