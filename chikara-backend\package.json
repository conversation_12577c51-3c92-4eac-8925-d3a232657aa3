{"name": "chikara-backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "bun ./src/server.ts", "dev": "bun --watch --no-clear-screen ./src/server.ts", "build": "tsc && tsc-alias", "seed": "bun run reset && bun prisma/seed.ts", "alpha-seed": "bun run reset && bun prisma/seeders/alphaSeed.ts", "migrate": "npx prisma migrate dev", "reset": "npx prisma db push --force-reset", "fetchJSON": "node ./prisma/seeders/downloadJson.js", "test": "vitest run", "test:watch": "vitest", "coverage": "vitest run --coverage", "test:clear": "vitest --clear-cache", "lint": "npx eslint src/**/*.js ./src/server.ts", "format": "prettier --write .", "type-check": "tsc --noEmit && tsc-alias --noEmit", "generate": "prisma generate"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.848.0", "@aws-sdk/s3-request-presigner": "^3.848.0", "@date-fns/utc": "^2.1.0", "@logtail/node": "^0.5.5", "@logtail/winston": "^0.5.5", "@orpc/server": "^1.7.4", "@prisma/client": "6.12.0", "@xata.io/client": "^0.30.1", "adm-zip": "0.5.14", "better-auth": "^1.3.2", "bullmq": "^5.56.5", "cors": "^2.8.5", "date-fns": "^4.1.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "firebase-admin": "^13.4.0", "http-graceful-shutdown": "^3.1.14", "mathjs": "^14.5.3", "ms": "^2.1.3", "multer": "^2.0.2", "nodemailer": "^7.0.5", "openai": "^5.10.1", "prisma-json-types-generator": "^3.5.1", "redis": "^5.6.0", "request-ip": "^3.3.0", "sharp": "^0.34.3", "socket.io": "^4.8.1", "winston": "^3.17.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/bun": "^1.2.19", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/ms": "~2.1.0", "@types/multer": "~2.0.0", "@types/node": "^24.0.15", "@types/nodemailer": "~6.4.17", "@types/request-ip": "~0.0.41", "@typescript/native-preview": "^7.0.0-dev.20250721.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/eslint-plugin": "^1.3.4", "bun": "^1.2.19", "eslint": "^9.31.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import-x": "^4.16.1", "eslint-plugin-n": "^17.21.0", "eslint-plugin-unicorn": "^59.0.1", "globals": "^16.3.0", "prisma": "6.12.0", "tsc-alias": "^1.8.16", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "vitest": "^3.2.4", "vitest-mock-extended": "^3.1.0"}, "prisma": {"seed": "bun prisma/seed.ts", "schema": "prisma/schema.prisma"}, "engines": {"node": ">=21.2.0"}, "trustedDependencies": ["bun", "sharp", "@contrast/fn-inspect", "@firebase/util", "@prisma/client", "@prisma/engines", "@sentry/cli", "core-js", "esbuild", "msgpackr-extract", "prisma", "protobufjs", "unrs-resolver"]}