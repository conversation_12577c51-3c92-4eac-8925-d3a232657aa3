import { GetDamage } from "./battle.damage.js";
import { BattlePlayer, PlayerEquipment } from "../types/battle.types.js";
import * as TalentHelper from "../../talents/talents.helpers.js";
import * as UniqueItemHelpers from "../../item/uniqueitem.helpers.js";
import { GetAmmoForUser, getEquippedItemDetails } from "./battle.equipment.js";
import {
    GetShieldBashDamage,
    GetSprayDamage,
    GetGiantKillingSlingshotDamage,
    GetHeadbuttDamage,
    ApplySelfHarmDamage,
    STATUS_ABILITIES,
} from "../logic/battle.abilities.js";
import { getHealingImprovement, getImpactResistance } from "./battle.scaling.js";
import gameConfig from "../../../config/gameConfig.js";
import {
    HEADBUTT_ABILITY_NAME,
    HEAL_ABILITY_NAME,
    MAX_HP_HEAL_ABILITY_NAME,
    SELF_HARM_ABILITY_NAME,
    SHIELD_BASH_ABILITY_NAME,
    RELOAD_ABILITY_NAME,
    SPRAY_ABILITY_NAME,
    GIANT_KILLING_SLINGSHOT_ABILITY_NAME,
    ATTACK_TYPE_MELEE,
    ATTACK_TYPE_RANGED,
} from "./battle.constants.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import { handleError } from "../../../utils/log.js";

// Define the action result type for actions that need to return additional metadata
export interface ActionResult {
    damage: number;
    metadata?: {
        fleeAttempt?: {
            success: boolean;
        };
    };
}

const processBasicAttack = async (user: BattlePlayer, target: BattlePlayer) => {
    return await GetDamage(user, target, ATTACK_TYPE_MELEE);
};

const processRangedAttack = async (user: BattlePlayer, target: BattlePlayer) => {
    if ((user.ammo ?? 0) <= 0) {
        return handleError("Not enough ammo", 400);
    }

    if (!getEquippedItemDetails(user, "ranged")) {
        return handleError("No ranged weapon equipped", 400);
    }

    user.ammo--;

    return await GetDamage(user, target, ATTACK_TYPE_RANGED);
};

const processAbilityAction = async (user: BattlePlayer, target: BattlePlayer, action: string) => {
    // Ability validation - only for human players
    if (user.userType === "player") {
        const ability = user.abilities?.find((ab) => ab.name === action);

        if (!ability) {
            return handleError("Ability not equipped or available.", 400);
        }
        if (user.statusEffects["ability_lock"] || user.statusEffects["ability_lock_debuff"]) {
            return handleError("Ability locked!", 400);
        }

        // Handle ability efficiency - only for human players
        let abilityEfficiencyActive = false;
        const abilityEfficiencyTalent =
            user.userType === "player"
                ? await TalentHelper.UserHasAbilityEfficiencyTalent(Number.parseInt(user.id))
                : null;
        if (abilityEfficiencyTalent) {
            const statusEffects = user.statusEffects;

            // Initialize lastCast and lastCastAmount if they don't exist
            if (!statusEffects["lastCast"]) {
                statusEffects["lastCast"] = { name: "" };
            }
            if (!statusEffects["lastCastAmount"]) {
                statusEffects["lastCastAmount"] = { value: 0 };
            }

            if (statusEffects["lastCast"].name === action) {
                if (statusEffects["lastCastAmount"].value === 1) {
                    statusEffects["lastCastAmount"].value = 2;
                } else {
                    abilityEfficiencyActive = true;
                    delete statusEffects["lastCast"];
                    delete statusEffects["lastCastAmount"];
                }
            } else {
                statusEffects["lastCast"].name = action;
                statusEffects["lastCastAmount"].value = 1;
            }
            user.statusEffects = statusEffects;
        }

        // Calculate stamina cost with ability efficiency - only for human players
        const staminaCost =
            ability.staminaCost * (abilityEfficiencyActive ? (abilityEfficiencyTalent?.modifier ?? 1) : 1);

        if (staminaCost && staminaCost > user.currentStamina) {
            return handleError("Not enough stamina", 400);
        }

        user.currentStamina -= staminaCost;
    } else {
        // For NPC/AI players, just check if they have enough stamina for the action
        const ability = user.abilities?.find((ab) => ab.name === action);
        if (!ability) {
            return handleError("Invalid ability", 400);
        }

        const baseCost = ability.staminaCost;
        if (baseCost && baseCost > user.currentStamina) {
            return handleError("Not enough stamina", 400);
        }

        user.currentStamina -= baseCost;
    }

    const abilityDmgDebuff = (user.statusEffects["ability_damage_debuff"] as number) || 0;

    // Process specific abilities
    const healthyCasterTalentActive =
        user.userType === "player" ? await TalentHelper.HealthyCasterTalentActiveForUser(user) : false;

    // Handle immediate effect abilities
    switch (action) {
        case HEADBUTT_ABILITY_NAME: {
            return GetHeadbuttDamage(target, healthyCasterTalentActive, abilityDmgDebuff, user.abilities);
        }

        case HEAL_ABILITY_NAME: {
            const healAbility = user.abilities?.find((ab) => ab.name === HEAL_ABILITY_NAME);
            if (!healAbility || !healAbility.currentModifier) return 0;

            applyPercentageHeal(user, healAbility.currentModifier);
            return 0;
        }

        case MAX_HP_HEAL_ABILITY_NAME: {
            const maxHealAbility = user.abilities?.find((ab) => ab.name === MAX_HP_HEAL_ABILITY_NAME);
            if (!maxHealAbility || !maxHealAbility.currentModifier) return 0;

            applyPercentageHeal(user, maxHealAbility.currentModifier);
            return 0;
        }

        case SHIELD_BASH_ABILITY_NAME: {
            return GetShieldBashDamage(user, healthyCasterTalentActive, abilityDmgDebuff, user.abilities);
        }

        case SELF_HARM_ABILITY_NAME: {
            // Apply self-inflicted damage; also applies a status effect further down.
            ApplySelfHarmDamage(user, user.abilities);
            break;
        }

        case RELOAD_ABILITY_NAME: {
            user.ammo = await GetAmmoForUser(user.id, user.equipment as PlayerEquipment);
            return 0;
        }

        case SPRAY_ABILITY_NAME: {
            const ammoToUse = user.ammo ?? 0;
            if (ammoToUse <= 0) {
                return handleError("Not enough ammo", 400);
            }
            if (!getEquippedItemDetails(user, "ranged")) {
                return handleError("No ranged weapon equipped", 400);
            }

            user.ammo = 0;
            // 30% extra damage per additional ammo used (1 ammo used = 1, 2 = 1.3)
            return GetSprayDamage(user, target, ammoToUse, abilityDmgDebuff, user.abilities);
        }

        case GIANT_KILLING_SLINGSHOT_ABILITY_NAME: {
            return GetGiantKillingSlingshotDamage(target, healthyCasterTalentActive, abilityDmgDebuff, user.abilities);
        }
    }

    // Handle status effect abilities
    for (const ability of STATUS_ABILITIES) {
        if (ability.name === action) {
            const targetOfAbility = ability.target === "self" ? user : target;
            const statusEffects = targetOfAbility.statusEffects;

            let finalTurns = ability.turns;

            // Apply impact resistance from intelligence for debuffs on players
            if (ability.target !== "self" && targetOfAbility.userType === "player") {
                const impactResistance = getImpactResistance(targetOfAbility.attributes.intelligence);
                finalTurns = Math.max(1, Math.round(ability.turns * (1 - impactResistance)));
            }

            statusEffects[ability.name] = { turns: finalTurns };
            targetOfAbility.statusEffects = statusEffects;
            return 0;
        }
    }

    return handleError("Invalid action", 400);
};

/**
 * Calculates the flee chance for a player based on equipped items and talents
 */
const calculateFleeChance = async (userId: string) => {
    let fleeChance = gameConfig.FLEE_CHANCE;
    const currentUser = await UserRepository.getUserById(Number.parseInt(userId));

    if (currentUser) {
        if (await UniqueItemHelpers.IsRunItemEquipped(currentUser)) {
            fleeChance += 0.2;
        }

        const cowardTalent = await TalentHelper.UserHasCowardTalent(Number.parseInt(userId));
        if (cowardTalent && typeof cowardTalent !== "boolean" && cowardTalent.modifier) {
            fleeChance += cowardTalent.modifier;
        }
    }

    return fleeChance;
};

/**
 * Processes a flee attempt. Returns 0 damage with flee success status.
 */
const processFlee = async (user: BattlePlayer): Promise<ActionResult> => {
    // NPCs cannot flee. Return a failed attempt.
    if (user.userType !== "player") {
        return {
            damage: 0,
            metadata: {
                fleeAttempt: {
                    success: false,
                },
            },
        };
    }

    const fleeChance = await calculateFleeChance(user.id);
    const fleeSuccess = Math.random() < fleeChance;

    // Return structured result with flee attempt metadata
    return {
        damage: 0,
        metadata: {
            fleeAttempt: {
                success: fleeSuccess,
            },
        },
    };
};

// --- Shared helper ---
const applyPercentageHeal = (user: BattlePlayer, percentage: number) => {
    const healAmount = Math.floor(user.maxHealth * percentage);

    // Apply vitality-based healing improvement (players only)
    const healingImprovement = user.userType === "player" ? getHealingImprovement(user.attributes.vitality) : 1;
    const improvedHeal = Math.round(healAmount * healingImprovement);

    user.currentHealth = Math.min(user.maxHealth, user.currentHealth + improvedHeal);
};

/**
 * Mapping of simple action strings to their respective handler functions.
 * Keeps ProcessAction concise and makes it easy to add/modify simple actions.
 */
// Adjust the handler type so that `target` is optional
type ActionHandler = (user: BattlePlayer, target?: BattlePlayer) => Promise<number | ActionResult>;

const actionHandlers: Record<string, ActionHandler> = {
    attack: (u, t) => processBasicAttack(u, t!),
    ranged: (u, t) => processRangedAttack(u, t!),
    flee: (u) => processFlee(u),
};

export const ProcessAction = async (
    user: BattlePlayer,
    target: BattlePlayer,
    action: string
): Promise<number | ActionResult> => {
    // Check if the action has a dedicated handler
    const handler = actionHandlers[action];
    if (handler) {
        return await handler(user, target);
    }

    // Fallback to ability processing
    return await processAbilityAction(user, target, action);
};
