import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from "../chat/chat.helpers.js";
import * as ShrineRepository from "../../repositories/shrine.repository.js";
import { UserModel } from "../../lib/db.js";
import { getToday } from "../../utils/dateHelpers.js";
import { logger } from "../../utils/log.js";
import gameConfig from "../../config/gameConfig.js";
import SHRINE_BUFFS from "../../data/shrineBuffs.js";

export const hasUserDonatedMinimumForSecondaryBuffs = async (userId: number, date: Date) => {
    const userDonation = await ShrineRepository.findUserDonationForDate(userId, date);
    if (!userDonation) {
        return false;
    }
    // Require at least the minimum donation amount for secondary buffs
    return userDonation.amount >= gameConfig.SHRINE_MINIMUM_DONATION;
};

export const dailyBuffIsActive = async (type: string, userId: number) => {
    const today = getToday();

    const [dailyShrine, userDonation] = await Promise.all([
        ShrineRepository.findDailyShrineGoalWithReached(today),
        ShrineRepository.findUserDonationForDate(userId, today),
    ]);

    if (!dailyShrine?.buffRewards?.[type]) {
        return null;
    }

    const buffData = dailyShrine.buffRewards[type];

    // Primary buffs are available to all players
    if (buffData.isPrimary) {
        return buffData.value;
    }

    // Secondary buffs require minimum donation
    const hasMinimumDonation = (userDonation?.amount ?? 0) >= gameConfig.SHRINE_MINIMUM_DONATION;
    return hasMinimumDonation ? buffData.value : null;
};

export const dailyShrineDonations = async (date: Date) => {
    return await ShrineRepository.getDailyDonations(date);
};

export const getDailyShrineGoal = async (date: Date) => {
    return await ShrineRepository.findDailyShrineGoal(date);
};

export const addToDailyShrineGoal = async (user: UserModel, amount: number) => {
    const today = getToday();
    const dailyShrineGoal = await getDailyShrineGoal(today);
    if (!dailyShrineGoal) {
        return false;
    }
    dailyShrineGoal.donationAmount += amount;

    const existingDonation = await ShrineRepository.findUserDonationForDate(user.id, today);

    if (existingDonation) {
        await ShrineRepository.updateDonation(existingDonation.id, existingDonation.amount + amount);
    } else {
        await ShrineRepository.createDonation(user.id, today, amount);
    }

    if (!dailyShrineGoal.goalReached && dailyShrineGoal.donationAmount >= dailyShrineGoal.donationGoal) {
        dailyShrineGoal.goalReached = true;

        await ChatHelper.SendAnnouncementMessage(
            "shrineGoalReached",
            `The Shrine daily donation goal has been reached. Global buffs are now active!`
        );
    }

    return await ShrineRepository.updateShrineGoal(dailyShrineGoal.id, {
        donationAmount: dailyShrineGoal.donationAmount,
        goalReached: dailyShrineGoal.goalReached,
    });
};

export const getRandomDailyBuffs = () => {
    const primaryBuff = SHRINE_BUFFS[Math.floor(Math.random() * SHRINE_BUFFS.length)];

    // Filter out the primary buff type to ensure different buff types
    const availableSecondaryBuffs = SHRINE_BUFFS.filter((buff) => buff.buffType !== primaryBuff.buffType);
    const secondaryBuff = availableSecondaryBuffs[Math.floor(Math.random() * availableSecondaryBuffs.length)];

    const primary = {
        buffType: primaryBuff.buffType,
        description: primaryBuff.description,
        value: primaryBuff.primaryValue,
        isPrimary: true,
    };

    const secondary = {
        buffType: secondaryBuff.buffType,
        description: secondaryBuff.description,
        value: secondaryBuff.secondaryValue,
        isPrimary: false,
    };

    return { [primary.buffType]: primary, [secondary.buffType]: secondary };
};

export const announceDonationGoalReset = async (donationGoal: number | string) => {
    try {
        await ChatHelper.SendAnnouncementMessage("shrineGoalReset", JSON.stringify({ goal: donationGoal }));
    } catch (error) {
        logger.error(`Failed to send chat message: ${error}`);
    }
};
