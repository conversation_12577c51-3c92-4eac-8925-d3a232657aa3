# Technology Stack

## Build System

- **Monorepo**: Turborepo with Bun package manager
- **Package Manager**: Bun 1.2.18+ (preferred over npm/yarn)
- **Workspaces**: 4 main applications (frontend, backend, admin-panel, landing)

## Frontend Stack

- **Framework**: React 19+ with TypeScript
- **Build Tool**: Vite 7+
- **Styling**: Tailwind CSS 4+ with Radix UI components
- **State Management**: Zustand, React Tracked
- **Data Fetching**: TanStack Query with ORPC client
- **Routing**: React Router DOM 7+
- **Real-time**: Socket.io client
- **Testing**: <PERSON><PERSON><PERSON>, Playwright for E2E

## Backend Stack

- **Runtime**: Bun (Node.js 21.2.0+ compatible)
- **Framework**: Express 5+ with TypeScript
- **Database**: MySQL with Prisma ORM 6.11+
- **Caching**: Redis 5+
- **Queue System**: BullMQ
- **Real-time**: Socket.io server
- **API**: ORPC for type-safe RPC
- **Authentication**: Better Auth
- **Testing**: Vitest with coverage

## Admin Panel

- **Framework**: React 19+ with TypeScript
- **UI**: Radix UI, shadcn/ui components
- **Data Tables**: TanStack Table
- **Charts**: Recharts
- **Build**: Vite

## Common Commands

### Development

```bash
# Start all services
bun dev

# Start specific service
bun dev:frontend
bun dev:backend
bun dev:admin

# Database operations
bun run migrate      # Run Prisma migrations
bun run seed         # Seed database
bun run alpha-seed   # Alpha environment seed
bun run reset        # Reset database
```

### Build & Deploy

```bash
# Build all
bun build

# Build specific
bun build:frontend
bun build:backend
bun build:admin
```

### Testing

```bash
# Run all tests
bun test

# Watch mode
bun test:watch

# Coverage (backend)
bun run coverage
```

### Code Quality

```bash
bun lint           # ESLint
bun format         # Prettier
bun type-check     # TypeScript
```

## Key Dependencies

- **oRPC**: Type-safe RPC between frontend/backend
- **Prisma**: Database ORM with MySQL
- **Socket.io**: Real-time communication
- **BullMQ**: Job queue system
- **Better Auth**: Authentication system
- **Tailwind CSS**: Utility-first styling
- **Radix UI**: Accessible component primitives
