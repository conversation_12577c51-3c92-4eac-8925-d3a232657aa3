import { cn } from "@/lib/utils";
import React from "react";

interface TabItem {
    value: string;
    label: string;
    icon: React.ElementType | string;
    color?: string;
    disabled?: boolean;
}

interface TabsProps {
    tabs: TabItem[];
    selectedTab: string;
    onTabChange: (value: string) => void;
    className?: string;
    children?: React.ReactNode;
}

export default function Tabs({ tabs, selectedTab, onTabChange, className, children }: TabsProps) {
    // Dynamic scaling based on number of tabs
    const getScaleClasses = (tabCount: number) => {
        // Mobile scaling
        const mobileIconSize = tabCount <= 3 ? "size-7" : tabCount <= 5 ? "size-6" : "size-5";
        const mobileTextSize = tabCount <= 7 ? "text-xl" : "text-lg";
        const mobilePadding = tabCount <= 3 ? "px-4 py-2.5" : tabCount <= 5 ? "px-3 py-2" : "px-2 py-1.5";

        // Desktop scaling
        const desktopIconSize = tabCount <= 4 ? "md:size-6" : tabCount <= 6 ? "md:size-5" : "md:size-4";
        const desktopTextSize = tabCount <= 6 ? "md:text-base" : "md:text-sm";
        const desktopPadding =
            tabCount <= 4 ? "md:px-4 md:py-2.5" : tabCount <= 6 ? "md:px-3 md:py-2" : "md:px-2.5 md:py-1.5";
        const desktopGap = tabCount <= 4 ? "md:gap-2.5" : tabCount <= 6 ? "md:gap-2" : "md:gap-1.5";

        return {
            iconSize: cn(mobileIconSize, desktopIconSize),
            textSize: cn(mobileTextSize, desktopTextSize),
            padding: cn(mobilePadding, desktopPadding),
            gap: desktopGap,
        };
    };

    const scaleClasses = getScaleClasses(tabs.length);

    return (
        <div className={cn("px-2 md:px-0 w-full", className)}>
            <div role="tablist" className="tabs tabs-boxed p-1 w-full">
                <div className="tabs tabs-box bg-base-200 p-1 w-full justify-center">
                    {tabs.map((tab) => {
                        const { icon, color } = tab;
                        const isStringIcon = typeof icon === "string";

                        return (
                            <>
                                <button
                                    key={tab.value}
                                    role="tab"
                                    title={tab.label}
                                    disabled={tab.disabled}
                                    className={cn(
                                        "tab transition-all",
                                        "flex-1 md:flex-initial mt-1 mb-2",
                                        scaleClasses.padding,
                                        scaleClasses.gap,
                                        selectedTab === tab.value ? "tab-active bg-base-300" : "hover:bg-base-300/50",
                                        tab.disabled && "cursor-not-allowed opacity-50"
                                    )}
                                    onClick={() => !tab.disabled && onTabChange(tab.value)}
                                >
                                    {isStringIcon ? (
                                        <span
                                            className={cn(
                                                scaleClasses.textSize,
                                                selectedTab === tab.value ? color : "opacity-50"
                                            )}
                                        >
                                            {icon}
                                        </span>
                                    ) : (
                                        React.createElement(icon as React.ElementType, {
                                            className: cn(
                                                scaleClasses.iconSize,
                                                selectedTab === tab.value ? color : "opacity-50"
                                            ),
                                        })
                                    )}
                                    <span
                                        className={cn(
                                            "hidden md:inline",
                                            scaleClasses.textSize,
                                            selectedTab === tab.value && "font-semibold"
                                        )}
                                    >
                                        {tab.label}
                                    </span>
                                </button>
                                {children && <div className="tab-content bg-base-300 border-base-300">{children}</div>}
                            </>
                        );
                    })}
                </div>
            </div>
        </div>
    );
}
