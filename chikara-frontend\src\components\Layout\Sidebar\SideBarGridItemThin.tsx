import arrow from "@/assets/images/UI/arrow.gif";
import NotificationBadge from "@/components/NotificationBadge";
import StrokedText from "@/components/StrokedText";
import { cn } from "@/lib/utils";
import { NavLink, useLocation } from "react-router-dom";
import { useNormalStore } from "../../../app/store/stores";

interface NavItem {
    name: string;
    href: string;
    icon: string;
    current?: string;
    external?: boolean;
    thin?: boolean;
    construction?: boolean;
}

interface SideBarGridItemThinProps {
    item: NavItem;
    inFight?: string | null;
    availableQuests?: number;
}

interface ButtonContentProps {
    item: NavItem;
    inFight?: string | null;
    checkCurrent: (name?: string) => boolean;
    availableQuests?: number;
    displayTutorialArrow?: boolean;
    craftCollectReady?: boolean;
}

const ButtonContent = ({
    item,
    inFight,
    checkCurrent,
    availableQuests,
    displayTutorialArrow = false,
    craftCollectReady,
}: ButtonContentProps) => {
    const tutorialArrow = item.name === "Tasks" && !checkCurrent(item.current) && displayTutorialArrow;

    return (
        <div className="relative w-full h-full flex items-center min-w-0">
            {tutorialArrow && (
                <img
                    className="-rotate-90 -translate-x-1/2 -top-20 absolute left-1/2 z-100 size-24 scale-75"
                    src={arrow}
                    alt=""
                />
            )}

            {/* Notification badges container */}
            <div className="absolute inset-0 pointer-events-none 2xl:hidden">
                {item.name === "Campus" && craftCollectReady && (
                    <NotificationBadge empty pulse className="absolute top-1 right-[5%] size-4" />
                )}

                {item.name === "Tasks" && availableQuests ? (
                    <NotificationBadge amount={availableQuests} className="h-5! w-5! absolute top-0.5 right-[5%]" />
                ) : null}
            </div>

            {/* Icon container - fixed width to prevent layout shift */}
            <div className="flex-shrink-0 w-12 h-full flex items-center justify-center 2xl:w-16">
                <img
                    src={item.icon}
                    alt=""
                    className={cn(
                        `h-[70%] max-h-8 w-auto group-hover:scale-105 transition-transform`,
                        (item.construction || inFight) && "brightness-50"
                    )}
                />
            </div>

            {/* Text container - takes remaining space */}
            <div className="flex-1 min-w-0 h-full flex items-center justify-start 2xl:justify-center pl-3 2xl:pl-0">
                <div
                    className={cn(
                        item.construction && "text-gray-500 text-stroke-sm",
                        "w-full text-left 2xl:text-center text-base font-normal font-display opacity-90 group-hover:scale-105 transition-transform sidebar-button-text",
                        checkCurrent(item.current) ? "text-custom-yellow" : "text-white group-hover:text-yellow-400"
                    )}
                >
                    <StrokedText
                        hideShadow
                        className="font-medium font-accent text-lg 2xl:text-xl text-custom-yellow text-stroke-s-md uppercase ml-1 2xl:ml-0"
                    >
                        {item.name}
                    </StrokedText>
                </div>
            </div>
        </div>
    );
};

export default function SideBarGridItemThin({ item, inFight, availableQuests }: SideBarGridItemThinProps) {
    const location = useLocation();
    const { craftCollectReady } = useNormalStore();

    const checkCurrent = (name?: string): boolean => {
        if (!name) return false;
        return `/${name}` === location.pathname;
    };

    if (item.external) {
        return (
            <a
                href={item.href}
                target="_blank"
                rel="noreferrer"
                className={cn(
                    "roundedBtnBlueThin group relative col-span-2 flex w-full h-12 min-w-0 child-hover:scale-105 rounded-md text-center font-medium font-accent text-stroke-md text-xs drop-shadow-lg hover:brightness-110"
                )}
            >
                <ButtonContent item={item} inFight={inFight} checkCurrent={checkCurrent} />
            </a>
        );
    }

    return (
        <NavLink
            to={!inFight ? item.href : "#"}
            aria-current={checkCurrent(item.current) ? "page" : undefined}
            className={cn(
                checkCurrent(item.current) ? "roundedBtnLightBlueThin" : "roundedBtnBlueThin",
                "group relative col-span-2 flex w-full h-12 min-w-0 child-hover:scale-105 rounded-md text-center font-medium font-accent text-stroke-md text-xs drop-shadow-lg hover:brightness-110"
            )}
        >
            <ButtonContent
                item={item}
                inFight={inFight}
                checkCurrent={checkCurrent}
                availableQuests={availableQuests}
                craftCollectReady={craftCollectReady}
            />
        </NavLink>
    );
}
