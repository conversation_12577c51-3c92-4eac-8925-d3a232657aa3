import { describe, expect, it, vi, beforeEach } from "vitest";
import * as SharedQuestUtils from "../quest.service.js";
import { QuestObjectiveTypes } from "../../types/quest.js";
import { LocationTypes, QuestTargetAction } from "@prisma/client";

// Mock the quest repositories and helpers
vi.mock("../../repositories/quest.repository.js", () => ({
    findUserQuestObjectiveProgress: vi.fn(),
}));

vi.mock("../../repositories/dailyquest.repository.js", () => ({
    findDailyQuestProgress: vi.fn(),
}));

vi.mock("../../features/quest/quest.helpers.js", () => ({
    updateQuestObjectiveCount: vi.fn(),
    checkObjectiveComplete: vi.fn(),
}));

vi.mock("../../features/dailyquest/dailyquest.helpers.js", () => ({
    updateDailyQuest: vi.fn(),
}));

vi.mock("../../utils/dateHelpers.js", () => ({
    getToday: vi.fn(() => new Date("2023-01-01")),
}));

describe("Shared Quest Utils", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("handleDefeatNPC", () => {
        it("should handle both specific and any NPC defeat objectives", async () => {
            const userId = 123;
            const creatureId = 456;
            const location = LocationTypes.shibuya;

            await SharedQuestUtils.handleDefeatNPC(userId, creatureId, location);

            // Should be called for both specific creature and any creature objectives
            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledTimes(2);
        });

        it("should handle only any NPC defeat objectives when creatureId is null", async () => {
            const userId = 123;
            const location = LocationTypes.shibuya;

            await SharedQuestUtils.handleDefeatNPC(userId, null, location);

            // Should be called only once for any creature objectives
            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledTimes(1);
        });
    });

    describe("handleDefeatNPCInTurns", () => {
        it("should handle NPC defeat in turns objectives", async () => {
            const userId = 123;
            const turns = 5;

            await SharedQuestUtils.handleDefeatNPCInTurns(userId, turns);

            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledWith(userId, {
                objectiveType: QuestObjectiveTypes.DEFEAT_NPC_IN_TURNS,
                target: turns,
            });
        });
    });

    describe("handleDefeatNPCWithLowDamage", () => {
        it("should handle low damage defeat objectives", async () => {
            const userId = 123;
            const damageTaken = 50;

            await SharedQuestUtils.handleDefeatNPCWithLowDamage(userId, damageTaken);

            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledWith(userId, {
                objectiveType: QuestObjectiveTypes.DEFEAT_NPC_WITH_LOW_DAMAGE,
                target: { gte: 50 }, // 50% damage taken
            });
        });

        it("should return early if user health is not found", async () => {
            const userId = 123;
            const damageTaken = null;

            await SharedQuestUtils.handleDefeatNPCWithLowDamage(userId, damageTaken);

            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).not.toHaveBeenCalled();
        });
    });

    describe("handlePvPPostBattleChoice", () => {
        it("should handle PvP post battle choice objectives", async () => {
            const userId = 123;
            const targetId = 456;
            const postBattleAction = QuestTargetAction.mug;

            await SharedQuestUtils.handlePvPPostBattleChoice(userId, targetId, postBattleAction);

            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledWith(userId, {
                objectiveType: QuestObjectiveTypes.PVP_POST_BATTLE_CHOICE,
                targetAction: postBattleAction,
            });
        });
    });

    describe("handlePvPKill", () => {
        it("should handle PvP kill objectives", async () => {
            const userId = 123;
            const targetLevel = 10;

            await SharedQuestUtils.handlePvPKill(userId, targetLevel);

            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledWith(userId, {
                objectiveType: QuestObjectiveTypes.DEFEAT_PLAYER,
                target: { lte: targetLevel },
            });
        });

        it("should return early if target level is not found", async () => {
            const userId = 123;
            const targetLevel = null;

            await SharedQuestUtils.handlePvPKill(userId, targetLevel);

            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).not.toHaveBeenCalled();
        });
    });

    describe("handleBountyPlaced", () => {
        it("should handle bounty placement objectives", async () => {
            const userId = 123;
            const bountyAmount = 1000;

            await SharedQuestUtils.handleBountyPlaced(userId, bountyAmount);

            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledWith(userId, {
                objectiveType: QuestObjectiveTypes.PLACE_BOUNTY,
                target: { lte: bountyAmount },
            });
        });
    });

    describe("handleCraftItem", () => {
        it("should handle both specific and any item crafting objectives", async () => {
            const userId = 123;
            const itemId = 456;
            const amount = 2;

            await SharedQuestUtils.handleCraftItem(userId, itemId, amount);

            // Should be called for both specific item and any item objectives
            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledTimes(2);
        });

        it("should handle only any item crafting objectives when itemId is null", async () => {
            const userId = 123;
            const amount = 2;

            await SharedQuestUtils.handleCraftItem(userId, null, amount);

            // Should be called only once for any item objectives
            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledTimes(1);
        });
    });

    describe("handleSuggestionVote", () => {
        it("should handle suggestion vote objectives", async () => {
            const userId = 123;
            const suggestionId = 456;
            const amount = 1;

            await SharedQuestUtils.handleSuggestionVote(userId, suggestionId, amount);

            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledWith(userId, {
                objectiveType: QuestObjectiveTypes.VOTE_ON_SUGGESTION,
            });
        });
    });

    describe("handleCharacterEncounter", () => {
        it("should handle character encounter objectives", async () => {
            const userId = 123;
            const encounterId = 456;
            const location = LocationTypes.shibuya;
            const amount = 1;

            await SharedQuestUtils.handleCharacterEncounter(userId, encounterId, location, amount);

            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledWith(userId, {
                objectiveType: QuestObjectiveTypes.CHARACTER_ENCOUNTERS,
                location,
            });
        });
    });

    describe("handleFetchItem", () => {
        it("should handle fetch item objectives", async () => {
            const userId = 123;
            const itemId = 456;
            const amount = 3;

            await SharedQuestUtils.handleFetchItem(userId, itemId, amount);

            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledWith(userId, {
                objectiveType: QuestObjectiveTypes.ACQUIRE_ITEM,
                target: itemId,
            });
        });
    });

    describe("handleResourceGathering", () => {
        it("should handle resource gathering objectives for specific items and activity types", async () => {
            const userId = 123;
            const itemId = 456;
            const activityType = "mining";
            const amount = 2;

            await SharedQuestUtils.handleResourceGathering(userId, itemId, activityType, amount);

            // Should be called for specific item + activity type, any item + activity type, and specific item + any activity
            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledTimes(3);

            // Check specific calls
            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledWith(userId, {
                objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
                target: itemId,
                targetAction: activityType,
            });

            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledWith(userId, {
                objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
                target: null,
                targetAction: activityType,
            });

            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledWith(userId, {
                objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
                target: itemId,
                targetAction: null,
            });
        });

        it("should handle different activity types", async () => {
            const userId = 123;
            const itemId = 456;
            const amount = 1;

            // Test scavenging
            await SharedQuestUtils.handleResourceGathering(userId, itemId, "scavenging", amount);

            // Test foraging
            await SharedQuestUtils.handleResourceGathering(userId, itemId, "foraging", amount);

            // Should be called 6 times total (3 calls per activity type)
            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledTimes(6);
        });
    });

    describe("handleMissionComplete", () => {
        it("should handle COMPLETE_MISSIONS objectives for both regular and daily quests", async () => {
            const userId = 123;

            await SharedQuestUtils.handleMissionComplete(userId);

            // Should handle standard COMPLETE_MISSIONS objectives
            expect(
                vi.mocked(await import("../../repositories/quest.repository.js")).findUserQuestObjectiveProgress
            ).toHaveBeenCalledWith(userId, {
                objectiveType: QuestObjectiveTypes.COMPLETE_MISSIONS,
            });

            // Should also handle daily quest objectives
            expect(
                vi.mocked(await import("../../repositories/dailyquest.repository.js")).findDailyQuestProgress
            ).toHaveBeenCalled();
        });
    });
});
