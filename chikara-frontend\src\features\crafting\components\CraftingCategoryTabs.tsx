import Tabs from "@/components/Tabs";

export type CraftingCategory = "all" | "weapons" | "armor" | "consumables" | "misc" | "quest";

interface CraftingCategoryTabsProps {
    activeCategory: CraftingCategory;
    onCategoryChange: (category: CraftingCategory) => void;
    children: React.ReactNode;
}

export default function CraftingCategoryTabs({
    activeCategory,
    onCategoryChange,
    children,
}: CraftingCategoryTabsProps) {
    const tabs = [
        { value: "all", label: "All", icon: "🔮", color: "text-primary" },
        { value: "weapons", label: "Weapons", icon: "⚔️", color: "text-error" },
        { value: "armor", label: "Armor", icon: "🛡️", color: "text-info" },
        { value: "consumables", label: "Consumables", icon: "🧪", color: "text-success" },
        { value: "misc", label: "Misc", icon: "📦", color: "text-warning" },
        { value: "quest", label: "Quest", icon: "📜", color: "text-secondary" },
    ];

    return (
        <Tabs tabs={tabs} selectedTab={activeCategory} onTabChange={onCategoryChange as (value: string) => void}>
            {children}
        </Tabs>
    );
}
