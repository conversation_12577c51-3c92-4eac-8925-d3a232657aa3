import { getNextMidnightDate } from "@/helpers/dateHelpers";
import { Link } from "react-router-dom";
import { CountdownTimer } from "./CountdownTimer";
import { useGetActiveShrineBuffs } from "@/features/shrine/api/useGetActiveShrineBuffs";

type ShrineBuffReward = {
    buffType: string;
    description: string;
    value: number;
    isPrimary: boolean;
};

// Helper function to format buff values for display
const formatBuffValue = (buffType: string, value: number) => {
    if (buffType === "jail" || buffType === "mission" || buffType === "craftSpeed" || buffType === "auctionFees") {
        // These are reduction buffs (multipliers < 1)
        const reduction = Math.round((1 - value) * 100);
        return `-${reduction}%`;
    } else {
        // These are increase buffs (multipliers > 1)
        const increase = Math.round((value - 1) * 100);
        return `+${increase}%`;
    }
};

// Helper function to get buff display name
const getBuffDisplayName = (buffType: string) => {
    const buffNames = {
        rareDrops: "Rare Drop Chance",
        craftSpeed: "Crafting Speed",
        damage: "Damage",
        exp: "Experience",
        armour: "Armour",
        jail: "Jail Time",
        mission: "Mission Duration",
        yenEarnings: "Yen Earnings",
        auctionFees: "Auction Fees",
        exploreSpawn: "Explore Spawn Rate",
        gatheringAmount: "Gathering Amount",
    };
    return buffNames[buffType as keyof typeof buffNames] || buffType;
};

export default function ShrineBuff({ isMobile = false }) {
    const { data: activeShrineBuffs, isLoading } = useGetActiveShrineBuffs();

    const midnightDate = getNextMidnightDate();

    // Don't show if loading or no data (no active buffs)
    if (
        isLoading ||
        !activeShrineBuffs ||
        !activeShrineBuffs.buffRewards ||
        (typeof activeShrineBuffs.buffRewards === "object" && Object.keys(activeShrineBuffs.buffRewards).length === 0)
    )
        return null;

    // Extract buff data from buffRewards
    const buffRewardsArray = Object.values(activeShrineBuffs.buffRewards || {}) as ShrineBuffReward[];
    // Sort so primary buff is always first
    buffRewardsArray.sort((a, b) => (b.isPrimary ? 1 : 0) - (a.isPrimary ? 1 : 0));
    const activeBuffs = buffRewardsArray.map((buff) => ({
        name: getBuffDisplayName(buff.buffType),
        description: buff.description,
        value: formatBuffValue(buff.buffType, buff.value),
        isPrimary: buff.isPrimary,
    }));

    if (isMobile) {
        return (
            <Link
                to="/shrine"
                data-tooltip-id="shrinebuff-tooltip"
                data-tooltip-buff1name={activeBuffs?.[0]?.name}
                data-tooltip-buff1value={activeBuffs?.[0]?.value}
                data-tooltip-buff1desc={activeBuffs?.[0]?.description}
                data-tooltip-buff1primary={activeBuffs?.[0]?.isPrimary === true ? "true" : "false"}
                data-tooltip-buff2name={activeBuffs?.[1]?.name}
                data-tooltip-buff2value={activeBuffs?.[1]?.value}
                data-tooltip-buff2desc={activeBuffs?.[1]?.description}
                data-tooltip-buff2primary={activeBuffs?.[1]?.isPrimary === true ? "true" : "false"}
                className="-mt-1 mx-auto mb-1.5 flex items-center gap-2"
            >
                <div className="flex gap-1">
                    <img
                        className="h-4 w-auto rotate-180"
                        src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/pyBEK4l.png`}
                        alt=""
                    />
                    <p className="text-center text-custom-yellow text-sm">Shrine Buff</p>
                </div>

                <div className="relative mt-0.5 w-auto text-center text-green-500 text-xs">
                    <CountdownTimer showHours showHoursText targetDate={midnightDate} showSeconds={false} />
                </div>
            </Link>
        );
    }

    return (
        <>
            <Link
                to="/shrine"
                data-tooltip-id="shrinebuff-tooltip"
                data-tooltip-buff1name={activeBuffs?.[0]?.name}
                data-tooltip-buff1value={activeBuffs?.[0]?.value}
                data-tooltip-buff1desc={activeBuffs?.[0]?.description}
                data-tooltip-buff1primary={activeBuffs?.[0]?.isPrimary === true ? "true" : "false"}
                data-tooltip-buff2name={activeBuffs?.[1]?.name}
                data-tooltip-buff2value={activeBuffs?.[1]?.value}
                data-tooltip-buff2desc={activeBuffs?.[1]?.description}
                data-tooltip-buff2primary={activeBuffs?.[1]?.isPrimary === true ? "true" : "false"}
                className="relative cursor-pointer select-none rounded-xl border border-gray-800 bg-blue-950/50 px-2 py-1"
            >
                <div className="ml-5 flex w-fit gap-1">
                    <img
                        className="my-auto h-3 w-auto rotate-180"
                        src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/pyBEK4l.png`}
                        alt=""
                    />
                    <p className="text-center text-custom-yellow text-xs 2xl:text-sm">Shrine Buff</p>
                </div>

                <div className="relative my-auto h-5 w-auto 2xl:h-6">
                    <img
                        className="h-full w-auto"
                        src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/AdXczms.png`}
                        alt=""
                    />

                    <p className="absolute top-[0.2rem] right-[1.3rem] text-xs 2xl:top-[0.3rem] 2xl:right-8">
                        {" "}
                        <CountdownTimer showHours targetDate={midnightDate} showSeconds={false} />
                    </p>
                </div>
            </Link>
        </>
    );
}
