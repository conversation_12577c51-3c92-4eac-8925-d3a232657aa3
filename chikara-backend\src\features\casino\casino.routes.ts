import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import CasinoController from "./casino.controller.js";
import RouletteController from "./roulette.controller.js";
import { gambleSchema, checkLotteryEntrySchema, enterLotterySchema, placeBetSchema } from "./casino.validation.js";

export const casinoRouter = {
    gamble: canMakeStateChangesAuth
        .input(gambleSchema)
        .handler(async ({ input, context }) =>
            handleResponse(await CasinoController.gamble(context.user.id, input.amount))
        ),

    enterLottery: canMakeStateChangesAuth
        .input(enterLotterySchema)
        .handler(async ({ input, context }) =>
            handleResponse(await CasinoController.enterLottery(context.user.id, input.lotteryId))
        ),

    getLottery: isLoggedInAuth.handler(async () => handleResponse(await CasinoController.getLottery())),

    checkLotteryEntry: isLoggedInAuth
        .input(checkLotteryEntrySchema)
        .handler(async ({ input, context }) =>
            handleResponse(await CasinoController.checkLotteryEntry(context.user.id, Number(input.id)))
        ),

    placeBet: canMakeStateChangesAuth
        .input(placeBetSchema)
        .handler(async ({ input, context }) =>
            handleResponse(await RouletteController.placeBet(context.user.id, input))
        ),
};

export default casinoRouter;
