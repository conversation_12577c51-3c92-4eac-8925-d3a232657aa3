import { <PERSON>, <PERSON>, <PERSON>rk<PERSON>, ScrollText } from "lucide-react";
import { BattlePlayer, CombatLogEntry } from "../types/battle";
import { useAutoAnimate } from "@formkit/auto-animate/react";
import { getActionIcon, getActionText, getAvatar, getDetailedActionText } from "../helpers/battleLogHelpers";

interface CombatLogProps {
    logs: CombatLogEntry[];
    player: BattlePlayer;
    enemy: BattlePlayer;
}

const BattleLog: React.FC<CombatLogProps> = ({ logs, player, enemy }) => {
    const [animationParent] = useAutoAnimate();

    const formatTime = (timestamp: number) => {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
    };

    const getActionBadgeStyle = (action: string, isActor: boolean) => {
        switch (action?.toLowerCase()) {
            case "attack":
            case "ranged":
            case "melee":
                return "badge-warning";
            case "flee_success":
                return "badge-info";
            case "flee_failed":
                return "badge-error";
            case "battle_win":
                return isActor ? "badge-primary" : "badge-neutral";
            case "battle_start":
                return "badge-accent";
            case "rage":
                return "badge-secondary";
            case "spray":
                return "badge-primary";
            default:
                return "badge-neutral";
        }
    };

    const getChatBubbleStyle = (actorId: string) => {
        const isPlayerAction = actorId === player.id;
        return isPlayerAction ? "chat-end" : "chat-start";
    };

    const getChatBubbleColor = (action: string, isActor: boolean) => {
        switch (action?.toLowerCase()) {
            case "attack":
            case "ranged":
            case "melee":
                return "chat-bubble-error";
            case "flee_success":
                return "chat-bubble-success";
            case "flee_failed":
                return "chat-bubble-warning";
            case "battle_win":
                return isActor ? "chat-bubble-success" : "chat-bubble-error";
            case "battle_start":
                return "chat-bubble-info";
            case "rage":
                return "chat-bubble-accent";
            case "spray":
                return "chat-bubble-secondary";
            default:
                return "chat-bubble-neutral";
        }
    };

    return (
        <div className="card bg-base-200 shadow-xl h-48 lg:h-96 relative">
            <div className="card-body p-0 flex flex-col h-full">
                {/* Header */}
                <div className="bg-base-300 px-3 py-1 rounded-t-lg border-t-2 border-base-200 flex-shrink-0">
                    <div className="flex items-center gap-2">
                        <div className="flex items-center gap-2 px-1">
                            <ScrollText className="size-3 md:size-4 text-gray-400" />
                            <h2 className="text-gray-100 text-sm lg:text-base">Combat Log</h2>
                        </div>
                        <div className="ml-auto text-xs text-base-content/70">
                            {logs.length} {logs.length === 1 ? "action" : "actions"}
                        </div>
                    </div>
                </div>

                {/* Chat container */}
                <div className="flex-1 overflow-y-auto p-2 md:p-4 scroll-smooth bg-base-200 rounded-b-lg min-h-0">
                    {logs.length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-full">
                            <div className="text-center">
                                <Sparkles className="w-12 h-12 mx-auto text-base-content/30 mb-4" />
                                <p className="text-base-content/60 text-sm">Waiting for battle actions...</p>
                                <p className="text-base-content/40 text-xs mt-2">Combat events will appear here</p>
                            </div>
                        </div>
                    ) : (
                        <div ref={animationParent} className="space-y-3">
                            {[...logs].reverse().map((log) => {
                                const isPlayerAction = log.actorId === player.id;
                                const chatPlacement = getChatBubbleStyle(log.actorId);
                                const bubbleColor = getChatBubbleColor(log.action, isPlayerAction);
                                const badgeStyle = getActionBadgeStyle(log.action, isPlayerAction);

                                return (
                                    <div key={log.id} className={`chat ${chatPlacement}`}>
                                        {/* Avatar */}
                                        <div className="chat-image avatar">
                                            <div className="w-8 h-8 md:w-10 md:h-10 rounded-full ring ring-base-300 ring-offset-base-100 ring-offset-2">
                                                {getAvatar(log.actorId, player, enemy)}
                                            </div>
                                        </div>

                                        {/* Header with round and timestamp */}
                                        <div className="chat-header flex items-center gap-2 mb-1">
                                            {log.round && (
                                                <div className="badge badge-primary badge-sm text-stroke-0 font-bold">
                                                    Round {log.round}
                                                </div>
                                            )}
                                            <time className="text-xs text-base-content/60 flex items-center gap-1">
                                                <Clock className="w-3 h-3" />
                                                {formatTime(log.timestamp)}
                                            </time>
                                        </div>

                                        {/* Main chat bubble */}
                                        <div className={`chat-bubble ${bubbleColor} text-sm shadow-lg font-semibold`}>
                                            <div className="flex items-center gap-2 mb-2 text-stroke-0">
                                                <span className={`badge ${badgeStyle} badge-sm gap-1 text-black`}>
                                                    {getActionIcon(log.action, isPlayerAction)}
                                                    {getActionText(log.action, isPlayerAction)}
                                                </span>
                                                {log.damage !== undefined && log.damage > 0 && (
                                                    <div className="badge badge-neutral badge-sm gap-1">
                                                        <Heart className="w-3 h-3" />-{log.damage}
                                                    </div>
                                                )}
                                            </div>

                                            {/* Detailed description */}
                                            <div className="text-xs opacity-90">
                                                {getDetailedActionText(log, player, enemy)}
                                            </div>
                                        </div>

                                        {/* Footer with status effects */}
                                        {log.details?.statusEffects && log.details.statusEffects.length > 0 && (
                                            <div className="chat-footer mt-1">
                                                <div className="flex flex-wrap gap-1">
                                                    {log.details.statusEffects.map((effect, index) => (
                                                        <div key={index} className="badge badge-accent badge-xs gap-1">
                                                            <Sparkles className="w-2 h-2" />
                                                            {effect}
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default BattleLog;
