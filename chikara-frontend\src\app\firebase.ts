import { client } from "@/lib/orpc";
import { initializeApp } from "firebase/app";
import { getMessaging, getToken } from "firebase/messaging";
import { persistStore } from "./store/persistStore";

// Firebase configurations.
var firebaseConfig = {
    apiKey: "AIzaSyCQ-QeWMrBZw8v9idybo_cFwAIlDO6j-z0",
    authDomain: "chikara-academy.firebaseapp.com",
    projectId: "chikara-academy",
    storageBucket: "chikara-academy.appspot.com",
    messagingSenderId: "75175802639",
    appId: "1:75175802639:web:a1cc5e6073f185ec46707a",
};

const firebaseApp = initializeApp(firebaseConfig);
export const messaging = getMessaging(firebaseApp);

export const sendTokenToServer = async (token: string) => {
    try {
        const res = await client.notification.saveFCMToken({ token });
        console.log("Token sent to server successfully: " + token);
        return res;
    } catch (error) {
        console.error("Error sending token to server:", error);
    }
};

export const requestNotificationPermission = async (registration: ServiceWorkerRegistration) => {
    const { messagingToken, setMessagingToken } = persistStore.getState();
    if (messagingToken && messagingToken.length > 0) return;
    const permission = await Notification.requestPermission();

    if (permission === "granted") {
        const token = await getToken(messaging, {
            vapidKey: "BCv5d6-bgzNJ3EmseSvXJNcGKMFwmFMI_bk-CALpT67DSxgY6qT7_mh68egbVsZ2xR9O4OuuzstFz60uzLFFVCk",
            serviceWorkerRegistration: registration,
        });
        if (token) {
            await sendTokenToServer(token);
            setMessagingToken(token);
            console.log("Token generated :", token);
        }
    } else if (permission === "denied") {
        //notifications are blocked
        console.log("Notifications denied");
    }
};
