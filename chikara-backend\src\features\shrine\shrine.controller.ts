import gameConfig from "../../config/gameConfig.js";
import * as UserService from "../../core/user.service.js";
import * as ShrineHelper from "./shrine.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { getToday } from "../../utils/dateHelpers.js";
import { emitShrineDonationMade } from "../../core/events/index.js";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ShrineRepository from "../../repositories/shrine.repository.js";

export const getDailyShrineGoal = async (date: Date) => {
    return await ShrineHelper.getDailyShrineGoal(date);
};

export const isTodaysDonationGoalReached = async () => {
    const today = getToday();
    return await ShrineRepository.findDailyShrineGoalWithReached(today);
};

export const getActiveBuffsForUser = async (userId: number) => {
    const today = getToday();
    const [dailyShrine, userDonation] = await Promise.all([
        ShrineRepository.findDailyShrineGoalWithReached(today),
        ShrineRepository.findUserDonationForDate(userId, today),
    ]);

    if (!dailyShrine?.buffRewards) {
        return null;
    }

    const hasMinimumDonation = (userDonation?.amount ?? 0) >= gameConfig.SHRINE_MINIMUM_DONATION;
    const buffRewards = dailyShrine.buffRewards as Record<
        string,
        { buffType: string; description: string; value: number; isPrimary: boolean }
    >;

    // Filter buffs based on user's donation status
    const activeBuffs: PrismaJson.ShrineBuffRewards = {};

    for (const [buffType, buffData] of Object.entries(buffRewards)) {
        // Primary buffs are available to all players
        if (buffData.isPrimary) {
            activeBuffs[buffType] = buffData;
        }
        // Secondary buffs require minimum donation
        else if (hasMinimumDonation) {
            activeBuffs[buffType] = buffData;
        }
    }

    return {
        ...dailyShrine,
        buffRewards: activeBuffs,
    };
};

export const getDailyShrineDonations = async (date: Date) => {
    return await ShrineHelper.dailyShrineDonations(date);
};

export const getUserDonationStatus = async (userId: number) => {
    const today = getToday();
    const hasMinimumDonation = await ShrineHelper.hasUserDonatedMinimumForSecondaryBuffs(userId, today);
    const userDonation = await ShrineRepository.findUserDonationForDate(userId, today);

    return {
        hasMinimumDonation,
        donationAmount: userDonation?.amount || 0,
        minimumRequired: gameConfig.SHRINE_MINIMUM_DONATION,
    };
};

export const donateToShrine = async (userId: number, amount: number) => {
    if (!amount) {
        return { error: "Amount is required and must be a number", statusCode: 400 };
    }
    if (amount < 0 || amount < gameConfig.SHRINE_MINIMUM_DONATION) {
        return { error: "Invalid amount", statusCode: 400 };
    }

    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    if (currentUser.cash < amount) {
        return { error: "Not enough cash", statusCode: 400 };
    }

    const dailyShrineGoal = await ShrineHelper.getDailyShrineGoal(getToday());
    if (!dailyShrineGoal) {
        return { error: "No daily shrine goal set for today", statusCode: 400 };
    }

    const updatedCash = (currentUser.cash -= amount);
    await UserService.updateUser(currentUser.id, { cash: updatedCash });

    const updatedShrineGoal = await ShrineHelper.addToDailyShrineGoal(currentUser, amount);
    if (!updatedShrineGoal) {
        return { error: "Failed to update shrine goal", statusCode: 500 };
    }

    await emitShrineDonationMade({
        userId: currentUser.id,
        amount,
    });

    logAction({
        action: "SHRINE_DONATION",
        userId: currentUser.id,
        info: {
            amount,
            totalDonations: updatedShrineGoal.donationAmount,
        },
    });

    return { data: `Donated ${amount} to daily shrine goal` };
};
