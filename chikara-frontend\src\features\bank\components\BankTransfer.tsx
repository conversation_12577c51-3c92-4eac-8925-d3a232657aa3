import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { Link } from "react-router-dom";
import { User } from "lucide-react";

interface BankTransferProps {
    transaction: {
        initiatorId: number | null;
        secondPartyId: number | null;
    };
    currentUser: string;
}

export default function BankTransfer({ transaction, currentUser }: BankTransferProps) {
    const isSender = String(transaction.initiatorId) === String(currentUser);
    
    // If current user is sender, show recipient (secondPartyId)
    // If current user is recipient, show sender (initiatorId)
    const otherUserId = isSender ? transaction.secondPartyId : transaction.initiatorId;

    const { data: user } = useGetUserInfo(otherUserId?.toString(), {
        enabled: !!otherUserId,
    });

    return (
        <div className="flex items-center gap-2 text-sm">
            <span className="text-gray-300">{isSender ? "To" : "From"}</span>
            <Link
                to={`/profile/${otherUserId}`}
                className="link link-hover text-blue-400 font-medium flex items-center gap-1 hover:text-blue-300"
            >
                <User className="w-3 h-3" />
                {user?.username || `#${otherUserId}`}
            </Link>
        </div>
    );
}
