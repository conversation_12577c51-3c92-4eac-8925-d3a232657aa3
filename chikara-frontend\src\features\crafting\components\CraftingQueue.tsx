import { DisplayItem } from "@/components/DisplayItem";
import { cn } from "@/lib/utils";
import { safeTimestampToNumber, hasTimestampExpired, getMillisecondsUntilTimestamp } from "@/utils/timestampHelpers";
import { ArrowRight, Clock, Hammer, RefreshCw, XCircle } from "lucide-react";
import { useState } from "react";
import useCancelCraft from "../api/useCancelCraft";
import useCollectCraft from "../api/useCollectCraft";
import useStartCraft from "../api/useStartCraft";
import type { CraftingQueueItem } from "../types/crafting";

export default function CraftingQueue({
    craftingQueue,
    maxCraftQueue,
    isLoading,
    isError,
}: {
    craftingQueue: CraftingQueueItem[];
    maxCraftQueue: number;
    isLoading: boolean;
    isError: boolean;
}) {
    const [showMaterials, setShowMaterials] = useState(true);

    // Use the collect craft hook
    const { mutate: collectCraft, isPending: isCollecting } = useCollectCraft();

    // Use the start craft hook
    const { mutate: startCraft, isPending: isStarting } = useStartCraft();

    // Use the cancel craft hook
    const { mutate: cancelCraft, isPending: isCancelling } = useCancelCraft();

    // Check if a crafting item is completed
    const isCompleted = (item: CraftingQueueItem) => {
        return hasTimestampExpired(item?.endsAt);
    };

    // Format time remaining
    const formatTimeRemaining = (endsAt: string | bigint | number | undefined | null) => {
        const timeLeftMs = getMillisecondsUntilTimestamp(endsAt);

        if (timeLeftMs === -1) return "Unknown";
        if (timeLeftMs <= 0) return "Ready";

        const hours = Math.floor(timeLeftMs / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeftMs % (1000 * 60 * 60)) / (1000 * 60));

        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    };

    // Calculate progress percentage
    const getProgressPercentage = (item: CraftingQueueItem) => {
        const startTime = safeTimestampToNumber(item?.startedAt);
        const endTime = safeTimestampToNumber(item?.endsAt);

        if (startTime === 0 || endTime === 0) return 0;

        const now = Date.now();
        const totalTime = endTime - startTime;
        const elapsed = now - startTime;

        if (totalTime <= 0) return 100;
        return Math.min(100, Math.max(0, (elapsed / totalTime) * 100));
    };

    // Collect and recraft function
    const handleCollectAndRecraft = (item: CraftingQueueItem) => {
        const recipeId = item?.crafting_recipe.id;
        const itemId = item?.id;

        if (!recipeId || !itemId) {
            console.error("Missing recipe ID or item ID for collect and recraft");
            return;
        }

        collectCraft(
            { id: itemId },
            {
                onSuccess: () => {
                    // Start crafting the same recipe again
                    startCraft({ recipeId, amount: 1 });
                },
            }
        );
    };

    return (
        <div className="bg-gray-800/50 rounded-lg border border-purple-900/30 overflow-hidden lg:h-fit lg:w-80">
            <button
                className="w-full flex items-center justify-between p-3 text-white"
                onClick={() => setShowMaterials(!showMaterials)}
            >
                <div className="flex items-center gap-2">
                    <Clock className="size-5 text-purple-400" />
                    <span className="">Crafting Queue</span>
                    {!isLoading && !isError && (
                        <span className="text-sm text-purple-300">
                            <span className="font-medium">{craftingQueue.length}</span>
                            <span className="text-gray-500">/{maxCraftQueue} slots</span>
                        </span>
                    )}
                    {!isLoading && !isError && craftingQueue.some((item) => isCompleted(item)) && (
                        <span className="px-1.5 py-0.5 bg-green-600 text-gray-200 text-xs rounded-full text-stroke-s-sm">
                            Ready
                        </span>
                    )}
                </div>
                <ArrowRight className={cn("size-5 text-gray-400 transition-transform", showMaterials && "rotate-90")} />
            </button>

            {showMaterials && (
                <div className="p-3 pt-0">
                    {isLoading && (
                        <div className="flex items-center justify-center py-4">
                            <div className="animate-spin size-8 border-4 border-purple-500 border-t-transparent rounded-full"></div>
                        </div>
                    )}

                    {isError && (
                        <div className="flex items-center justify-center py-4 text-red-400">
                            <span>Failed to load crafting queue</span>
                        </div>
                    )}

                    {!isLoading && !isError && craftingQueue.length > 0 ? (
                        <div className="space-y-2">
                            {craftingQueue.map((item) => {
                                const completed = isCompleted(item);
                                const outputItem = item?.crafting_recipe.outputs[0];

                                return (
                                    <div
                                        key={item?.id}
                                        className="flex items-center justify-between p-2 bg-gray-900/50 rounded-lg"
                                    >
                                        <div className="flex items-center gap-2">
                                            <div className={cn("size-10 rounded-lg flex items-center justify-center")}>
                                                {outputItem?.image ? (
                                                    <DisplayItem item={outputItem} className="" />
                                                ) : (
                                                    <div className="text-xl">🔮</div>
                                                )}
                                            </div>
                                            <div>
                                                <h4 className="text-white text-sm font-medium">
                                                    {outputItem?.name || "Unknown Item"}{" "}
                                                    <span className="text-gray-400">x{outputItem?.amount}</span>
                                                </h4>
                                                <div className="flex items-center gap-1 text-xs">
                                                    {completed ? (
                                                        <span className="text-green-400">Ready to collect</span>
                                                    ) : (
                                                        <>
                                                            <Clock className="size-3 text-blue-400" />
                                                            <span className="text-gray-400">
                                                                {formatTimeRemaining(item?.endsAt)}
                                                            </span>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        {completed ? (
                                            <div className="flex flex-col gap-2">
                                                <button
                                                    disabled={isCollecting || isStarting}
                                                    className={cn(
                                                        "px-3 py-1.5 text-white! text-sm rounded-sm transition-colors text-stroke-s-sm",
                                                        isCollecting || isStarting
                                                            ? "bg-green-700 cursor-not-allowed"
                                                            : "bg-green-600 hover:bg-green-700"
                                                    )}
                                                    onClick={() => {
                                                        if (item?.id) {
                                                            collectCraft({ id: item.id });
                                                        }
                                                    }}
                                                >
                                                    {isCollecting ? "Collecting..." : "Collect"}
                                                </button>
                                                <button
                                                    disabled={isCollecting || isStarting}
                                                    className={cn(
                                                        "px-3 py-1.5 flex items-center justify-center gap-1 text-white! text-sm rounded-sm transition-colors text-stroke-s-sm",
                                                        isCollecting || isStarting
                                                            ? "bg-blue-700 cursor-not-allowed"
                                                            : "bg-blue-600 hover:bg-blue-700"
                                                    )}
                                                    onClick={() => handleCollectAndRecraft(item)}
                                                >
                                                    {isCollecting || isStarting ? (
                                                        "Processing..."
                                                    ) : (
                                                        <>
                                                            <RefreshCw className="size-3" /> Collect + Craft Again
                                                        </>
                                                    )}
                                                </button>
                                            </div>
                                        ) : (
                                            <div className="flex items-center gap-2">
                                                <div className="w-24 h-2 bg-gray-700 rounded-full overflow-hidden">
                                                    <div
                                                        className="h-full bg-blue-600 rounded-full"
                                                        style={{
                                                            width: `${getProgressPercentage(item)}%`,
                                                        }}
                                                    ></div>
                                                </div>
                                                <button
                                                    disabled={isCancelling}
                                                    className={cn(
                                                        "btn btn-error btn-outline btn-xs",
                                                        isCancelling && "loading"
                                                    )}
                                                    onClick={() => {
                                                        if (item?.id) {
                                                            cancelCraft({ id: item.id });
                                                        }
                                                    }}
                                                >
                                                    {!isCancelling && <XCircle className="size-3" />}
                                                    <span className="hidden sm:inline"></span>
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    ) : !isLoading && !isError ? (
                        <div className="flex flex-col items-center justify-center py-4 text-gray-300">
                            <Hammer className="size-8 mb-2 opacity-75" />
                            <p className="text-sm">No items in queue</p>
                        </div>
                    ) : null}
                </div>
            )}
        </div>
    );
}
