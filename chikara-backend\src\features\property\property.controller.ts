import * as propertyRepository from "../../repositories/property.repository.js";
import * as UserRepository from "../../repositories/user.repository.js";

//   const Furniture = {
//     id: INTEGER,
//     name: STRING,
//     type: STRING,
//     slots: INTEGER,
//     buffs: JSON,
//     craftingRequirements: JSON
//   };

export const getHousingList = async () => {
    const housingList = await propertyRepository.findHousingList();

    if (!housingList) {
        throw new Error("Property list not found");
    }

    return housingList;
};

export const purchaseProperty = async (propertyId: number, userId: number) => {
    const [property, currentUser] = await Promise.all([
        propertyRepository.findPropertyById(propertyId),
        UserRepository.getUserById(userId),
    ]);

    if (!property) {
        return { error: "Property not found" };
    }

    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }

    const exactProperty = await propertyRepository.findUserPropertyByUserAndPropertyId(userId, propertyId);

    if (exactProperty) {
        return { error: "You already own this property" };
    }

    // Check if user has any property of the same type
    const existingPropertyOfType = await propertyRepository.findUserPropertyByPropertyType(userId, property.propertyType);
    
    if (existingPropertyOfType) {
        // Calculate net cost after deducting sell value of existing property
        const sellValue = Math.floor(existingPropertyOfType.property.cost * 0.2); // 20% of original cost
        const netCost = property.cost - sellValue;
        
        if (currentUser.cash < netCost) {
            return { error: "Not enough cash to complete purchase." };
        }
        
        // Replace the existing property of the same type
        const result = await propertyRepository.executePropertyReplacement(
            userId,
            propertyId,
            currentUser.cash,
            netCost,
            existingPropertyOfType.id
        );
        return { data: result[2] };
    }

    if (currentUser.cash < property.cost) {
        return { error: "Not enough cash to complete purchase." };
    }

    // No existing property of this type, proceed with normal purchase
    const result = await propertyRepository.executePropertyPurchase(
        userId,
        propertyId,
        currentUser.cash,
        property.cost
    );

    return { data: result[1] };
};

export const sellProperty = async (propertyId: number, userId: number) => {
    const userProperty = await propertyRepository.findUserPropertyByUserAndPropertyIdWithProperty(userId, propertyId);

    if (!userProperty) {
        return { error: "You don't own this property" };
    }

    const user = await UserRepository.getUserById(userId);
    const sellPrice = Math.floor(userProperty.property.cost * 0.2); // 20% of original cost

    if (!user) {
        return { error: "User not found", statusCode: 404 };
    }

    const result = await propertyRepository.executePropertySale(userProperty.id, userId, user.cash, sellPrice);

    return {
        data: {
            soldFor: sellPrice,
            newBalance: result[1].cash,
        },
    };
};

export const getUserProperties = async (userId: number) => {
    const userProperties = await propertyRepository.findUserPropertiesWithDetails(userId);
    return { data: userProperties };
};

