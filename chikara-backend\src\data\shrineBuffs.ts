import deepFreeze from "../utils/freezeUtil.js";

export const SHRINE_BUFFS = [
    {
        buffType: "rareDrops",
        description: "Increases rare item drop chance",
        primaryValue: 1.4,
        secondaryValue: 1.2,
    },
    {
        buffType: "craftSpeed",
        description: "Reduces crafting time of all items",
        primaryValue: 0.6,
        secondaryValue: 0.8,
    },
    {
        buffType: "damage",
        description: "Increases all damage done",
        primaryValue: 1.25,
        secondaryValue: 1.1,
    },
    {
        buffType: "exp",
        description: "Increases all EXP received",
        primaryValue: 1.25,
        secondaryValue: 1.1,
    },
    {
        buffType: "armour",
        description: "Increases armour",
        primaryValue: 1.25,
        secondaryValue: 1.1,
    },
    {
        buffType: "jail",
        description: "Reduces jail time",
        primaryValue: 0.5,
        secondaryValue: 0.25,
    },
    {
        buffType: "mission",
        description: "Reduces mission durations",
        primaryValue: 0.5,
        secondaryValue: 0.25,
    },
    {
        buffType: "yenEarnings",
        description: "Increases all Yen from encounters and job payouts",
        primaryValue: 1.5,
        secondaryValue: 1.25,
    },
    {
        buffType: "auctionFees",
        description: "Reduces auction listing fees",
        primaryValue: 0.5,
        secondaryValue: 0.75,
    },
    {
        buffType: "exploreSpawn",
        description: "Increases spawn rate of explore map nodes",
        primaryValue: 1.5,
        secondaryValue: 1.25,
    },
    {
        buffType: "gatheringAmount",
        description: "Increases amount gathered from gathering nodes",
        primaryValue: 1.4,
        secondaryValue: 1.2,
    },
] as const;

export default deepFreeze(SHRINE_BUFFS);
