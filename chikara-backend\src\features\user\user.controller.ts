import gameConfig, { publicConfig } from "../../config/gameConfig.js";
import * as EquipmentService from "../../core/equipment.service.js";
import * as InventoryService from "../../core/inventory.service.js";
import { emitStatsTrained } from "../../core/events/game-event.service.js";
import * as UserService from "../../core/user.service.js";
import * as BattleHelpers from "../battle/helpers/battle.helpers.js";
import * as TalentHelper from "../talents/talents.helpers.js";
import * as QuestService from "../../core/quest.service.js";
import * as UserHelper from "./user.helpers.js";
import * as UserRepository from "../../repositories/user.repository.js";
import * as SkillsService from "../../core/skills.service.js";
import { logAction } from "../../lib/actionLogger.js";
import { UserModel, UserAchievementsModel } from "../../lib/db.js";
import * as imagesHelper from "../../utils/images.js";
import { EquipSlots, ItemTypes, SkillType } from "@prisma/client";
import { BattleType } from "../battle/types/battle.types.js";
import type { UserStat } from "../../types/stats.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import * as CraftingRepository from "../../repositories/crafting.repository.js";

interface UserResponseData extends UserModel {
    user_achievements?: UserAchievementsModel;
    battleValidUntil?: number;
    activeBattleType?: BattleType;
    battleAggressor?: boolean;
    xpForNextLevel?: number;
}

// Helper function to process profile updates
const processProfileUpdate = async (
    currentUser: UserModel,
    input: {
        about?: string;
        username?: string;
        avatarData?: imagesHelper.ImageData;
        bannerData?: imagesHelper.ImageData;
    }
) => {
    const updateValues: UserRepository.UserProfileUpdateValues = {};
    const detailsChangedLog: string[] = [];

    if (input.about && input.about !== currentUser.about) {
        if (input.about.length > 500) {
            return { error: "Description is too long", statusCode: 400 };
        }
        updateValues.about = input.about;
        detailsChangedLog.push("Updated profile description");
    }

    if (input.username && input.username !== currentUser.username) {
        if ((await UserRepository.findUserByUsername(input.username)) !== null) {
            return { error: "Username is taken", statusCode: 400 };
        }
        updateValues.username = input.username;
        detailsChangedLog.push(`Updated username from ${currentUser.username} to ${input.username}`);
    }

    if (input.avatarData) {
        const avatar = await imagesHelper.saveAsWebp(
            input.avatarData,
            imagesHelper.UploadType.AVATAR,
            currentUser.avatar || undefined
        );
        if (avatar) {
            updateValues.avatar = avatar;
            detailsChangedLog.push(`Updated avatar to ${avatar}`);
        } else {
            return { error: "Invalid avatar file type", statusCode: 400 };
        }
    }

    if (input.bannerData) {
        if (!(await QuestService.hasUserCompletedBannerQuest())) {
            return { error: "Profile banner quest not completed", statusCode: 400 };
        }

        const profileBanner = await imagesHelper.saveAsWebp(
            input.bannerData,
            imagesHelper.UploadType.PROFILE_BANNER,
            currentUser.profileBanner || undefined
        );
        if (profileBanner) {
            updateValues.profileBanner = profileBanner;
            detailsChangedLog.push(`Updated profile banner to ${profileBanner}`);
        } else {
            return { error: "Invalid banner file type", statusCode: 400 };
        }
    }

    return { updateValues, detailsChangedLog };
};

export const getCurrentUserInfo = async (userId: number) => {
    const user = await UserRepository.getUserWithAchievements(userId);
    if (!user) {
        return { error: "User not found" };
    }

    const battleState = await BattleHelpers.IsUserInBattle(user);
    const userData: UserResponseData = UserHelper.GetSafeUserDict({ ...user });

    if (battleState) {
        const battleAggressor = Number.parseInt(battleState.aggressorId) === user.id;
        if (battleAggressor) {
            userData.battleValidUntil = battleState.validUntil;
            userData.activeBattleType = battleState.battleType;
            userData.battleAggressor = battleAggressor;
        }
    }

    userData.xpForNextLevel = UserService.getXpForNextLevel(user);

    return { data: userData };
};

export const getUserInfo = async (userId: number) => {
    const user = await UserRepository.getUserProfile(userId);

    if (!user) {
        return { error: "User not found" };
    }

    return { data: user };
};

export const getInventory = async (userId: number) => {
    const inventory = await UserRepository.findUserInventory(userId);

    // for (const userItem of inventory) {
    //     if (userItem.upgradeLevel > 0) {
    //         userItem.item = InventoryService.ApplyItemUpgrades(userItem.item, userItem.upgradeLevel);
    //     }
    // }
    return { data: inventory };
};

export const getEquippedItems = async (userId: number) => {
    const equippedItems = await EquipmentService.GetEquippedItems(userId);
    return { data: equippedItems };
};

export const getTradeableInventory = async (userId: number) => {
    const currentUser = await UserRepository.findTradeableInventory(userId);
    const userItems = currentUser?.user_item || [];
    return { data: userItems };
};

export const userList = async () => {
    const users = await UserRepository.getAllUsers();
    return { data: users };
};

export const UpdateProfileDetails = async (
    userId: number,
    description: string,
    username: string,
    files: {
        avatar?: Express.Multer.File[];
        banner?: Express.Multer.File[];
    }
) => {
    const currentUser = await UserHelper.GetUserByIdWithAssociations(userId);

    if (!currentUser) {
        return { error: "User not found" };
    }

    if (currentUser.profileDetailBanUntil && currentUser.profileDetailBanUntil > Date.now()) {
        return {
            error: "You are currently banned from updating your profile details",
            statusCode: 400,
        };
    }

    const input: {
        about?: string;
        username?: string;
        avatarData?: imagesHelper.ImageData;
        bannerData?: imagesHelper.ImageData;
    } = {};

    if (description) {
        input.about = description;
    }

    if (username) {
        input.username = username;
    }

    if (files?.avatar && files.avatar.length > 0) {
        const avatarFile = files.avatar[0];
        input.avatarData = avatarFile as unknown as imagesHelper.ImageData;
    }

    if (files?.banner && files.banner.length > 0) {
        const bannerFile = files.banner[0];
        input.bannerData = bannerFile as unknown as imagesHelper.ImageData;
    }

    const processResult = await processProfileUpdate(currentUser, input);

    if ("error" in processResult) {
        return processResult;
    }

    const { updateValues, detailsChangedLog } = processResult;

    const result = await UserRepository.updateUserProfile(currentUser.id, updateValues);

    for (const detail of detailsChangedLog) {
        logAction({
            action: "UPDATED_ACCOUNT_DETAILS",
            userId: currentUser.id,
            info: {
                detail,
            },
        });
    }

    return { data: UserHelper.GetSafeUserDict(result) };
};

// ORPC handler for updating profile details
export const updateProfileDetailsORPC = async (
    userId: number,
    input: {
        about?: string;
        username?: string;
        avatar?: File;
        banner?: File;
    }
) => {
    const currentUser = await UserHelper.GetUserByIdWithAssociations(userId);

    if (!currentUser) {
        return { error: "User not found" };
    }

    if (currentUser.profileDetailBanUntil && currentUser.profileDetailBanUntil > Date.now()) {
        return {
            error: "You are currently banned from updating your profile details",
            statusCode: 400,
        };
    }

    const helperInput: {
        about?: string;
        username?: string;
        avatarData?: imagesHelper.ImageData;
        bannerData?: imagesHelper.ImageData;
    } = {};

    if (input.about) {
        helperInput.about = input.about;
    }

    if (input.username) {
        helperInput.username = input.username;
    }

    if (input.avatar) {
        const buffer = Buffer.from(await input.avatar.arrayBuffer());
        helperInput.avatarData = {
            buffer: buffer,
            mimetype: input.avatar.type,
            originalname: input.avatar.name,
            size: input.avatar.size,
        };
    }

    if (input.banner) {
        const buffer = Buffer.from(await input.banner.arrayBuffer());
        helperInput.bannerData = {
            buffer: buffer,
            mimetype: input.banner.type,
            originalname: input.banner.name,
            size: input.banner.size,
        };
    }

    const processResult = await processProfileUpdate(currentUser, helperInput);

    if ("error" in processResult) {
        return processResult;
    }

    const { updateValues, detailsChangedLog } = processResult;

    const result = await UserRepository.updateUserProfile(currentUser.id, updateValues);

    for (const detail of detailsChangedLog) {
        logAction({
            action: "UPDATED_ACCOUNT_DETAILS",
            userId: currentUser.id,
            info: {
                detail,
            },
        });
    }

    return { data: UserHelper.GetSafeUserDict(result) };
};

export const train = async (user: UserModel, stat: UserStat, focusAmount: number) => {
    // Get the user's current skill level
    const userStatLevel = await SkillsService.getSkillLevel(user.id, stat as SkillType);

    // Check if stat is at max level
    if (userStatLevel >= 100) {
        return {
            error: "This stat is already at maximum level!",
            statusCode: 400,
        };
    }

    // Get fresh user data to ensure we have the most recent fatigue values
    const currentUser = await UserRepository.getUserById(user.id);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }

    // Check if user has enough focus
    if (currentUser.focus < focusAmount) {
        return {
            error: "Not enough focus! Earn more focus through gameplay activities.",
            statusCode: 400,
        };
    }

    const dailyFatigueUsed = currentUser.dailyFatigueUsed || 0;

    // Check if training would exceed daily fatigue cap
    if (dailyFatigueUsed + focusAmount > gameConfig.DAILY_FATIGUE_CAP) {
        const remainingFatigue = gameConfig.DAILY_FATIGUE_CAP - dailyFatigueUsed;
        return {
            error: `You can only spend ${remainingFatigue} more focus on training today! Daily limit resets at midnight.`,
            statusCode: 400,
        };
    }

    // Calculate experience gained based on focus spent
    const baseExpGain = Math.floor(focusAmount * gameConfig.FOCUS_TO_EXP_RATIO);

    // Add experience to the stat
    const expResult = await SkillsService.addSkillExp(user.id, stat as SkillType, baseExpGain);

    // Update user focus and daily fatigue
    const statsToUpdate: UserRepository.UserStatsUpdateValues = {};
    statsToUpdate.focus = currentUser.focus - focusAmount;
    statsToUpdate.dailyFatigueUsed = dailyFatigueUsed + focusAmount;

    const updatedUser = await UserRepository.updateUserStats({ id: currentUser.id }, statsToUpdate);

    logAction({
        action: "TRAINED_STAT",
        userId: updatedUser.id,
        info: {
            stat: stat,
            focusSpent: focusAmount,
            expGained: baseExpGain,
            leveledUp: expResult.leveledUp,
            newLevel: expResult.currentLevel,
        },
    });

    await emitStatsTrained({
        userId: currentUser.id,
        amount: focusAmount,
    });

    // Return enhanced response with skill progress
    return {
        data: {
            statProgress: {
                stat: stat,
                expGained: baseExpGain,
                leveledUp: expResult.leveledUp,
                levelsGained: expResult.levelsGained,
                currentLevel: expResult.currentLevel,
                currentExp: expResult.currentExp,
                expToNextLevel: expResult.expToNextLevel,
                previousLevel: expResult.previousLevel,
            },
            focusRemaining: updatedUser.focus,
            dailyFatigueRemaining: gameConfig.DAILY_FATIGUE_CAP - updatedUser.dailyFatigueUsed,
        },
    };
};

export const getStatusEffects = async (userId: number) => {
    const effects = await UserRepository.findUserStatusEffects(userId);
    return { data: effects };
};

export const equipItem = async (userId: number, userItemId: number) => {
    const user = await UserRepository.getUserById(userId);

    if (!user) {
        return { error: "User not found", statusCode: 404 };
    }

    if (user.jailedUntil !== null) {
        return { error: "Can't equip items while jailed!", statusCode: 400 };
    }
    if (user.hospitalisedUntil != null) {
        return {
            error: "Can't equip items while hospitalised!",
            statusCode: 400,
        };
    }

    const userItem = await UserRepository.findUserItemById(userItemId);

    if (!userItem || userItem.userId !== userId) {
        return { error: "User does not have that item", statusCode: 400 };
    }

    const item = userItem.item;

    if (!item) {
        return { error: "Item not found", statusCode: 400 };
    }

    if (item.level > user.level) {
        return { error: "Level too low", statusCode: 400 };
    }

    if (item.itemType == ItemTypes.offhand && !(await TalentHelper.UserHasOffensiveOffhandsTalent(userId))) {
        return { error: "Not talented enough", statusCode: 400 };
    }

    if (item.itemType == ItemTypes.shield && !(await TalentHelper.UserHasShieldBearerTalent(userId))) {
        return { error: "Not talented enough", statusCode: 400 };
    }

    const success = await UserHelper.EquipItemToUser(user, userItem);
    if (!success) {
        return { error: "Cannot equip item to user", statusCode: 400 };
    }

    logAction({
        action: "EQUIPPED_ITEM",
        userId: user.id,
        info: {
            itemId: item.id,
            itemName: item.name,
        },
    });

    return { data: "Item equipped" };
};

export const unequipItem = async (userId: number, slot: EquipSlots) => {
    await EquipmentService.UnequipItemFromSlot(userId, slot);
    return { data: "Item unequipped" };
};

export const useItem = async (userId: number, userItemId: number) => {
    const userItem = await UserRepository.findUserItemById(userItemId);

    if (!userItem || userItem.userId !== userId) {
        return { error: "User does not have that item", statusCode: 400 };
    }

    const itemId = userItem.itemId;

    if (itemId === null || itemId === undefined || !(await InventoryService.UserHasItem(userId, itemId))) {
        return { error: "User does not have that item", statusCode: 400 };
    }

    const currentUser = await UserRepository.getUserById(userId);
    const item = await ItemRepository.findItemById(itemId);

    if (!currentUser) {
        return { error: "User not found" };
    }

    if (!item) {
        return { error: "Item not found" };
    }

    if (item.level > currentUser.level) {
        return { error: "Level too low", statusCode: 400 };
    }

    if (
        item.itemType !== ItemTypes.consumable &&
        item.itemType !== ItemTypes.recipe &&
        item.itemType !== ItemTypes.pet
    ) {
        return { error: "Item type is not usable", statusCode: 400 };
    }

    if (await BattleHelpers.IsUserInBattle(currentUser)) {
        return { error: "Cannot do this while in battle", statusCode: 400 };
    }
    if (currentUser.jailedUntil !== null) {
        return { error: "Can't use items while jailed!", statusCode: 400 };
    }
    if (currentUser.hospitalisedUntil != null) {
        return { error: "Can't use items while hospitalised!", statusCode: 400 };
    }

    let info: {
        recipeUnlocked?: boolean;
        effectRemoved?: boolean;
        buffAdded?: boolean;
        debuffAdded?: boolean;
        petUnlocked?: boolean;
    } = {};

    if (item.itemType == ItemTypes.recipe) {
        if (!item.recipeUnlockId) {
            return { error: "Item invalid", statusCode: 400 };
        }
        const recipeToUnlock = await CraftingRepository.findCraftingRecipeById(item.recipeUnlockId);
        if (!recipeToUnlock) {
            return { error: "Item invalid", statusCode: 400 };
        }

        const existingUnlock = await UserRepository.findUserRecipe(currentUser.id, recipeToUnlock.id);

        if (existingUnlock) {
            return { error: "Recipe already unlocked", statusCode: 400 };
        }

        await UserRepository.createUserRecipe(currentUser.id, recipeToUnlock.id);
        info.recipeUnlocked = true;
    }
    if (item.itemType == ItemTypes.consumable) {
        info = await UserHelper.useConsumable(item, currentUser, info);
    }

    if (item.itemType == ItemTypes.pet) {
        info = await UserHelper.usePetItem(item, currentUser, info);
    }

    await InventoryService.SubtractUserItemFromUser(userItemId, 1);

    // await UserService.updateUser(currentUser.id, {
    //     currentHealth: currentUser.currentHealth,
    //     energy: currentUser.energy,
    //     actionPoints: currentUser.actionPoints,
    // });

    logAction({
        action: "USED_ITEM",
        userId: currentUser.id,
        info: {
            itemId: item.id,
            itemName: item.name,
            recipeUnlocked: item.recipeUnlockId || undefined,
        },
    });

    return {
        data: {
            currentHealth: currentUser.currentHealth,
            energy: currentUser.energy,
            actionPoints: currentUser.actionPoints,
            info,
        },
    };
};

export const getGameConfig = () => {
    return { data: publicConfig };
};

// TODO: Update to work with new auth
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const linkDiscord = async (_token: string, _userId: number) => {
    return { error: "Not implemented" }; // TODO: Implement new Discord auth
};

export const setLastNewsIDRead = async (userId: number, newsId: number) => {
    await UserRepository.updateLastNewsIDRead(userId, newsId);
    return { data: "Success" };
};

interface SkillInfo {
    level: number;
    experience: number;
    expToNextLevel: number;
    talentPoints: number | null;
    maxTalentPoints: number;
}

export const getAllUserSkills = async (userId: number, skills: SkillType[]) => {
    // Get the user's skills
    const skillsInfo = {} as Record<SkillType, SkillInfo>;

    for (const skill of skills) {
        skillsInfo[skill] = await SkillsService.getSkillInfo(userId, skill as SkillType);
    }

    return { data: skillsInfo };
};
