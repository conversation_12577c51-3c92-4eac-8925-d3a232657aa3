import { useBankDeposit } from "@/features/bank/api/useBankDeposit";
import { useBankTransfer } from "@/features/bank/api/useBankTransfer";
import { useBankWithdraw } from "@/features/bank/api/useBankWithdraw";
import TransactionHistory from "@/features/bank/components/TransactionHistory";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import React, { useState } from "react";
import { toast } from "react-hot-toast";
import { formatCurrency, getCurrencySymbol } from "@/utils/currencyHelpers";
import {
    Wallet,
    ArrowDownToLine,
    ArrowUpFromLine,
    SendHorizontal,
    AlertCircle,
    Banknote,
    ShieldCheck,
} from "lucide-react";
import ButtonSpinner from "@/components/Spinners/ButtonSpinner";

type TransactionType = "Deposit" | "Withdraw" | "Transfer";

export default function Bank() {
    const [transactionAmount, setTransactionAmount] = useState(0);
    const [transactionType, setTransactionType] = useState<TransactionType>("Deposit");
    const [transferID, setTransferID] = useState(0);
    const [successMessage, setSuccessMessage] = useState<string | null>(null);
    const { data: currentUser } = useFetchCurrentUser();

    // ORPC mutation hooks
    const depositMutation = useBankDeposit();
    const withdrawMutation = useBankWithdraw();
    const transferMutation = useBankTransfer();

    const {
        MINIMUM_WITHDRAWAL,
        MINIMUM_DEPOSIT,
        MINIMUM_TRANSFER,
        TRANSACTION_HISTORY_LIMIT,
        TRANSACTION_FEE,
        BANK_DISABLED,
        DEPOSIT_DISABLED,
    } = useGameConfig();
    const BANK_TRANSACTION_FEE = TRANSACTION_FEE * 100;

    if (BANK_DISABLED) {
        return (
            <div className="min-h-screen bg-[#0d1b2a] flex items-center justify-center p-4">
                <div className="card bg-[#1a2f4a] border border-[#2a4a7c] shadow-xl max-w-md w-full">
                    <div className="card-body text-center">
                        <ShieldCheck className="w-16 h-16 mx-auto text-yellow-400 mb-4" />
                        <h2 className="card-title text-2xl justify-center text-white">Bank Currently Disabled</h2>
                        <p className="text-gray-300">Please return later.</p>
                    </div>
                </div>
            </div>
        );
    }

    if (DEPOSIT_DISABLED && transactionType === "Deposit") {
        setTransactionType("Withdraw");
    }

    const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setTransactionAmount(Number.parseInt(e.target.value));
    };

    const handleSetMaxAmount = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        if (transactionType === "Deposit") {
            setTransactionAmount(currentUser?.cash ?? 0);
            return;
        }
        setTransactionAmount(currentUser?.bank_balance ?? 0);
    };

    const handleIDChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setTransferID(Number.parseInt(e.target.value));
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        bankAction();
    };

    async function bankAction() {
        // Clear previous success message
        setSuccessMessage(null);

        if (transactionType === "Deposit") {
            if (transactionAmount < MINIMUM_DEPOSIT) {
                toast.error(`Minimum deposit amount is ${formatCurrency(MINIMUM_DEPOSIT)}`);
                return;
            }
        }

        if (transactionType === "Withdraw") {
            if (transactionAmount < MINIMUM_WITHDRAWAL) {
                toast.error(`Minimum withdrawal amount is ${formatCurrency(MINIMUM_WITHDRAWAL)}`);
                return;
            }
        }

        if (transactionType === "Transfer") {
            if (transactionAmount < MINIMUM_TRANSFER) {
                toast.error(`Minimum transfer amount is ${formatCurrency(MINIMUM_TRANSFER)}`);
                return;
            }
            if (currentUser?.id === transferID) {
                toast.error("You can't bank transfer to yourself!");
                return;
            }
        }

        try {
            if (transactionType === "Deposit") {
                await depositMutation.mutateAsync({
                    amount: transactionAmount,
                });
                setSuccessMessage(`Successfully deposited ${formatCurrency(transactionAmount)}`);
            } else if (transactionType === "Withdraw") {
                await withdrawMutation.mutateAsync({
                    amount: transactionAmount,
                });
                setSuccessMessage(`Successfully withdrew ${formatCurrency(transactionAmount)}`);
            } else if (transactionType === "Transfer") {
                await transferMutation.mutateAsync({
                    recipientId: transferID,
                    transferAmount: transactionAmount,
                });
                const fee = Math.floor(transactionAmount * (BANK_TRANSACTION_FEE / 100));
                setSuccessMessage(
                    `Successfully transferred ${formatCurrency(transactionAmount)} to Student #${transferID} (Fee: ${formatCurrency(fee)})`
                );
            }

            setTransactionAmount(0);
            setTransferID(0);

            // Auto-hide success message after 5 seconds
            setTimeout(() => setSuccessMessage(null), 5000);
        } catch (error) {
            console.error(error);
            toast.error(error instanceof Error ? error.message : "An unknown error occurred");
        }
    }

    const isLoading = depositMutation.isPending || withdrawMutation.isPending || transferMutation.isPending;

    const transactionIcons = {
        Deposit: ArrowDownToLine,
        Withdraw: ArrowUpFromLine,
        Transfer: SendHorizontal,
    };

    return (
        <div className="min-h-screen bg-[#0d1b2a] text-white">
            <div className="max-w-7xl mx-auto px-4 py-6">
                {/* Security Alert */}
                {DEPOSIT_DISABLED && (
                    <div className="alert mb-6 bg-yellow-900/30 border border-yellow-800 text-yellow-400">
                        <AlertCircle className="w-6 h-6" />
                        <span className="font-medium">
                            Due to a recent security breach, all bank deposits are temporarily closed.
                        </span>
                    </div>
                )}

                {/* Balance Cards */}
                <div className="grid md:grid-cols-2 gap-4 mb-6">
                    <div className="stats bg-[#1a2f4a] border border-[#2a4a7c] shadow">
                        <div className="stat">
                            <div className="stat-figure text-green-400">
                                <Banknote className="w-8 h-8" />
                            </div>
                            <div className="stat-title text-gray-400">Bank Balance</div>
                            <div className="stat-value text-green-400">
                                {formatCurrency(currentUser?.bank_balance || 0)}
                            </div>
                            <div className="stat-desc text-gray-400">Protected from mugging</div>
                        </div>
                    </div>
                    <div className="stats bg-[#1a2f4a] border border-[#2a4a7c] shadow">
                        <div className="stat">
                            <div className="stat-figure text-blue-400">
                                <Wallet className="w-8 h-8" />
                            </div>
                            <div className="stat-title text-gray-400">Cash on Hand</div>
                            <div className="stat-value text-blue-400">{formatCurrency(currentUser?.cash || 0)}</div>
                            <div className="stat-desc text-gray-400">Available for deposit</div>
                        </div>
                    </div>
                </div>

                {/* Transaction Form */}
                <div className="card bg-[#1a2f4a] border border-[#2a4a7c] shadow-xl mb-6">
                    <div className="card-body">
                        {/* Transaction Type Tabs */}
                        <div className="tabs tabs-box mb-6 bg-[#0d1b2a] border border-[#2a4a7c]">
                            {(!DEPOSIT_DISABLED || transactionType !== "Deposit") && (
                                <>
                                    {!DEPOSIT_DISABLED && (
                                        <button
                                            className={`tab flex items-center ${transactionType === "Deposit" ? "tab-active bg-[#2a4a7c] text-green-400" : "text-gray-400"}`}
                                            onClick={() => setTransactionType("Deposit")}
                                        >
                                            <ArrowDownToLine className="w-4 h-4 mr-2" />
                                            Deposit
                                        </button>
                                    )}
                                    <button
                                        className={`tab flex items-center ${transactionType === "Withdraw" ? "tab-active bg-[#2a4a7c] text-yellow-400" : "text-gray-400"}`}
                                        onClick={() => setTransactionType("Withdraw")}
                                    >
                                        <ArrowUpFromLine className="w-4 h-4 mr-2" />
                                        Withdraw
                                    </button>
                                    <button
                                        className={`tab flex items-center ${transactionType === "Transfer" ? "tab-active bg-[#2a4a7c] text-blue-400" : "text-gray-400"}`}
                                        onClick={() => setTransactionType("Transfer")}
                                    >
                                        <SendHorizontal className="w-4 h-4 mr-2" />
                                        Transfer
                                    </button>
                                </>
                            )}
                        </div>

                        {/* Success Message */}
                        {successMessage && (
                            <div className="alert mb-4 bg-green-900/30 border border-green-800 text-green-400">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="w-6 h-6"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                                <span>{successMessage}</span>
                            </div>
                        )}

                        <form onSubmit={handleSubmit} className="space-y-4">
                            {/* Amount Input */}
                            <div className="form-control">
                                <label className="label">
                                    <span className="label-text text-gray-300">Amount</span>
                                    <span className="label-text-alt text-gray-500">
                                        Min:{" "}
                                        {formatCurrency(
                                            transactionType === "Deposit"
                                                ? MINIMUM_DEPOSIT
                                                : transactionType === "Withdraw"
                                                  ? MINIMUM_WITHDRAWAL
                                                  : MINIMUM_TRANSFER
                                        )}
                                    </span>
                                </label>
                                <div className="join w-full">
                                    <span className="join-item btn btn-ghost bg-[#0d1b2a] border-[#2a4a7c] text-gray-400">
                                        {getCurrencySymbol("yen")}
                                    </span>
                                    <input
                                        type="number"
                                        className="input join-item flex-1 bg-[#0d1b2a] border-[#2a4a7c] text-white placeholder-gray-600 focus:border-blue-500"
                                        placeholder="0"
                                        min="0"
                                        value={transactionAmount}
                                        onChange={handleAmountChange}
                                    />
                                    <button
                                        type="button"
                                        className="btn join-item bg-[#2a4a7c] border-[#2a4a7c] hover:bg-[#3a5a8c] text-white"
                                        onClick={handleSetMaxAmount}
                                    >
                                        Max
                                    </button>
                                </div>
                            </div>

                            {/* Transfer Recipient */}
                            {transactionType === "Transfer" && (
                                <div className="form-control">
                                    <label className="label">
                                        <span className="label-text text-gray-300">Recipient Student ID</span>
                                    </label>
                                    <div className="join w-full">
                                        <span className="join-item btn btn-ghost bg-[#0d1b2a] border-[#2a4a7c] text-gray-400">
                                            #
                                        </span>
                                        <input
                                            type="number"
                                            className="input join-item flex-1 bg-[#0d1b2a] border-[#2a4a7c] text-white placeholder-gray-600 focus:border-blue-500"
                                            placeholder="Enter student ID"
                                            value={transferID || ""}
                                            onChange={handleIDChange}
                                        />
                                    </div>
                                </div>
                            )}

                            {/* Fee Information */}
                            {(transactionType === "Deposit" || transactionType === "Transfer") && (
                                <div className="alert">
                                    <AlertCircle className="w-4 h-4" />
                                    <div>
                                        <p className="font-medium">Transaction Fee: {BANK_TRANSACTION_FEE}%</p>
                                        {transactionAmount > 0 && (
                                            <p className="text-sm text-blue-300/80">
                                                Fee:{" "}
                                                {formatCurrency(
                                                    Math.floor(transactionAmount * (BANK_TRANSACTION_FEE / 100))
                                                )}
                                                {" | "}
                                                You&apos;ll receive:{" "}
                                                {formatCurrency(
                                                    transactionAmount -
                                                        Math.floor(transactionAmount * (BANK_TRANSACTION_FEE / 100))
                                                )}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Submit Button */}
                            <button
                                type="submit"
                                className="btn w-full bg-blue-600 hover:bg-blue-700 border-0 text-white"
                                disabled={
                                    isLoading ||
                                    transactionAmount === 0 ||
                                    (transactionType === "Transfer" && !transferID)
                                }
                            >
                                {isLoading ? (
                                    <>
                                        <ButtonSpinner />
                                        Processing...
                                    </>
                                ) : (
                                    <>
                                        {React.createElement(transactionIcons[transactionType], {
                                            className: "w-4 h-4 mr-2",
                                        })}
                                        {transactionType}{" "}
                                        {transactionAmount > 0 ? formatCurrency(transactionAmount) : ""}
                                    </>
                                )}
                            </button>
                        </form>

                        {/* Additional Info */}
                        <div className="divider before:bg-[#2a4a7c] after:bg-[#2a4a7c]"></div>
                        <div className="text-sm text-gray-400 space-y-1">
                            <p className="flex items-center gap-2">
                                <ShieldCheck className="w-4 h-4 text-green-400" />
                                Money in your bank cannot be mugged from you
                            </p>
                            <p className="flex items-center gap-2">
                                <AlertCircle className="w-4 h-4 text-blue-400" />
                                Withdrawals are free and instant
                            </p>
                        </div>
                    </div>
                </div>

                {/* Transaction History */}
                <TransactionHistory historyLimit={TRANSACTION_HISTORY_LIMIT} />
            </div>
        </div>
    );
}
