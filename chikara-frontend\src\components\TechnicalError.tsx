import React, { ErrorInfo } from "react";
import { RefreshCw, Home } from "lucide-react";
import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";
import errorIcon from "@/assets/icons/UI/NetworkError.png";

interface TechnicalErrorProps {
    error?: Error;
    errorInfo?: ErrorInfo;
    errorId?: string;
    retryCount?: number;
    onRetry?: () => void;
    showDetails?: boolean;
    enableRetry?: boolean;
    level?: "page" | "component" | "critical";
    boundary?: boolean;
}

interface ErrorCategory {
    type: "chunk" | "network" | "runtime" | "unknown";
    severity: "low" | "medium" | "high" | "critical";
    recoverable: boolean;
    autoRetry: boolean;
}

/**
 * Categorizes errors for better handling and user experience
 */
function categorizeError(error: Error): ErrorCategory {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || "";

    // Chunk loading errors (usually recoverable with refresh)
    if (
        message.includes("loading chunk") ||
        message.includes("dynamically imported module") ||
        message.includes("importing a module script failed")
    ) {
        return {
            type: "chunk",
            severity: "medium",
            recoverable: true,
            autoRetry: true,
        };
    }

    // Network errors
    if (message.includes("network") || message.includes("fetch") || message.includes("timeout")) {
        return {
            type: "network",
            severity: "medium",
            recoverable: true,
            autoRetry: false,
        };
    }

    // Runtime errors
    if (
        message.includes("cannot read property") ||
        message.includes("undefined is not a function") ||
        stack.includes("typeerror")
    ) {
        return {
            type: "runtime",
            severity: "high",
            recoverable: false,
            autoRetry: false,
        };
    }

    return {
        type: "unknown",
        severity: "high",
        recoverable: false,
        autoRetry: false,
    };
}

function TechnicalError({
    error,
    errorInfo,
    errorId,
    retryCount = 0,
    onRetry,
    showDetails = false,
    enableRetry = true,
    level = "component",
    boundary = false,
}: TechnicalErrorProps) {
    // Create a generic error if none is provided
    const displayError = error || new Error("Technical Error");
    const isGenericError = !error;

    const category = categorizeError(displayError);
    const isChunkError = category.type === "chunk";
    const isCritical = level === "critical" || category.severity === "critical";
    const isServerOffline = displayError?.message === "Server Offline";

    // Auto-refresh for chunk errors
    if (isChunkError && retryCount === 0) {
        setTimeout(() => window.location.reload(), 1000);
        return (
            <div className="flex items-center justify-center min-h-[200px] p-8">
                <div className="text-center">
                    <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
                    <p className="text-gray-600 dark:text-gray-400">Updating application...</p>
                </div>
            </div>
        );
    }

    const handleRefresh = () => {
        window.location.reload();
    };

    const handleGoHome = () => {
        window.location.href = "/";
    };

    const getErrorTitle = (category: ErrorCategory): string => {
        if (isServerOffline) return "Server Offline";

        // For generic errors (no error prop passed), show a simple title
        if (isGenericError) return "Technical Error";

        switch (category.type) {
            case "chunk":
                return "Application Update Required";
            case "network":
                return "Connection Problem";
            case "runtime":
                return "Something Went Wrong";
            default:
                return "Technical Error";
        }
    };

    const getErrorMessage = (category: ErrorCategory): string => {
        if (isServerOffline) return "Please try again later.";

        // For generic errors (no error prop passed), show a simple message
        if (isGenericError) return "Please try again later.";

        if (displayError?.message && !isServerOffline) return displayError.message;

        switch (category.type) {
            case "chunk":
                return "The application has been updated. Please refresh the page to continue.";
            case "network":
                return "Unable to connect to the server. Please check your internet connection and try again.";
            case "runtime":
                return "An unexpected error occurred. Our team has been notified and is working on a fix.";
            default:
                return "Something unexpected happened. Please try refreshing the page or contact support if the problem persists.";
        }
    };

    return (
        <div
            className={cn(
                isCritical || boundary
                    ? "min-h-full px-4 py-8 sm:px-6 sm:py-12 md:place-items-center md:pt-16 lg:px-8"
                    : "mx-2 my-4",
                "max-w-2xl mx-auto text-stroke-sm"
            )}
        >
            <img
                src={errorIcon}
                alt=""
                className={cn(isCritical || boundary ? "mb-4 w-48 md:mb-6" : "mb-3 w-32", "mx-auto h-auto")}
            />
            <div className="mx-auto max-w-lg text-center">
                <div className="space-y-4">
                    <p className="font-extrabold text-4xl text-indigo-600 sm:text-5xl">Uh Oh!</p>
                    <div>
                        <h1 className="text-3xl text-gray-200 sm:text-4xl font-semibold">{getErrorTitle(category)}</h1>
                        <p className="mt-2 text-sm text-gray-400">{getErrorMessage(category)}</p>
                    </div>

                    <div className="flex justify-center">
                        {enableRetry && category.recoverable && onRetry ? (
                            <button
                                className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                onClick={onRetry}
                            >
                                <RefreshCw className="w-4 h-4 mr-2" />
                                Try Again
                            </button>
                        ) : isCritical || boundary || isGenericError ? (
                            <a
                                href="/"
                                className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                            >
                                <Home className="w-4 h-4 mr-2" />
                                Go Home
                            </a>
                        ) : boundary ? (
                            <button
                                className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                onClick={handleRefresh}
                            >
                                <RefreshCw className="w-4 h-4 mr-2" />
                                Refresh
                            </button>
                        ) : (
                            <Link
                                to={"-1"}
                                className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                            >
                                Back
                            </Link>
                        )}
                    </div>

                    {/* Error Details - only show for real errors, not generic ones */}
                    {showDetails && !isGenericError && (
                        <details className="text-left bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
                            <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Technical Details
                            </summary>
                            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-2">
                                <div>
                                    <strong>Error:</strong> {displayError.message}
                                </div>
                                {errorId && (
                                    <div>
                                        <strong>Error ID:</strong> {errorId}
                                    </div>
                                )}
                                {displayError.stack && (
                                    <div>
                                        <strong>Stack:</strong>
                                        <pre className="mt-1 text-xs overflow-auto max-h-32 bg-gray-200 dark:bg-gray-700 p-2 rounded">
                                            {displayError.stack}
                                        </pre>
                                    </div>
                                )}
                            </div>
                        </details>
                    )}

                    {/* Retry Count - only show for real errors with retry attempts */}
                    {retryCount > 0 && !isGenericError && (
                        <p className="text-sm text-gray-500 dark:text-gray-400">Retry attempts: {retryCount}</p>
                    )}
                </div>
            </div>
        </div>
    );
}

export default TechnicalError;
