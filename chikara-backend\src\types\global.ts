import { EvolutionStage, Evolution as EvolutionType } from "../features/pets/pets.types.js";
/* eslint-disable @typescript-eslint/no-namespace */
import { CharacterDialogue, MapNodeType } from "../features/roguelike/roguelike.types.js";
import { ItemEffect } from "./item.js";
import { LocationTypes } from "@prisma/client";

declare global {
    namespace PrismaJson {
        type ItemEffects = ItemEffect[];

        type ShrineBuffRewards = Record<
            string,
            {
                buffType: string;
                description: string;
                value: number;
                isPrimary: boolean;
            }
        >;

        type RooftopDefeatedNpcs = number[];

        type Evolution = EvolutionType;

        type EvolutionStages = EvolutionStage[];

        interface RoguelikeMapType {
            playerLocation: number;
            currentNodeComplete: boolean;
            mapComplete: boolean;
            strBuff: number;
            defBuff: number;
            dexBuff: number;
            nodes: MapNodeType[];
            location: LocationTypes;

            currentNodeChoices?: string[] | null;
            currentNodeDialogue?: CharacterDialogue | null;
        }

        // ===============================================
        // Story Mode JSON Types
        // ===============================================

        // Story Chapter Types
        type RequiredChapterIds = number[];

        // Story Episode Types
        type StoryEpisodeContent = import("../features/story/story.types.js").StoryEpisodeContent;
        type StoryEpisodeChoices = import("../features/story/story.types.js").StoryEpisodeChoices;
        // type StoryEpisodeRewards = import("../features/story/story.types.js").StoryEpisodeRewards;
        type RequiredEpisodeIds = number[];
        type RequiredStoryFlags = string[];

        // User Story Progress Types
        type StoryFlags = Record<string, boolean | string | number>;

        // Story Episode Progress Types
        type EpisodeChoices = Record<string, string>;
    }
}
