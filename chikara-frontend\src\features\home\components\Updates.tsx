import { Link } from "react-router-dom";
import { FileText, ExternalLink } from "lucide-react";

function Updates() {
    return (
        <Link to="/updates" className="group block h-full">
            <div className="relative h-full overflow-hidden rounded-xl border border-slate-700/50 bg-slate-800/50 backdrop-blur-sm transition-all hover:bg-slate-800/70 hover:border-slate-600">
                {/* Background Pattern */}
                <div className="absolute inset-0 bg-gradient-to-br from-amber-600/10 via-orange-600/5 to-transparent"></div>

                {/* Character Image */}
                <div className="absolute inset-0 flex items-center justify-center opacity-20 group-hover:opacity-30 transition-opacity">
                    <img
                        src="https://d13cmcqz8qkryo.cloudfront.net/static/characters/Izumi/happyopen.webp"
                        alt=""
                        className="h-full object-cover object-top scale-150"
                    />
                </div>

                {/* Content */}
                <div className="relative flex h-full flex-col justify-between p-6">
                    <div>
                        <div className="flex items-center justify-between mb-4">
                            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-amber-500/20">
                                <FileText className="h-6 w-6 text-amber-400" />
                            </div>
                            <ExternalLink className="h-4 w-4 text-slate-500 transition-transform group-hover:translate-x-1 group-hover:-translate-y-1 group-hover:text-amber-400" />
                        </div>

                        <h3 className="text-xl font-bold text-white mb-2">Patch Notes</h3>
                        <p className="text-sm text-slate-300">
                            Check out the latest updates and improvements to Chikara Academy
                        </p>
                    </div>

                    {/* Latest Update Preview */}
                    <div className="mt-4 rounded-lg bg-slate-900/50 p-3">
                        <p className="text-xs text-amber-400 font-medium mb-1">Latest Update</p>
                        <p className="text-sm text-slate-300">Alpha 2.0 - New features and bug fixes</p>
                    </div>
                </div>
            </div>
        </Link>
    );
}

export default Updates;
