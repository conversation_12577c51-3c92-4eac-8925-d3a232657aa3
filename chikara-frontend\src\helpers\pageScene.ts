export const pageScene = (location: string) => {
    return scenes[location];
};

const scenes: Record<string, { scene: string; title: string; description?: string }> = {
    "/home": {
        scene: "livingroom1",
        title: "Home",
        description: "Your humble abode where you can rest and take a break from adventuring",
    },
    "/campus": {
        scene: "schoolField1",
        title: "Campus",
        description: "Explore the facilities of Chikara Academy and receive training from the best teachers in Tokyo",
    },
    "/shops": {
        scene: "mall1",
        title: "Shops",
        description: "A variety of shops where you can buy goods and equipment to help you with your training",
    },
    "/inventory": {
        scene: "livingroom1",
        title: "Inventory",
        description: "Manage all the items and equipment you've collected throughout your journey",
    },
    "/equipment": {
        scene: "livingroom1",
        title: "Equipment",
        description: "Manage all the items and equipment you've collected throughout your journey",
    },
    "/facultylist": {
        scene: "hallway1",
        title: "Faculty List",
        description: "All staff members and students enrolled at Chikara Academy",
    },
    "/settings": {
        scene: "livingroom1Alternative",
        title: "Settings",
        description: "Modify your game options",
    },
    "/events": {
        scene: "livingroom1Alternative",
        title: "Events",
        description: "A list of all your recent events",
    },
    "/bank": {
        scene: "shoppingstreet2",
        title: "Bank",
        description: "Conduct transactions and keep your Yen secure",
    },
    "/training": {
        scene: "sportsroom1",
        title: "Training",
    },
    "/fight": { scene: "schoolField1", title: "Battle" },
    "/moderate": { scene: "admin", title: "Moderation" },
    "/leaderboard": { scene: "smallclassroom1", title: "Alpha Leaderboard" },
    "/hospital": {
        scene: "hallway2",
        title: "Hospital",
        description: "Recover from injuries sustained in battle",
    },
    "/jail": {
        scene: "bathroom1",
        title: "Jail",
        description: "Temporary confinement for misbehaving students",
    },
    "/workshop": {
        scene: "smallclassroom1",
        title: "Workshop",
        description: "Develop and enhance items to aid you in your journey",
    },
    "/bugreport": { scene: "mall1", title: "Bug Report" },
    "/joblistings": {
        scene: "highstreet1",
        title: "Part-Time Job",
        description: "The job board where you can find part-time work to earn passive yen",
    },
    "/job": {
        scene: "highstreet1",
        title: "Part-Time Job",
        description: "Earn daily passive income through your part-time job",
    },
    "/updates": {
        scene: "market",
        title: "Updates",
        description: "Latest patch notes for the game",
    },
    "/adventure": {
        scene: "citystreet1",
        title: "Adventure",
        description: "Explore through Tokyo and earn powerful rewards",
    },
    "/calendar": {
        scene: "mall1",
        title: "Calendar",
        description: "Keep track of important dates and upcoming activities.",
    },
    // "/explore": {
    //     scene: "highstreet2",
    //     title: "Explore",
    //     description: "The bustling streets of Tokyo, where you can discover new places and meet interesting people",
    // },
    "/inbox": {
        scene: "livingroom1Alternative",
        title: "Messages",
        description: "Your latest messages",
    },
    "/courses": {
        scene: "citystreet1",
        title: "Courses",
        description: "Courses",
    },
    "/casino": {
        scene: "livingroom1",
        title: "Casino",
        description: "Casino",
    },
    "/talents": {
        scene: "livingroom1",
        title: "Talents",
        description: "Talents",
    },
    "/abilities": {
        scene: "livingroom1",
        title: "Abilities",
        description: "Equip abilities for use in combat",
    },
    "/arcade": {
        scene: "highstreet2",
        title: "Arcade",
        description: "Arcade",
    },
    "/bountyboard": {
        scene: "hallway1",
        title: "Bounty Board",
    },
    "/suggestions": {
        scene: "hallway1",
        title: "Suggestions",
    },
    "/missions": {
        scene: "hallway1",
        title: "Mission Board",
    },
    "/shrine": {
        scene: "shrine",
        title: "Shrine",
    },
    "/refer": {
        scene: "river",
        title: "Refer A Friend",
    },
    // "/gang": {
    //   scene: "river",
    //   title: "Gang",
    // },
    "/market": {
        scene: "market",
        title: "Market",
    },
    "/rooftop": {
        scene: "schoolroof1",
        title: "Rooftop",
    },
    "/news": {
        scene: "smallclassroom1",
        title: "Latest News",
    },
    "/property": {
        scene: "mall1",
        title: "Real Estate",
        description: "Build your real estate portfolio and unlock powerful buffs!",
    },
    "/tasks": {
        scene: "highstreet2",
        title: "Tasks",
    },
};

/*
<Route path="shops">
  <Route path=":shopname" element={<SingleShop />} />
</Route>
 */
