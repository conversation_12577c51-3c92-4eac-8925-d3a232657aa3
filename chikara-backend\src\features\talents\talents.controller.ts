import gameConfig from "../../config/gameConfig.js";
import * as TalentHelper from "./talents.helpers.js";
import * as talentsRepository from "../../repositories/talents.repository.js";
import * as UserHelper from "../user/user.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { LogErrorStack } from "../../utils/log.js";
import { UserStat } from "../../types/stats.js";
import * as UserRepository from "../../repositories/user.repository.js";

/**
 * Handles the HTTP request to retrieve and send a list of talents.
 */
export const getTalents = async () => {
    const talents = await TalentHelper.GetAllTalents();
    return { data: talents };
};

export const getUnlockedTalents = async (userId: number) => {
    const talents = await TalentHelper.GetUserTalents(userId);
    return { data: talents };
};

export const getEquippedAbilities = async (userId: number) => {
    const equippedAbilities = await talentsRepository.findUserEquippedAbilities(userId);
    return { data: equippedAbilities };
};

export const resetTalents = async (userId: number) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found" };
    }
    const respecCost = gameConfig.TALENT_RESPEC_BASE_COST * currentUser.level;

    if (currentUser.cash < respecCost) {
        return { error: "Not enough cash" };
    }
    if (currentUser.level < gameConfig.TALENTS_LEVEL_GATE) {
        return { error: "Level too low" };
    }

    await UserRepository.decrementUserCash(userId, respecCost);

    logAction({
        action: "TALENTS_RESET",
        userId: userId,
        info: {
            cost: respecCost,
        },
    });
    return { data: await TalentHelper.ResetTalents(userId, currentUser) };
};

export const unlock = async (userId: number, talentId: number) => {
    const targetTalentId = talentId;
    const targetTalent = await TalentHelper.GetTalentById(targetTalentId);
    if (!targetTalent) {
        return { error: "Talent does not exist" };
    }

    const user = await UserRepository.getUserById(userId);
    if (!user) {
        return { error: "User not found" };
    }

    if (user.talentPoints < targetTalent.pointsCost) {
        return { error: "Not enough talent points" };
    }

    const userTalents = await TalentHelper.GetUserTalents(userId);

    if (userTalents && userTalents.talentList) {
        const existingUserTalent = userTalents.talentList.find((x) => x.talentId === targetTalentId);

        if (existingUserTalent && existingUserTalent.level && existingUserTalent.level >= targetTalent.maxPoints) {
            return { error: "Talent already maxed" };
        }
    }

    if (
        userTalents.treePoints[targetTalent.tree] < targetTalent.pointsInTreeRequired ||
        (!userTalents.treePoints[targetTalent.tree] && targetTalent.pointsInTreeRequired > 0)
    ) {
        return { error: "Not enough points spent in tree" };
    }

    const targetTree = targetTalent.tree;

    // Check stat requirements using the ability system
    const statTrees: UserStat[] = ["strength", "intelligence", "dexterity", "defence", "endurance", "vitality"];
    if (statTrees.includes(targetTree as UserStat)) {
        const userStatLevel = await UserHelper.getUserStatLevel(userId, targetTree as UserStat);
        if (userStatLevel < targetTalent.skillLevelRequired) {
            return { error: `Not enough ${targetTree}` };
        }
    }

    await talentsRepository.decrementUserTalentPoints(userId, targetTalent.pointsCost);
    const talent = await TalentHelper.LevelUpTalent(userId, targetTalentId);

    // talents that have on-unlock effects
    if (targetTalent.name === "energetic") {
        let increment = 0;
        if (talent.level === 1) {
            increment = 1;
        }
        if (talent.level === 2) {
            increment = 2;
        }
        if (talent.level === 3) {
            increment = 2;
        }

        await talentsRepository.incrementUserMaxActionPoints(userId, increment);
    }

    if (targetTalent.name === "built") {
        const healthIncrement = targetTalent.tier1Modifier;
        if (!healthIncrement) {
            return { error: "Health increment is not defined" };
        }
        await talentsRepository.incrementUserHealth(userId, healthIncrement);
    }

    logAction({
        action: "TALENTS_UNLOCK",
        userId: userId,
        info: {
            talentId: targetTalent.id,
            talentName: targetTalent.name,
        },
    });

    return { data: "Success" };
};

type AbilitySlot = 1 | 2 | 3 | 4;

export const equipAbility = async (userId: number, talentId: number, slot: AbilitySlot) => {
    const targetTalent = await TalentHelper.GetTalentById(talentId);

    if (!targetTalent) {
        return { error: "Ability does not exist" };
    }

    if (!slot) {
        return { error: "Ability slot not selected" };
    }

    if (slot < 1 || slot > 4) {
        return { error: "Ability slot must be between 1 and 4" };
    }

    // Check if the talent is a combat ability
    if (!targetTalent.staminaCost || targetTalent.staminaCost <= 0) {
        return { error: "Only combat abilities can be equipped" };
    }

    if (!(await TalentHelper.UserHasTalent(String(userId), targetTalent.name))) {
        return { error: "Ability is not unlocked" };
    }

    try {
        await talentsRepository.updateUserEquippedAbilities(userId, slot, targetTalent.id);
    } catch (error) {
        LogErrorStack({ message: "Error when equipping ability to user", error });
        return { error: "Failed to equip ability" };
    }

    logAction({
        action: "ABILITY_EQUIP",
        userId: userId,
        info: {
            talentId: targetTalent.id,
            talentName: targetTalent.name,
            slot: slot,
        },
    });

    return { data: "Success" };
};

export const unequipAbility = async (userId: number, slot: AbilitySlot) => {
    if (!slot) {
        return { error: "Ability slot not selected" };
    }

    if (slot < 1 || slot > 4) {
        return { error: "Ability slot must be between 1 and 4" };
    }

    let abilityId;

    try {
        const equippedAbilityId = `equippedAbility${slot}Id`;
        const equippedAbilities = await talentsRepository.findUserEquippedAbilities(userId);
        if (equippedAbilities) {
            abilityId = equippedAbilities[equippedAbilityId as keyof typeof equippedAbilities];
        }
        await talentsRepository.updateUserEquippedAbilities(userId, slot, null);
    } catch (error) {
        LogErrorStack({ message: "Error when unequipping ability to user", error });
        return { error: "Failed to unequip ability" };
    }

    logAction({
        action: "ABILITY_UNEQUIP",
        userId: userId,
        info: {
            talentId: abilityId,
            slot: slot,
        },
    });

    return { data: "Success" };
};
