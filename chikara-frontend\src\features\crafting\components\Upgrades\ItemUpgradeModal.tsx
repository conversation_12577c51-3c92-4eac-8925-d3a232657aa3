import Button from "@/components/Buttons/Button";
import { Modal } from "@/components/Modal/Modal";
import Spinner from "@/components/Spinners/Spinner";
import useGetInventory from "@/hooks/api/useGetInventory";
import { useState } from "react";
import ItemUpgrade from "./ItemUpgrade";

export default function ItemUpgradeModal() {
    const [upgradeItemModalOpen, setUpgradeItemModalOpen] = useState(false);
    const { data: userInventory, isLoading } = useGetInventory();

    if (isLoading) {
        return <Spinner />;
    }

    return (
        <>
            <div className="flex">
                <Button variant="primary" className="mx-auto" onClick={() => setUpgradeItemModalOpen(true)}>
                    Upgrade Item
                </Button>
            </div>

            <Modal
                showClose
                open={upgradeItemModalOpen}
                title="Item Upgrading"
                iconBackground="shadow-lg"
                modalMaxWidth="max-w-3xl!"
                Icon={() => (
                    <img
                        src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/cgBzeee.png`}
                        alt=""
                        className="mt-0.5 h-11 w-auto"
                    />
                )}
                onOpenChange={setUpgradeItemModalOpen}
            >
                <div className="relative mx-auto overflow-hidden rounded-md bg-white pt-4 md:mt-3 md:w-3/4 dark:border dark:border-gray-700 dark:bg-slate-800">
                    <div className="vignette-sm pointer-events-none absolute inset-0 z-5 size-full object-cover opacity-50 lg:rounded-2xl"></div>
                    {userInventory && <ItemUpgrade userInventory={userInventory} />}
                </div>
            </Modal>
        </>
    );
}
