{"version": 3, "sources": ["../../../../node_modules/@better-fetch/fetch/src/error.ts", "../../../../node_modules/@better-fetch/fetch/src/plugins.ts", "../../../../node_modules/@better-fetch/fetch/src/retry.ts", "../../../../node_modules/@better-fetch/fetch/src/auth.ts", "../../../../node_modules/@better-fetch/fetch/src/utils.ts", "../../../../node_modules/@better-fetch/fetch/src/create-fetch/schema.ts", "../../../../node_modules/@better-fetch/fetch/src/create-fetch/index.ts", "../../../../node_modules/@better-fetch/fetch/src/url.ts", "../../../../node_modules/@better-fetch/fetch/src/fetch.ts", "../../../../node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs", "../../../../node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs", "../../../../node_modules/nanostores/clean-stores/index.js", "../../../../node_modules/nanostores/atom/index.js", "../../../../node_modules/nanostores/listen-keys/index.js", "../../../../node_modules/nanostores/lifecycle/index.js", "../../../../node_modules/better-auth/dist/shared/better-auth.Buni1mmI.mjs"], "sourcesContent": ["export class BetterFetchError extends Error {\n\tconstructor(\n\t\tpublic status: number,\n\t\tpublic statusText: string,\n\t\tpublic error: any,\n\t) {\n\t\tsuper(statusText || status.toString(), {\n\t\t\tcause: error,\n\t\t});\n\t}\n}\n", "import type { StandardSchemaV1 } from \"./standard-schema\";\nimport { Schema } from \"./create-fetch\";\nimport { BetterFetchError } from \"./error\";\nimport type { BetterFetchOption } from \"./types\";\n\nexport type RequestContext<T extends Record<string, any> = any> = {\n\turl: URL | string;\n\theaders: Headers;\n\tbody: any;\n\tmethod: string;\n\tsignal: AbortSignal;\n} & BetterFetchOption<any, any, any, T>;\nexport type ResponseContext = {\n\tresponse: Response;\n\trequest: RequestContext;\n};\nexport type SuccessContext<Res = any> = {\n\tdata: Res;\n\tresponse: Response;\n\trequest: RequestContext;\n};\nexport type ErrorContext = {\n\tresponse: Response;\n\trequest: RequestContext;\n\terror: BetterFetchError & Record<string, any>;\n};\nexport interface FetchHooks<Res = any> {\n\t/**\n\t * a callback function that will be called when a\n\t * request is made.\n\t *\n\t * The returned context object will be reassigned to\n\t * the original request context.\n\t */\n\tonRequest?: <T extends Record<string, any>>(\n\t\tcontext: RequestContext<T>,\n\t) => Promise<RequestContext | void> | RequestContext | void;\n\t/**\n\t * a callback function that will be called when\n\t * response is received. This will be called before\n\t * the response is parsed and returned.\n\t *\n\t * The returned response will be reassigned to the\n\t * original response if it's changed.\n\t */\n\tonResponse?: (\n\t\tcontext: ResponseContext,\n\t) =>\n\t\t| Promise<Response | void | ResponseContext>\n\t\t| Response\n\t\t| ResponseContext\n\t\t| void;\n\t/**\n\t * a callback function that will be called when a\n\t * response is successful.\n\t */\n\tonSuccess?: (context: SuccessContext<Res>) => Promise<void> | void;\n\t/**\n\t * a callback function that will be called when an\n\t * error occurs.\n\t */\n\tonError?: (context: ErrorContext) => Promise<void> | void;\n\t/**\n\t * a callback function that will be called when a\n\t * request is retried.\n\t */\n\tonRetry?: (response: ResponseContext) => Promise<void> | void;\n\t/**\n\t * Options for the hooks\n\t */\n\thookOptions?: {\n\t\t/**\n\t\t * Clone the response\n\t\t * @see https://developer.mozilla.org/en-US/docs/Web/API/Response/clone\n\t\t */\n\t\tcloneResponse?: boolean;\n\t};\n}\n\n/**\n * A plugin that returns an id and hooks\n */\nexport type BetterFetchPlugin = {\n\t/**\n\t * A unique id for the plugin\n\t */\n\tid: string;\n\t/**\n\t * A name for the plugin\n\t */\n\tname: string;\n\t/**\n\t * A description for the plugin\n\t */\n\tdescription?: string;\n\t/**\n\t * A version for the plugin\n\t */\n\tversion?: string;\n\t/**\n\t * Hooks for the plugin\n\t */\n\thooks?: FetchHooks;\n\t/**\n\t * A function that will be called when the plugin is\n\t * initialized. This will be called before the any\n\t * of the other internal functions.\n\t *\n\t * The returned options will be merged with the\n\t * original options.\n\t */\n\tinit?: (\n\t\turl: string,\n\t\toptions?: BetterFetchOption,\n\t) =>\n\t\t| Promise<{\n\t\t\t\turl: string;\n\t\t\t\toptions?: BetterFetchOption;\n\t\t  }>\n\t\t| {\n\t\t\t\turl: string;\n\t\t\t\toptions?: BetterFetchOption;\n\t\t  };\n\t/**\n\t * A schema for the plugin\n\t */\n\tschema?: Schema;\n\t/**\n\t * Additional options that can be passed to the plugin\n\t */\n\tgetOptions?: () => StandardSchemaV1;\n};\n\nexport const initializePlugins = async (\n\turl: string,\n\toptions?: BetterFetchOption,\n) => {\n\tlet opts = options || {};\n\tconst hooks: {\n\t\tonRequest: Array<FetchHooks[\"onRequest\"]>;\n\t\tonResponse: Array<FetchHooks[\"onResponse\"]>;\n\t\tonSuccess: Array<FetchHooks[\"onSuccess\"]>;\n\t\tonError: Array<FetchHooks[\"onError\"]>;\n\t\tonRetry: Array<FetchHooks[\"onRetry\"]>;\n\t} = {\n\t\tonRequest: [options?.onRequest],\n\t\tonResponse: [options?.onResponse],\n\t\tonSuccess: [options?.onSuccess],\n\t\tonError: [options?.onError],\n\t\tonRetry: [options?.onRetry],\n\t};\n\tif (!options || !options?.plugins) {\n\t\treturn {\n\t\t\turl,\n\t\t\toptions: opts,\n\t\t\thooks,\n\t\t};\n\t}\n\tfor (const plugin of options?.plugins || []) {\n\t\tif (plugin.init) {\n\t\t\tconst pluginRes = await plugin.init?.(url.toString(), options);\n\t\t\topts = pluginRes.options || opts;\n\t\t\turl = pluginRes.url;\n\t\t}\n\t\thooks.onRequest.push(plugin.hooks?.onRequest);\n\t\thooks.onResponse.push(plugin.hooks?.onResponse);\n\t\thooks.onSuccess.push(plugin.hooks?.onSuccess);\n\t\thooks.onError.push(plugin.hooks?.onError);\n\t\thooks.onRetry.push(plugin.hooks?.onRetry);\n\t}\n\n\treturn {\n\t\turl,\n\t\toptions: opts,\n\t\thooks,\n\t};\n};\n", "export type RetryCondition = (\n\tresponse: Response | null,\n) => boolean | Promise<boolean>;\n\nexport type LinearRetry = {\n\ttype: \"linear\";\n\tattempts: number;\n\tdelay: number;\n\tshouldRetry?: RetryCondition;\n};\n\nexport type ExponentialRetry = {\n\ttype: \"exponential\";\n\tattempts: number;\n\tbaseDelay: number;\n\tmaxDelay: number;\n\tshouldRetry?: RetryCondition;\n};\n\nexport type RetryOptions = LinearRetry | ExponentialRetry | number;\n\nexport interface RetryStrategy {\n\tshouldAttemptRetry(\n\t\tattempt: number,\n\t\tresponse: Response | null,\n\t): Promise<boolean>;\n\tgetDelay(attempt: number): number;\n}\n\nclass LinearRetryStrategy implements RetryStrategy {\n\tconstructor(private options: LinearRetry) {}\n\n\tshouldAttemptRetry(\n\t\tattempt: number,\n\t\tresponse: Response | null,\n\t): Promise<boolean> {\n\t\tif (this.options.shouldRetry) {\n\t\t\treturn Promise.resolve(\n\t\t\t\tattempt < this.options.attempts && this.options.shouldRetry(response),\n\t\t\t);\n\t\t}\n\t\treturn Promise.resolve(attempt < this.options.attempts);\n\t}\n\n\tgetDelay(): number {\n\t\treturn this.options.delay;\n\t}\n}\n\nclass ExponentialRetryStrategy implements RetryStrategy {\n\tconstructor(private options: ExponentialRetry) {}\n\n\tshouldAttemptRetry(\n\t\tattempt: number,\n\t\tresponse: Response | null,\n\t): Promise<boolean> {\n\t\tif (this.options.shouldRetry) {\n\t\t\treturn Promise.resolve(\n\t\t\t\tattempt < this.options.attempts && this.options.shouldRetry(response),\n\t\t\t);\n\t\t}\n\t\treturn Promise.resolve(attempt < this.options.attempts);\n\t}\n\n\tgetDelay(attempt: number): number {\n\t\tconst delay = Math.min(\n\t\t\tthis.options.maxDelay,\n\t\t\tthis.options.baseDelay * 2 ** attempt,\n\t\t);\n\t\treturn delay;\n\t}\n}\n\nexport function createRetryStrategy(options: RetryOptions): RetryStrategy {\n\tif (typeof options === \"number\") {\n\t\treturn new LinearRetryStrategy({\n\t\t\ttype: \"linear\",\n\t\t\tattempts: options,\n\t\t\tdelay: 1000,\n\t\t});\n\t}\n\n\tswitch (options.type) {\n\t\tcase \"linear\":\n\t\t\treturn new LinearRetryStrategy(options);\n\t\tcase \"exponential\":\n\t\t\treturn new ExponentialRetryStrategy(options);\n\t\tdefault:\n\t\t\tthrow new Error(\"Invalid retry strategy\");\n\t}\n}\n", "import type { BetterFetchOption } from \"./types\";\n\nexport type typeOrTypeReturning<T> = T | (() => T);\n/**\n * Bearer token authentication\n *\n * the value of `token` will be added to a header as\n * `auth: Bearer token`,\n */\nexport type Bearer = {\n\ttype: \"Bearer\";\n\ttoken: typeOrTypeReturning<string | undefined | Promise<string | undefined>>;\n};\n\n/**\n * Basic auth\n */\nexport type Basic = {\n\ttype: \"Basic\";\n\tusername: typeOrTypeReturning<string | undefined>;\n\tpassword: typeOrTypeReturning<string | undefined>;\n};\n\n/**\n * Custom auth\n *\n * @param prefix - prefix of the header\n * @param value - value of the header\n *\n * @example\n * ```ts\n * {\n *  type: \"Custom\",\n *  prefix: \"Token\",\n *  value: \"token\"\n * }\n * ```\n */\nexport type Custom = {\n\ttype: \"Custom\";\n\tprefix: typeOrTypeReturning<string | undefined>;\n\tvalue: typeOrTypeReturning<string | undefined>;\n};\n\nexport type Auth = Bearer | Basic | Custom;\n\nexport const getAuthHeader = async (options?: BetterFetchOption) => {\n\tconst headers: Record<string, string> = {};\n\tconst getValue = async (\n\t\tvalue: typeOrTypeReturning<\n\t\t\tstring | undefined | Promise<string | undefined>\n\t\t>,\n\t) => (typeof value === \"function\" ? await value() : value);\n\tif (options?.auth) {\n\t\tif (options.auth.type === \"Bearer\") {\n\t\t\tconst token = await getValue(options.auth.token);\n\t\t\tif (!token) {\n\t\t\t\treturn headers;\n\t\t\t}\n\t\t\theaders[\"authorization\"] = `Bearer ${token}`;\n\t\t} else if (options.auth.type === \"Basic\") {\n\t\t\tconst username = getValue(options.auth.username);\n\t\t\tconst password = getValue(options.auth.password);\n\t\t\tif (!username || !password) {\n\t\t\t\treturn headers;\n\t\t\t}\n\t\t\theaders[\"authorization\"] = `Basic ${btoa(`${username}:${password}`)}`;\n\t\t} else if (options.auth.type === \"Custom\") {\n\t\t\tconst value = getValue(options.auth.value);\n\t\t\tif (!value) {\n\t\t\t\treturn headers;\n\t\t\t}\n\t\t\theaders[\"authorization\"] = `${getValue(options.auth.prefix)} ${value}`;\n\t\t}\n\t}\n\treturn headers;\n};\n", "import type { StandardSchemaV1 } from \"./standard-schema\";\nimport { getAuthHeader } from \"./auth\";\nimport { methods } from \"./create-fetch\";\nimport type { BetterFetchOption, FetchEsque } from \"./types\";\n\nconst JSON_RE = /^application\\/(?:[\\w!#$%&*.^`~-]*\\+)?json(;.+)?$/i;\n\nexport type ResponseType = \"json\" | \"text\" | \"blob\";\nexport function detectResponseType(request: Response): ResponseType {\n\tconst _contentType = request.headers.get(\"content-type\");\n\tconst textTypes = new Set([\n\t\t\"image/svg\",\n\t\t\"application/xml\",\n\t\t\"application/xhtml\",\n\t\t\"application/html\",\n\t]);\n\tif (!_contentType) {\n\t\treturn \"json\";\n\t}\n\tconst contentType = _contentType.split(\";\").shift() || \"\";\n\tif (JSON_RE.test(contentType)) {\n\t\treturn \"json\";\n\t}\n\tif (textTypes.has(contentType) || contentType.startsWith(\"text/\")) {\n\t\treturn \"text\";\n\t}\n\treturn \"blob\";\n}\n\nexport function isJSONParsable(value: any) {\n\ttry {\n\t\tJSON.parse(value);\n\t\treturn true;\n\t} catch (error) {\n\t\treturn false;\n\t}\n}\n\n//https://github.com/unjs/ofetch/blob/main/src/utils.ts\nexport function isJSONSerializable(value: any) {\n\tif (value === undefined) {\n\t\treturn false;\n\t}\n\tconst t = typeof value;\n\tif (t === \"string\" || t === \"number\" || t === \"boolean\" || t === null) {\n\t\treturn true;\n\t}\n\tif (t !== \"object\") {\n\t\treturn false;\n\t}\n\tif (Array.isArray(value)) {\n\t\treturn true;\n\t}\n\tif (value.buffer) {\n\t\treturn false;\n\t}\n\treturn (\n\t\t(value.constructor && value.constructor.name === \"Object\") ||\n\t\ttypeof value.toJSON === \"function\"\n\t);\n}\n\nexport function jsonParse(text: string) {\n\ttry {\n\t\treturn JSON.parse(text);\n\t} catch (error) {\n\t\treturn text;\n\t}\n}\n\nexport function isFunction(value: any): value is () => any {\n\treturn typeof value === \"function\";\n}\n\nexport function getFetch(options?: BetterFetchOption): FetchEsque {\n\tif (options?.customFetchImpl) {\n\t\treturn options.customFetchImpl;\n\t}\n\tif (typeof globalThis !== \"undefined\" && isFunction(globalThis.fetch)) {\n\t\treturn globalThis.fetch;\n\t}\n\tif (typeof window !== \"undefined\" && isFunction(window.fetch)) {\n\t\treturn window.fetch;\n\t}\n\tthrow new Error(\"No fetch implementation found\");\n}\n\nexport function isPayloadMethod(method?: string) {\n\tif (!method) {\n\t\treturn false;\n\t}\n\tconst payloadMethod = [\"POST\", \"PUT\", \"PATCH\", \"DELETE\"];\n\treturn payloadMethod.includes(method.toUpperCase());\n}\n\nexport function isRouteMethod(method?: string) {\n\tconst routeMethod = [\"GET\", \"POST\", \"PUT\", \"PATCH\", \"DELETE\"];\n\tif (!method) {\n\t\treturn false;\n\t}\n\treturn routeMethod.includes(method.toUpperCase());\n}\n\nexport async function getHeaders(opts?: BetterFetchOption) {\n\tconst headers = new Headers(opts?.headers);\n\tconst authHeader = await getAuthHeader(opts);\n\tfor (const [key, value] of Object.entries(authHeader || {})) {\n\t\theaders.set(key, value);\n\t}\n\tif (!headers.has(\"content-type\")) {\n\t\tconst t = detectContentType(opts?.body);\n\t\tif (t) {\n\t\t\theaders.set(\"content-type\", t);\n\t\t}\n\t}\n\n\treturn headers;\n}\n\nexport function getURL(url: string, options?: BetterFetchOption) {\n\tif (url.startsWith(\"@\")) {\n\t\tconst m = url.toString().split(\"@\")[1].split(\"/\")[0];\n\t\tif (methods.includes(m)) {\n\t\t\turl = url.replace(`@${m}/`, \"/\");\n\t\t}\n\t}\n\tlet _url: string | URL;\n\ttry {\n\t\tif (url.startsWith(\"http\")) {\n\t\t\t_url = url;\n\t\t} else {\n\t\t\tlet baseURL = options?.baseURL;\n\t\t\tif (baseURL && !baseURL?.endsWith(\"/\")) {\n\t\t\t\tbaseURL = baseURL + \"/\";\n\t\t\t}\n\t\t\tif (url.startsWith(\"/\")) {\n\t\t\t\t_url = new URL(url.substring(1), baseURL);\n\t\t\t} else {\n\t\t\t\t_url = new URL(url, options?.baseURL);\n\t\t\t}\n\t\t}\n\t} catch (e) {\n\t\tif (e instanceof TypeError) {\n\t\t\tif (!options?.baseURL) {\n\t\t\t\tthrow TypeError(\n\t\t\t\t\t`Invalid URL ${url}. Are you passing in a relative url but not setting the baseURL?`,\n\t\t\t\t);\n\t\t\t}\n\t\t\tthrow TypeError(\n\t\t\t\t`Invalid URL ${url}. Please validate that you are passing the correct input.`,\n\t\t\t);\n\t\t}\n\t\tthrow e;\n\t}\n\n\t/**\n\t * Dynamic Parameters.\n\t */\n\tif (options?.params) {\n\t\tif (Array.isArray(options?.params)) {\n\t\t\tconst params = options?.params\n\t\t\t\t? Array.isArray(options.params)\n\t\t\t\t\t? `/${options.params.join(\"/\")}`\n\t\t\t\t\t: `/${Object.values(options.params).join(\"/\")}`\n\t\t\t\t: \"\";\n\t\t\t_url = _url.toString().split(\"/:\")[0];\n\t\t\t_url = `${_url.toString()}${params}`;\n\t\t} else {\n\t\t\tfor (const [key, value] of Object.entries(options?.params)) {\n\t\t\t\t_url = _url.toString().replace(`:${key}`, String(value));\n\t\t\t}\n\t\t}\n\t}\n\tconst __url = new URL(_url);\n\t/**\n\t * Query Parameters\n\t */\n\tconst queryParams = options?.query;\n\tif (queryParams) {\n\t\tfor (const [key, value] of Object.entries(queryParams)) {\n\t\t\t__url.searchParams.append(key, String(value));\n\t\t}\n\t}\n\treturn __url;\n}\n\nexport function detectContentType(body: any) {\n\tif (isJSONSerializable(body)) {\n\t\treturn \"application/json\";\n\t}\n\n\treturn null;\n}\n\nexport function getBody(options?: BetterFetchOption) {\n\tif (!options?.body) {\n\t\treturn null;\n\t}\n\tconst headers = new Headers(options?.headers);\n\tif (isJSONSerializable(options.body) && !headers.has(\"content-type\")) {\n\t\tfor (const [key, value] of Object.entries(options?.body)) {\n\t\t\tif (value instanceof Date) {\n\t\t\t\toptions.body[key] = value.toISOString();\n\t\t\t}\n\t\t}\n\t\treturn JSON.stringify(options.body);\n\t}\n\n\treturn options.body;\n}\n\nexport function getMethod(url: string, options?: BetterFetchOption) {\n\tif (options?.method) {\n\t\treturn options.method.toUpperCase();\n\t}\n\tif (url.startsWith(\"@\")) {\n\t\tconst pMethod = url.split(\"@\")[1]?.split(\"/\")[0];\n\t\tif (!methods.includes(pMethod)) {\n\t\t\treturn options?.body ? \"POST\" : \"GET\";\n\t\t}\n\t\treturn pMethod.toUpperCase();\n\t}\n\treturn options?.body ? \"POST\" : \"GET\";\n}\n\nexport function getTimeout(\n\toptions?: BetterFetchOption,\n\tcontroller?: AbortController,\n) {\n\tlet abortTimeout: ReturnType<typeof setTimeout> | undefined;\n\tif (!options?.signal && options?.timeout) {\n\t\tabortTimeout = setTimeout(() => controller?.abort(), options?.timeout);\n\t}\n\treturn {\n\t\tabortTimeout,\n\t\tclearTimeout: () => {\n\t\t\tif (abortTimeout) {\n\t\t\t\tclearTimeout(abortTimeout);\n\t\t\t}\n\t\t},\n\t};\n}\n\nexport function bodyParser(data: any, responseType: ResponseType) {\n\tif (responseType === \"json\") {\n\t\treturn JSON.parse(data);\n\t}\n\treturn data;\n}\n\nexport class ValidationError extends Error {\n\tpublic readonly issues: ReadonlyArray<StandardSchemaV1.Issue>;\n\n\tconstructor(issues: ReadonlyArray<StandardSchemaV1.Issue>, message?: string) {\n\t\t// Default message fallback in case one isn't supplied.\n\t\tsuper(message || JSON.stringify(issues, null, 2));\n\t\tthis.issues = issues;\n\n\t\t// Set the prototype explicitly to ensure that instanceof works correctly.\n\t\tObject.setPrototypeOf(this, ValidationError.prototype);\n\t}\n}\n\nexport async function parseStandardSchema<TSchema extends StandardSchemaV1>(\n\tschema: TSchema,\n\tinput: StandardSchemaV1.InferInput<TSchema>,\n): Promise<StandardSchemaV1.InferOutput<TSchema>> {\n\tlet result = await schema[\"~standard\"].validate(input);\n\n\tif (result.issues) {\n\t\tthrow new ValidationError(result.issues);\n\t}\n\treturn result.value;\n}\n", "import type { StandardSchemaV1 } from \"../standard-schema\";\nimport type { StringLiteralUnion } from \"../type-utils\";\n\nexport type FetchSchema = {\n\tinput?: StandardSchemaV1;\n\toutput?: StandardSchemaV1;\n\tquery?: StandardSchemaV1;\n\tparams?: StandardSchemaV1<Record<string, unknown>> | undefined;\n\tmethod?: Methods;\n};\n\nexport type Methods = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\n\nexport const methods = [\"get\", \"post\", \"put\", \"patch\", \"delete\"];\n\ntype RouteKey = StringLiteralUnion<`@${Methods}/`>;\n\nexport type FetchSchemaRoutes = {\n\t[key in RouteKey]?: FetchSchema;\n};\n\nexport const createSchema = <\n\tF extends FetchSchemaRoutes,\n\tS extends SchemaConfig,\n>(\n\tschema: F,\n\tconfig?: S,\n) => {\n\treturn {\n\t\tschema: schema as F,\n\t\tconfig: config as S,\n\t};\n};\n\nexport type SchemaConfig = {\n\tstrict?: boolean;\n\t/**\n\t * A prefix that will be prepended when it's\n\t * calling the schema.\n\t *\n\t * NOTE: Make sure to handle converting\n\t * the prefix to the baseURL in the init\n\t * function if you you are defining for a\n\t * plugin.\n\t */\n\tprefix?: \"\" | (string & Record<never, never>);\n\t/**\n\t * The base url of the schema. By default it's the baseURL of the fetch instance.\n\t */\n\tbaseURL?: \"\" | (string & Record<never, never>);\n};\n\nexport type Schema = {\n\tschema: FetchSchemaRoutes;\n\tconfig: SchemaConfig;\n};\n", "import { betterFetch } from \"../fetch\";\nimport { BetterFetchPlugin } from \"../plugins\";\nimport type { BetterFetchOption } from \"../types\";\nimport { parseStandardSchema } from \"../utils\";\nimport type { BetterFetch, CreateFetchOption } from \"./types\";\n\nexport const applySchemaPlugin = (config: CreateFetchOption) =>\n\t({\n\t\tid: \"apply-schema\",\n\t\tname: \"Apply Schema\",\n\t\tversion: \"1.0.0\",\n\t\tasync init(url, options) {\n\t\t\tconst schema =\n\t\t\t\tconfig.plugins?.find((plugin) =>\n\t\t\t\t\tplugin.schema?.config\n\t\t\t\t\t\t? url.startsWith(plugin.schema.config.baseURL || \"\") ||\n\t\t\t\t\t\t\turl.startsWith(plugin.schema.config.prefix || \"\")\n\t\t\t\t\t\t: false,\n\t\t\t\t)?.schema || config.schema;\n\t\t\tif (schema) {\n\t\t\t\tlet urlKey = url;\n\t\t\t\tif (schema.config?.prefix) {\n\t\t\t\t\tif (urlKey.startsWith(schema.config.prefix)) {\n\t\t\t\t\t\turlKey = urlKey.replace(schema.config.prefix, \"\");\n\t\t\t\t\t\tif (schema.config.baseURL) {\n\t\t\t\t\t\t\turl = url.replace(schema.config.prefix, schema.config.baseURL);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (schema.config?.baseURL) {\n\t\t\t\t\tif (urlKey.startsWith(schema.config.baseURL)) {\n\t\t\t\t\t\turlKey = urlKey.replace(schema.config.baseURL, \"\");\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst keySchema = schema.schema[urlKey];\n\t\t\t\tif (keySchema) {\n\t\t\t\t\tlet opts = {\n\t\t\t\t\t\t...options,\n\t\t\t\t\t\tmethod: keySchema.method,\n\t\t\t\t\t\toutput: keySchema.output,\n\t\t\t\t\t};\n\t\t\t\t\tif (!options?.disableValidation) {\n\t\t\t\t\t\topts = {\n\t\t\t\t\t\t\t...opts,\n\t\t\t\t\t\t\tbody: keySchema.input\n\t\t\t\t\t\t\t\t? await parseStandardSchema(keySchema.input, options?.body)\n\t\t\t\t\t\t\t\t: options?.body,\n\t\t\t\t\t\t\tparams: keySchema.params\n\t\t\t\t\t\t\t\t? await parseStandardSchema(keySchema.params, options?.params)\n\t\t\t\t\t\t\t\t: options?.params,\n\t\t\t\t\t\t\tquery: keySchema.query\n\t\t\t\t\t\t\t\t? await parseStandardSchema(keySchema.query, options?.query)\n\t\t\t\t\t\t\t\t: options?.query,\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\treturn {\n\t\t\t\t\t\turl,\n\t\t\t\t\t\toptions: opts,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn {\n\t\t\t\turl,\n\t\t\t\toptions,\n\t\t\t};\n\t\t},\n\t}) satisfies BetterFetchPlugin;\n\nexport const createFetch = <Option extends CreateFetchOption>(\n\tconfig?: Option,\n) => {\n\tasync function $fetch(url: string, options?: BetterFetchOption) {\n\t\tconst opts = {\n\t\t\t...config,\n\t\t\t...options,\n\t\t\tplugins: [...(config?.plugins || []), applySchemaPlugin(config || {})],\n\t\t} as BetterFetchOption;\n\n\t\tif (config?.catchAllError) {\n\t\t\ttry {\n\t\t\t\treturn await betterFetch(url, opts);\n\t\t\t} catch (error) {\n\t\t\t\treturn {\n\t\t\t\t\tdata: null,\n\t\t\t\t\terror: {\n\t\t\t\t\t\tstatus: 500,\n\t\t\t\t\t\tstatusText: \"Fetch Error\",\n\t\t\t\t\t\tmessage:\n\t\t\t\t\t\t\t\"Fetch related error. Captured by catchAllError option. See error property for more details.\",\n\t\t\t\t\t\terror,\n\t\t\t\t\t},\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\treturn await betterFetch(url, opts);\n\t}\n\treturn $fetch as BetterFetch<Option>;\n};\n\nexport * from \"./schema\";\nexport * from \"./types\";\n", "import { methods } from \"./create-fetch\";\nimport { BetterFetchOption } from \"./types\";\n\n/**\n * Normalize URL\n */\nexport function getURL(url: string, option?: BetterFetchOption) {\n\tlet { baseURL, params, query } = option || {\n\t\tquery: {},\n\t\tparams: {},\n\t\tbaseURL: \"\",\n\t};\n\tlet basePath = url.startsWith(\"http\")\n\t\t? url.split(\"/\").slice(0, 3).join(\"/\")\n\t\t: baseURL || \"\";\n\n\t/**\n\t * Remove method modifiers\n\t */\n\tif (url.startsWith(\"@\")) {\n\t\tconst m = url.toString().split(\"@\")[1].split(\"/\")[0];\n\t\tif (methods.includes(m)) {\n\t\t\turl = url.replace(`@${m}/`, \"/\");\n\t\t}\n\t}\n\n\tif (!basePath.endsWith(\"/\")) basePath += \"/\";\n\tlet [path, urlQuery] = url.replace(basePath, \"\").split(\"?\");\n\tconst queryParams = new URLSearchParams(urlQuery);\n\tfor (const [key, value] of Object.entries(query || {})) {\n\t\tif (value == null) continue;\n\t\tqueryParams.set(key, String(value));\n\t}\n\tif (params) {\n\t\tif (Array.isArray(params)) {\n\t\t\tconst paramPaths = path.split(\"/\").filter((p) => p.startsWith(\":\"));\n\t\t\tfor (const [index, key] of paramPaths.entries()) {\n\t\t\t\tconst value = params[index];\n\t\t\t\tpath = path.replace(key, value);\n\t\t\t}\n\t\t} else {\n\t\t\tfor (const [key, value] of Object.entries(params)) {\n\t\t\t\tpath = path.replace(`:${key}`, String(value));\n\t\t\t}\n\t\t}\n\t}\n\n\tpath = path.split(\"/\").map(encodeURIComponent).join(\"/\");\n\tif (path.startsWith(\"/\")) path = path.slice(1);\n\tlet queryParamString = queryParams.toString();\n\tqueryParamString =\n\t\tqueryParamString.length > 0 ? `?${queryParamString}`.replace(/\\+/g, \"%20\") : \"\";\n\tif (!basePath.startsWith(\"http\")) {\n\t\treturn `${basePath}${path}${queryParamString}`;\n\t}\n\tconst _url = new URL(`${path}${queryParamString}`, basePath);\n\treturn _url;\n}\n", "import type { StandardSchemaV1 } from \"./standard-schema\";\nimport { BetterFetchError } from \"./error\";\nimport { initializePlugins } from \"./plugins\";\nimport { createRetryStrategy } from \"./retry\";\nimport type { BetterFetchOption, BetterFetchResponse } from \"./types\";\nimport { getURL } from \"./url\";\nimport {\n\tdetectResponseType,\n\tgetBody,\n\tgetFetch,\n\tgetHeaders,\n\tgetMethod,\n\tgetTimeout,\n\tisJSONParsable,\n\tjsonParse,\n\tparseStandardSchema,\n} from \"./utils\";\n\nexport const betterFetch = async <\n\tTRes extends Option[\"output\"] extends StandardSchemaV1\n\t\t? StandardSchemaV1.InferOutput<Option[\"output\"]>\n\t\t: unknown,\n\tTErr = unknown,\n\tOption extends BetterFetchOption = BetterFetchOption<any, any, any, TRes>,\n>(\n\turl: string,\n\toptions?: Option,\n): Promise<\n\tBetterFetchResponse<\n\t\tTRes,\n\t\tTErr,\n\t\tOption[\"throw\"] extends true ? true : TErr extends false ? true : false\n\t>\n> => {\n\tconst {\n\t\thooks,\n\t\turl: __url,\n\t\toptions: opts,\n\t} = await initializePlugins(url, options);\n\tconst fetch = getFetch(opts);\n\tconst controller = new AbortController();\n\tconst signal = opts.signal ?? controller.signal;\n\tconst _url = getURL(__url, opts);\n\tconst body = getBody(opts);\n\tconst headers = await getHeaders(opts);\n\tconst method = getMethod(__url, opts);\n\tlet context = {\n\t\t...opts,\n\t\turl: _url,\n\t\theaders,\n\t\tbody,\n\t\tmethod,\n\t\tsignal,\n\t};\n\t/**\n\t * Run all on request hooks\n\t */\n\tfor (const onRequest of hooks.onRequest) {\n\t\tif (onRequest) {\n\t\t\tconst res = await onRequest(context);\n\t\t\tif (res instanceof Object) {\n\t\t\t\tcontext = res;\n\t\t\t}\n\t\t}\n\t}\n\tif (\n\t\t(\"pipeTo\" in (context as any) &&\n\t\t\ttypeof (context as any).pipeTo === \"function\") ||\n\t\ttypeof options?.body?.pipe === \"function\"\n\t) {\n\t\tif (!(\"duplex\" in context)) {\n\t\t\tcontext.duplex = \"half\";\n\t\t}\n\t}\n\n\tconst { clearTimeout } = getTimeout(opts, controller);\n\tlet response = await fetch(context.url, context);\n\tclearTimeout();\n\n\tconst responseContext = {\n\t\tresponse,\n\t\trequest: context,\n\t};\n\n\tfor (const onResponse of hooks.onResponse) {\n\t\tif (onResponse) {\n\t\t\tconst r = await onResponse({\n\t\t\t\t...responseContext,\n\t\t\t\tresponse: options?.hookOptions?.cloneResponse\n\t\t\t\t\t? response.clone()\n\t\t\t\t\t: response,\n\t\t\t});\n\t\t\tif (r instanceof Response) {\n\t\t\t\tresponse = r;\n\t\t\t} else if (r instanceof Object) {\n\t\t\t\tresponse = r.response;\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * OK Branch\n\t */\n\tif (response.ok) {\n\t\tconst hasBody = context.method !== \"HEAD\";\n\t\tif (!hasBody) {\n\t\t\treturn {\n\t\t\t\tdata: \"\" as any,\n\t\t\t\terror: null,\n\t\t\t} as any;\n\t\t}\n\t\tconst responseType = detectResponseType(response);\n\t\tconst successContext = {\n\t\t\tdata: \"\" as any,\n\t\t\tresponse,\n\t\t\trequest: context,\n\t\t};\n\t\tif (responseType === \"json\" || responseType === \"text\") {\n\t\t\tconst text = await response.text();\n\t\t\tconst parser = context.jsonParser ?? jsonParse;\n\t\t\tconst data = await parser(text);\n\t\t\tsuccessContext.data = data;\n\t\t} else {\n\t\t\tsuccessContext.data = await response[responseType]();\n\t\t}\n\n\t\t/**\n\t\t * Parse the data if the output schema is defined\n\t\t */\n\t\tif (context?.output) {\n\t\t\tif (context.output && !context.disableValidation) {\n\t\t\t\tsuccessContext.data = await parseStandardSchema(\n\t\t\t\t\tcontext.output as StandardSchemaV1,\n\t\t\t\t\tsuccessContext.data,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tfor (const onSuccess of hooks.onSuccess) {\n\t\t\tif (onSuccess) {\n\t\t\t\tawait onSuccess({\n\t\t\t\t\t...successContext,\n\t\t\t\t\tresponse: options?.hookOptions?.cloneResponse\n\t\t\t\t\t\t? response.clone()\n\t\t\t\t\t\t: response,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (options?.throw) {\n\t\t\treturn successContext.data as any;\n\t\t}\n\n\t\treturn {\n\t\t\tdata: successContext.data,\n\t\t\terror: null,\n\t\t} as any;\n\t}\n\tconst parser = options?.jsonParser ?? jsonParse;\n\tconst responseText = await response.text();\n\tconst isJSONResponse = isJSONParsable(responseText);\n\tconst errorObject = isJSONResponse ? await parser(responseText) : null;\n\t/**\n\t * Error Branch\n\t */\n\tconst errorContext = {\n\t\tresponse,\n\t\tresponseText,\n\t\trequest: context,\n\t\terror: {\n\t\t\t...errorObject,\n\t\t\tstatus: response.status,\n\t\t\tstatusText: response.statusText,\n\t\t},\n\t};\n\tfor (const onError of hooks.onError) {\n\t\tif (onError) {\n\t\t\tawait onError({\n\t\t\t\t...errorContext,\n\t\t\t\tresponse: options?.hookOptions?.cloneResponse\n\t\t\t\t\t? response.clone()\n\t\t\t\t\t: response,\n\t\t\t});\n\t\t}\n\t}\n\n\tif (options?.retry) {\n\t\tconst retryStrategy = createRetryStrategy(options.retry);\n\t\tconst _retryAttempt = options.retryAttempt ?? 0;\n\t\tif (await retryStrategy.shouldAttemptRetry(_retryAttempt, response)) {\n\t\t\tfor (const onRetry of hooks.onRetry) {\n\t\t\t\tif (onRetry) {\n\t\t\t\t\tawait onRetry(responseContext);\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst delay = retryStrategy.getDelay(_retryAttempt);\n\t\t\tawait new Promise((resolve) => setTimeout(resolve, delay));\n\t\t\treturn await betterFetch(url, {\n\t\t\t\t...options,\n\t\t\t\tretryAttempt: _retryAttempt + 1,\n\t\t\t});\n\t\t}\n\t}\n\n\tif (options?.throw) {\n\t\tthrow new BetterFetchError(\n\t\t\tresponse.status,\n\t\t\tresponse.statusText,\n\t\t\tisJSONResponse ? errorObject : responseText,\n\t\t);\n\t}\n\treturn {\n\t\tdata: null,\n\t\terror: {\n\t\t\t...errorObject,\n\t\t\tstatus: response.status,\n\t\t\tstatusText: response.statusText,\n\t\t},\n\t} as any;\n};\n", "const _envShim = /* @__PURE__ */ Object.create(null);\nconst _getEnv = (useShim) => globalThis.process?.env || //@ts-expect-error\nglobalThis.Deno?.env.toObject() || //@ts-expect-error\nglobalThis.__env__ || (useShim ? _envShim : globalThis);\nconst env = new Proxy(_envShim, {\n  get(_, prop) {\n    const env2 = _getEnv();\n    return env2[prop] ?? _envShim[prop];\n  },\n  has(_, prop) {\n    const env2 = _getEnv();\n    return prop in env2 || prop in _envShim;\n  },\n  set(_, prop, value) {\n    const env2 = _getEnv(true);\n    env2[prop] = value;\n    return true;\n  },\n  deleteProperty(_, prop) {\n    if (!prop) {\n      return false;\n    }\n    const env2 = _getEnv(true);\n    delete env2[prop];\n    return true;\n  },\n  ownKeys() {\n    const env2 = _getEnv(true);\n    return Object.keys(env2);\n  }\n});\nfunction toBoolean(val) {\n  return val ? val !== \"false\" : false;\n}\nconst nodeENV = typeof process !== \"undefined\" && process.env && process.env.NODE_ENV || \"\";\nconst isProduction = nodeENV === \"production\";\nconst isDevelopment = nodeENV === \"dev\" || nodeENV === \"development\";\nconst isTest = nodeENV === \"test\" || toBoolean(env.TEST);\n\nexport { isProduction as a, isDevelopment as b, env as e, isTest as i };\n", "class BetterAuthError extends Error {\n  constructor(message, cause) {\n    super(message);\n    this.name = \"BetterAuthError\";\n    this.message = message;\n    this.cause = cause;\n    this.stack = \"\";\n  }\n}\nclass MissingDependencyError extends BetterAuthError {\n  constructor(pkgName) {\n    super(\n      `The package \"${pkgName}\" is required. Make sure it is installed.`,\n      pkgName\n    );\n  }\n}\n\nexport { BetterAuthError as B, MissingDependencyError as M };\n", "import { cleanTasks } from '../task/index.js'\n\nexport let clean = Symbol('clean')\n\nexport let cleanStores = (...stores) => {\n  if (process.env.NODE_ENV === 'production') {\n    throw new Error(\n      'cleanStores() can be used only during development or tests'\n    )\n  }\n  cleanTasks()\n  for (let $store of stores) {\n    if ($store) {\n      if ($store.mocked) delete $store.mocked\n      if ($store[clean]) $store[clean]()\n    }\n  }\n}\n", "import { clean } from '../clean-stores/index.js'\n\nlet listenerQueue = []\nlet lqIndex = 0\nconst QUEUE_ITEMS_PER_LISTENER = 4\nexport let epoch = 0\n\nexport let atom = (initialValue) => {\n  let listeners = []\n  let $atom = {\n    get() {\n      if (!$atom.lc) {\n        $atom.listen(() => {})()\n      }\n      return $atom.value\n    },\n    lc: 0,\n    listen(listener) {\n      $atom.lc = listeners.push(listener)\n\n      return () => {\n        for (let i = lqIndex + QUEUE_ITEMS_PER_LISTENER; i < listenerQueue.length;) {\n          if (listenerQueue[i] === listener) {\n            listenerQueue.splice(i, QUEUE_ITEMS_PER_LISTENER)\n          } else {\n            i += QUEUE_ITEMS_PER_LISTENER\n          }\n        }\n\n        let index = listeners.indexOf(listener)\n        if (~index) {\n          listeners.splice(index, 1)\n          if (!--$atom.lc) $atom.off()\n        }\n      }\n    },\n    notify(oldValue, changedKey) {\n      epoch++\n      let runListenerQueue = !listenerQueue.length\n      for (let listener of listeners) {\n        listenerQueue.push(\n          listener,\n          $atom.value,\n          oldValue,\n          changedKey\n        )\n      }\n\n      if (runListenerQueue) {\n        for (lqIndex = 0; lqIndex < listenerQueue.length; lqIndex += QUEUE_ITEMS_PER_LISTENER) {\n            listenerQueue[lqIndex](\n              listenerQueue[lqIndex + 1],\n              listenerQueue[lqIndex + 2],\n              listenerQueue[lqIndex + 3]\n            )\n        }\n        listenerQueue.length = 0\n      }\n    },\n    /* It will be called on last listener unsubscribing.\n       We will redefine it in onMount and onStop. */\n    off() {},\n    set(newValue) {\n      let oldValue = $atom.value\n      if (oldValue !== newValue) {\n        $atom.value = newValue\n        $atom.notify(oldValue)\n      }\n    },\n    subscribe(listener) {\n      let unbind = $atom.listen(listener)\n      listener($atom.value)\n      return unbind\n    },\n    value: initialValue\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    $atom[clean] = () => {\n      listeners = []\n      $atom.lc = 0\n      $atom.off()\n    }\n  }\n\n  return $atom\n}\n", "export function listenKeys($store, keys, listener) {\n  let keysSet = new Set(keys).add(undefined)\n  return $store.listen((value, oldValue, changed) => {\n    if (keysSet.has(changed)) {\n      listener(value, oldValue, changed)\n    }\n  })\n}\n\nexport function subscribeKeys($store, keys, listener) {\n  let unbind = listenKeys($store, keys, listener)\n  listener($store.value)\n  return unbind\n}\n", "import { clean } from '../clean-stores/index.js'\n\nconst START = 0\nconst STOP = 1\nconst SET = 2\nconst NOTIFY = 3\nconst MOUNT = 5\nconst UNMOUNT = 6\nconst REVERT_MUTATION = 10\n\nexport let on = (object, listener, eventKey, mutateStore) => {\n  object.events = object.events || {}\n  if (!object.events[eventKey + REVERT_MUTATION]) {\n    object.events[eventKey + REVERT_MUTATION] = mutateStore(eventProps => {\n      // eslint-disable-next-line no-sequences\n      object.events[eventKey].reduceRight((event, l) => (l(event), event), {\n        shared: {},\n        ...eventProps\n      })\n    })\n  }\n  object.events[eventKey] = object.events[eventKey] || []\n  object.events[eventKey].push(listener)\n  return () => {\n    let currentListeners = object.events[eventKey]\n    let index = currentListeners.indexOf(listener)\n    currentListeners.splice(index, 1)\n    if (!currentListeners.length) {\n      delete object.events[eventKey]\n      object.events[eventKey + REVERT_MUTATION]()\n      delete object.events[eventKey + REVERT_MUTATION]\n    }\n  }\n}\n\nexport let onStart = ($store, listener) =>\n  on($store, listener, START, runListeners => {\n    let originListen = $store.listen\n    $store.listen = arg => {\n      if (!$store.lc && !$store.starting) {\n        $store.starting = true\n        runListeners()\n        delete $store.starting\n      }\n      return originListen(arg)\n    }\n    return () => {\n      $store.listen = originListen\n    }\n  })\n\nexport let onStop = ($store, listener) =>\n  on($store, listener, STOP, runListeners => {\n    let originOff = $store.off\n    $store.off = () => {\n      runListeners()\n      originOff()\n    }\n    return () => {\n      $store.off = originOff\n    }\n  })\n\nexport let onSet = ($store, listener) =>\n  on($store, listener, SET, runListeners => {\n    let originSet = $store.set\n    let originSetKey = $store.setKey\n    if ($store.setKey) {\n      $store.setKey = (changed, changedValue) => {\n        let isAborted\n        let abort = () => {\n          isAborted = true\n        }\n\n        runListeners({\n          abort,\n          changed,\n          newValue: { ...$store.value, [changed]: changedValue }\n        })\n        if (!isAborted) return originSetKey(changed, changedValue)\n      }\n    }\n    $store.set = newValue => {\n      let isAborted\n      let abort = () => {\n        isAborted = true\n      }\n\n      runListeners({ abort, newValue })\n      if (!isAborted) return originSet(newValue)\n    }\n    return () => {\n      $store.set = originSet\n      $store.setKey = originSetKey\n    }\n  })\n\nexport let onNotify = ($store, listener) =>\n  on($store, listener, NOTIFY, runListeners => {\n    let originNotify = $store.notify\n    $store.notify = (oldValue, changed) => {\n      let isAborted\n      let abort = () => {\n        isAborted = true\n      }\n\n      runListeners({ abort, changed, oldValue })\n      if (!isAborted) return originNotify(oldValue, changed)\n    }\n    return () => {\n      $store.notify = originNotify\n    }\n  })\n\nexport let STORE_UNMOUNT_DELAY = 1000\n\nexport let onMount = ($store, initialize) => {\n  let listener = payload => {\n    let destroy = initialize(payload)\n    if (destroy) $store.events[UNMOUNT].push(destroy)\n  }\n  return on($store, listener, MOUNT, runListeners => {\n    let originListen = $store.listen\n    $store.listen = (...args) => {\n      if (!$store.lc && !$store.active) {\n        $store.active = true\n        runListeners()\n      }\n      return originListen(...args)\n    }\n\n    let originOff = $store.off\n    $store.events[UNMOUNT] = []\n    $store.off = () => {\n      originOff()\n      setTimeout(() => {\n        if ($store.active && !$store.lc) {\n          $store.active = false\n          for (let destroy of $store.events[UNMOUNT]) destroy()\n          $store.events[UNMOUNT] = []\n        }\n      }, STORE_UNMOUNT_DELAY)\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      let originClean = $store[clean]\n      $store[clean] = () => {\n        for (let destroy of $store.events[UNMOUNT]) destroy()\n        $store.events[UNMOUNT] = []\n        $store.active = false\n        originClean()\n      }\n    }\n\n    return () => {\n      $store.listen = originListen\n      $store.off = originOff\n    }\n  })\n}\n", "import { atom, onMount } from 'nanostores';\n\nconst isServer = typeof window === \"undefined\";\nconst useAuthQuery = (initializedAtom, path, $fetch, options) => {\n  const value = atom({\n    data: null,\n    error: null,\n    isPending: true,\n    isRefetching: false,\n    refetch: () => {\n      return fn();\n    }\n  });\n  const fn = () => {\n    const opts = typeof options === \"function\" ? options({\n      data: value.get().data,\n      error: value.get().error,\n      isPending: value.get().isPending\n    }) : options;\n    return $fetch(path, {\n      ...opts,\n      async onSuccess(context) {\n        value.set({\n          data: context.data,\n          error: null,\n          isPending: false,\n          isRefetching: false,\n          refetch: value.value.refetch\n        });\n        await opts?.onSuccess?.(context);\n      },\n      async onError(context) {\n        const { request } = context;\n        const retryAttempts = typeof request.retry === \"number\" ? request.retry : request.retry?.attempts;\n        const retryAttempt = request.retryAttempt || 0;\n        if (retryAttempts && retryAttempt < retryAttempts) return;\n        value.set({\n          error: context.error,\n          data: null,\n          isPending: false,\n          isRefetching: false,\n          refetch: value.value.refetch\n        });\n        await opts?.onError?.(context);\n      },\n      async onRequest(context) {\n        const currentValue = value.get();\n        value.set({\n          isPending: currentValue.data === null,\n          data: currentValue.data,\n          error: null,\n          isRefetching: true,\n          refetch: value.value.refetch\n        });\n        await opts?.onRequest?.(context);\n      }\n    });\n  };\n  initializedAtom = Array.isArray(initializedAtom) ? initializedAtom : [initializedAtom];\n  let isMounted = false;\n  for (const initAtom of initializedAtom) {\n    initAtom.subscribe(() => {\n      if (isServer) {\n        return;\n      }\n      if (isMounted) {\n        fn();\n      } else {\n        onMount(value, () => {\n          setTimeout(() => {\n            fn();\n          }, 0);\n          isMounted = true;\n          return () => {\n            value.off();\n            initAtom.off();\n          };\n        });\n      }\n    });\n  }\n  return value;\n};\n\nexport { useAuthQuery as u };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAO,IAAM,mBAAN,cAA+B,MAAM;EAC3C,YACQ,QACA,YACA,OACN;AACD,UAAM,cAAc,OAAO,SAAS,GAAG;MACtC,OAAO;IACR,CAAC;AANM,SAAA,SAAA;AACA,SAAA,aAAA;AACA,SAAA,QAAA;EAKR;AACD;AC2HO,IAAM,oBAAoB,OAChC,KACA,YACI;AAxIL,MAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AAyIC,MAAI,OAAO,WAAW,CAAC;AACvB,QAAM,QAMF;IACH,WAAW,CAAC,WAAA,OAAA,SAAA,QAAS,SAAS;IAC9B,YAAY,CAAC,WAAA,OAAA,SAAA,QAAS,UAAU;IAChC,WAAW,CAAC,WAAA,OAAA,SAAA,QAAS,SAAS;IAC9B,SAAS,CAAC,WAAA,OAAA,SAAA,QAAS,OAAO;IAC1B,SAAS,CAAC,WAAA,OAAA,SAAA,QAAS,OAAO;EAC3B;AACA,MAAI,CAAC,WAAW,EAAC,WAAA,OAAA,SAAA,QAAS,UAAS;AAClC,WAAO;MACN;MACA,SAAS;MACT;IACD;EACD;AACA,aAAW,WAAU,WAAA,OAAA,SAAA,QAAS,YAAW,CAAC,GAAG;AAC5C,QAAI,OAAO,MAAM;AAChB,YAAM,YAAY,QAAM,KAAA,OAAO,SAAP,OAAA,SAAA,GAAA,KAAA,QAAc,IAAI,SAAS,GAAG,OAAA;AACtD,aAAO,UAAU,WAAW;AAC5B,YAAM,UAAU;IACjB;AACA,UAAM,UAAU,MAAK,KAAA,OAAO,UAAP,OAAA,SAAA,GAAc,SAAS;AAC5C,UAAM,WAAW,MAAK,KAAA,OAAO,UAAP,OAAA,SAAA,GAAc,UAAU;AAC9C,UAAM,UAAU,MAAK,KAAA,OAAO,UAAP,OAAA,SAAA,GAAc,SAAS;AAC5C,UAAM,QAAQ,MAAK,KAAA,OAAO,UAAP,OAAA,SAAA,GAAc,OAAO;AACxC,UAAM,QAAQ,MAAK,KAAA,OAAO,UAAP,OAAA,SAAA,GAAc,OAAO;EACzC;AAEA,SAAO;IACN;IACA,SAAS;IACT;EACD;AACD;ACnJA,IAAM,sBAAN,MAAmD;EAClD,YAAoB,SAAsB;AAAtB,SAAA,UAAA;EAAuB;EAE3C,mBACC,SACA,UACmB;AACnB,QAAI,KAAK,QAAQ,aAAa;AAC7B,aAAO,QAAQ;QACd,UAAU,KAAK,QAAQ,YAAY,KAAK,QAAQ,YAAY,QAAQ;MACrE;IACD;AACA,WAAO,QAAQ,QAAQ,UAAU,KAAK,QAAQ,QAAQ;EACvD;EAEA,WAAmB;AAClB,WAAO,KAAK,QAAQ;EACrB;AACD;AAEA,IAAM,2BAAN,MAAwD;EACvD,YAAoB,SAA2B;AAA3B,SAAA,UAAA;EAA4B;EAEhD,mBACC,SACA,UACmB;AACnB,QAAI,KAAK,QAAQ,aAAa;AAC7B,aAAO,QAAQ;QACd,UAAU,KAAK,QAAQ,YAAY,KAAK,QAAQ,YAAY,QAAQ;MACrE;IACD;AACA,WAAO,QAAQ,QAAQ,UAAU,KAAK,QAAQ,QAAQ;EACvD;EAEA,SAAS,SAAyB;AACjC,UAAM,QAAQ,KAAK;MAClB,KAAK,QAAQ;MACb,KAAK,QAAQ,YAAY,KAAK;IAC/B;AACA,WAAO;EACR;AACD;AAEO,SAAS,oBAAoB,SAAsC;AACzE,MAAI,OAAO,YAAY,UAAU;AAChC,WAAO,IAAI,oBAAoB;MAC9B,MAAM;MACN,UAAU;MACV,OAAO;IACR,CAAC;EACF;AAEA,UAAQ,QAAQ,MAAM;IACrB,KAAK;AACJ,aAAO,IAAI,oBAAoB,OAAO;IACvC,KAAK;AACJ,aAAO,IAAI,yBAAyB,OAAO;IAC5C;AACC,YAAM,IAAI,MAAM,wBAAwB;EAC1C;AACD;AC5CO,IAAM,gBAAgB,OAAO,YAAgC;AACnE,QAAM,UAAkC,CAAC;AACzC,QAAM,WAAW,OAChB,UAGK,OAAO,UAAU,aAAa,MAAM,MAAM,IAAI;AACpD,MAAI,WAAA,OAAA,SAAA,QAAS,MAAM;AAClB,QAAI,QAAQ,KAAK,SAAS,UAAU;AACnC,YAAM,QAAQ,MAAM,SAAS,QAAQ,KAAK,KAAK;AAC/C,UAAI,CAAC,OAAO;AACX,eAAO;MACR;AACA,cAAQ,eAAe,IAAI,UAAU,KAAK;IAC3C,WAAW,QAAQ,KAAK,SAAS,SAAS;AACzC,YAAM,WAAW,SAAS,QAAQ,KAAK,QAAQ;AAC/C,YAAM,WAAW,SAAS,QAAQ,KAAK,QAAQ;AAC/C,UAAI,CAAC,YAAY,CAAC,UAAU;AAC3B,eAAO;MACR;AACA,cAAQ,eAAe,IAAI,SAAS,KAAK,GAAG,QAAQ,IAAI,QAAQ,EAAE,CAAC;IACpE,WAAW,QAAQ,KAAK,SAAS,UAAU;AAC1C,YAAM,QAAQ,SAAS,QAAQ,KAAK,KAAK;AACzC,UAAI,CAAC,OAAO;AACX,eAAO;MACR;AACA,cAAQ,eAAe,IAAI,GAAG,SAAS,QAAQ,KAAK,MAAM,CAAC,IAAI,KAAK;IACrE;EACD;AACA,SAAO;AACR;ACvEA,IAAM,UAAU;AAGT,SAAS,mBAAmB,SAAiC;AACnE,QAAM,eAAe,QAAQ,QAAQ,IAAI,cAAc;AACvD,QAAM,YAAY,oBAAI,IAAI;IACzB;IACA;IACA;IACA;EACD,CAAC;AACD,MAAI,CAAC,cAAc;AAClB,WAAO;EACR;AACA,QAAM,cAAc,aAAa,MAAM,GAAG,EAAE,MAAM,KAAK;AACvD,MAAI,QAAQ,KAAK,WAAW,GAAG;AAC9B,WAAO;EACR;AACA,MAAI,UAAU,IAAI,WAAW,KAAK,YAAY,WAAW,OAAO,GAAG;AAClE,WAAO;EACR;AACA,SAAO;AACR;AAEO,SAAS,eAAe,OAAY;AAC1C,MAAI;AACH,SAAK,MAAM,KAAK;AAChB,WAAO;EACR,SAAS,OAAO;AACf,WAAO;EACR;AACD;AAGO,SAAS,mBAAmB,OAAY;AAC9C,MAAI,UAAU,QAAW;AACxB,WAAO;EACR;AACA,QAAM,IAAI,OAAO;AACjB,MAAI,MAAM,YAAY,MAAM,YAAY,MAAM,aAAa,MAAM,MAAM;AACtE,WAAO;EACR;AACA,MAAI,MAAM,UAAU;AACnB,WAAO;EACR;AACA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO;EACR;AACA,MAAI,MAAM,QAAQ;AACjB,WAAO;EACR;AACA,SACE,MAAM,eAAe,MAAM,YAAY,SAAS,YACjD,OAAO,MAAM,WAAW;AAE1B;AAEO,SAAS,UAAU,MAAc;AACvC,MAAI;AACH,WAAO,KAAK,MAAM,IAAI;EACvB,SAAS,OAAO;AACf,WAAO;EACR;AACD;AAEO,SAAS,WAAW,OAAgC;AAC1D,SAAO,OAAO,UAAU;AACzB;AAEO,SAAS,SAAS,SAAyC;AACjE,MAAI,WAAA,OAAA,SAAA,QAAS,iBAAiB;AAC7B,WAAO,QAAQ;EAChB;AACA,MAAI,OAAO,eAAe,eAAe,WAAW,WAAW,KAAK,GAAG;AACtE,WAAO,WAAW;EACnB;AACA,MAAI,OAAO,WAAW,eAAe,WAAW,OAAO,KAAK,GAAG;AAC9D,WAAO,OAAO;EACf;AACA,QAAM,IAAI,MAAM,+BAA+B;AAChD;AAkBA,eAAsB,WAAW,MAA0B;AAC1D,QAAM,UAAU,IAAI,QAAQ,QAAA,OAAA,SAAA,KAAM,OAAO;AACzC,QAAM,aAAa,MAAM,cAAc,IAAI;AAC3C,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,cAAc,CAAC,CAAC,GAAG;AAC5D,YAAQ,IAAI,KAAK,KAAK;EACvB;AACA,MAAI,CAAC,QAAQ,IAAI,cAAc,GAAG;AACjC,UAAM,IAAI,kBAAkB,QAAA,OAAA,SAAA,KAAM,IAAI;AACtC,QAAI,GAAG;AACN,cAAQ,IAAI,gBAAgB,CAAC;IAC9B;EACD;AAEA,SAAO;AACR;AAqEO,SAAS,kBAAkB,MAAW;AAC5C,MAAI,mBAAmB,IAAI,GAAG;AAC7B,WAAO;EACR;AAEA,SAAO;AACR;AAEO,SAAS,QAAQ,SAA6B;AACpD,MAAI,EAAC,WAAA,OAAA,SAAA,QAAS,OAAM;AACnB,WAAO;EACR;AACA,QAAM,UAAU,IAAI,QAAQ,WAAA,OAAA,SAAA,QAAS,OAAO;AAC5C,MAAI,mBAAmB,QAAQ,IAAI,KAAK,CAAC,QAAQ,IAAI,cAAc,GAAG;AACrE,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,WAAA,OAAA,SAAA,QAAS,IAAI,GAAG;AACzD,UAAI,iBAAiB,MAAM;AAC1B,gBAAQ,KAAK,GAAG,IAAI,MAAM,YAAY;MACvC;IACD;AACA,WAAO,KAAK,UAAU,QAAQ,IAAI;EACnC;AAEA,SAAO,QAAQ;AAChB;AAEO,SAAS,UAAU,KAAa,SAA6B;AAnNpE,MAAA;AAoNC,MAAI,WAAA,OAAA,SAAA,QAAS,QAAQ;AACpB,WAAO,QAAQ,OAAO,YAAY;EACnC;AACA,MAAI,IAAI,WAAW,GAAG,GAAG;AACxB,UAAM,WAAU,KAAA,IAAI,MAAM,GAAG,EAAE,CAAC,MAAhB,OAAA,SAAA,GAAmB,MAAM,GAAA,EAAK,CAAA;AAC9C,QAAI,CAAC,QAAQ,SAAS,OAAO,GAAG;AAC/B,cAAO,WAAA,OAAA,SAAA,QAAS,QAAO,SAAS;IACjC;AACA,WAAO,QAAQ,YAAY;EAC5B;AACA,UAAO,WAAA,OAAA,SAAA,QAAS,QAAO,SAAS;AACjC;AAEO,SAAS,WACf,SACA,YACC;AACD,MAAI;AACJ,MAAI,EAAC,WAAA,OAAA,SAAA,QAAS,YAAU,WAAA,OAAA,SAAA,QAAS,UAAS;AACzC,mBAAe,WAAW,MAAM,cAAA,OAAA,SAAA,WAAY,MAAA,GAAS,WAAA,OAAA,SAAA,QAAS,OAAO;EACtE;AACA,SAAO;IACN;IACA,cAAc,MAAM;AACnB,UAAI,cAAc;AACjB,qBAAa,YAAY;MAC1B;IACD;EACD;AACD;AASO,IAAM,kBAAN,MAAM,yBAAwB,MAAM;EAG1C,YAAY,QAA+C,SAAkB;AAE5E,UAAM,WAAW,KAAK,UAAU,QAAQ,MAAM,CAAC,CAAC;AAChD,SAAK,SAAS;AAGd,WAAO,eAAe,MAAM,iBAAgB,SAAS;EACtD;AACD;AAEA,eAAsB,oBACrB,QACA,OACiD;AACjD,MAAI,SAAS,MAAM,OAAO,WAAW,EAAE,SAAS,KAAK;AAErD,MAAI,OAAO,QAAQ;AAClB,UAAM,IAAI,gBAAgB,OAAO,MAAM;EACxC;AACA,SAAO,OAAO;AACf;ACpQO,IAAM,UAAU,CAAC,OAAO,QAAQ,OAAO,SAAS,QAAQ;ACPxD,IAAM,oBAAoB,CAAC,YAChC;EACA,IAAI;EACJ,MAAM;EACN,SAAS;EACT,MAAM,KAAK,KAAK,SAAS;AAX3B,QAAA,IAAA,IAAA,IAAA;AAYG,UAAM,WACL,MAAA,KAAA,OAAO,YAAP,OAAA,SAAA,GAAgB;MAAK,CAAC,WAAQ;AAblC,YAAAA;AAcK,iBAAAA,MAAA,OAAO,WAAP,OAAA,SAAAA,IAAe,UACZ,IAAI,WAAW,OAAO,OAAO,OAAO,WAAW,EAAE,KAClD,IAAI,WAAW,OAAO,OAAO,OAAO,UAAU,EAAE,IAC/C;MAAA;IAAA,MAJJ,OAAA,SAAA,GAKG,WAAU,OAAO;AACrB,QAAI,QAAQ;AACX,UAAI,SAAS;AACb,WAAI,KAAA,OAAO,WAAP,OAAA,SAAA,GAAe,QAAQ;AAC1B,YAAI,OAAO,WAAW,OAAO,OAAO,MAAM,GAAG;AAC5C,mBAAS,OAAO,QAAQ,OAAO,OAAO,QAAQ,EAAE;AAChD,cAAI,OAAO,OAAO,SAAS;AAC1B,kBAAM,IAAI,QAAQ,OAAO,OAAO,QAAQ,OAAO,OAAO,OAAO;UAC9D;QACD;MACD;AACA,WAAI,KAAA,OAAO,WAAP,OAAA,SAAA,GAAe,SAAS;AAC3B,YAAI,OAAO,WAAW,OAAO,OAAO,OAAO,GAAG;AAC7C,mBAAS,OAAO,QAAQ,OAAO,OAAO,SAAS,EAAE;QAClD;MACD;AACA,YAAM,YAAY,OAAO,OAAO,MAAM;AACtC,UAAI,WAAW;AACd,YAAI,OAAO,cAAA,eAAA,CAAA,GACP,OAAA,GADO;UAEV,QAAQ,UAAU;UAClB,QAAQ,UAAU;QACnB,CAAA;AACA,YAAI,EAAC,WAAA,OAAA,SAAA,QAAS,oBAAmB;AAChC,iBAAO,cAAA,eAAA,CAAA,GACH,IAAA,GADG;YAEN,MAAM,UAAU,QACb,MAAM,oBAAoB,UAAU,OAAO,WAAA,OAAA,SAAA,QAAS,IAAI,IACxD,WAAA,OAAA,SAAA,QAAS;YACZ,QAAQ,UAAU,SACf,MAAM,oBAAoB,UAAU,QAAQ,WAAA,OAAA,SAAA,QAAS,MAAM,IAC3D,WAAA,OAAA,SAAA,QAAS;YACZ,OAAO,UAAU,QACd,MAAM,oBAAoB,UAAU,OAAO,WAAA,OAAA,SAAA,QAAS,KAAK,IACzD,WAAA,OAAA,SAAA,QAAS;UACb,CAAA;QACD;AACA,eAAO;UACN;UACA,SAAS;QACV;MACD;IACD;AACA,WAAO;MACN;MACA;IACD;EACD;AACD;AAEM,IAAM,cAAc,CAC1B,WACI;AACJ,iBAAe,OAAO,KAAa,SAA6B;AAC/D,UAAM,OAAO,cAAA,eAAA,eAAA,CAAA,GACT,MAAA,GACA,OAAA,GAFS;MAGZ,SAAS,CAAC,IAAI,UAAA,OAAA,SAAA,OAAQ,YAAW,CAAC,GAAI,kBAAkB,UAAU,CAAC,CAAC,CAAC;IACtE,CAAA;AAEA,QAAI,UAAA,OAAA,SAAA,OAAQ,eAAe;AAC1B,UAAI;AACH,eAAO,MAAM,YAAY,KAAK,IAAI;MACnC,SAAS,OAAO;AACf,eAAO;UACN,MAAM;UACN,OAAO;YACN,QAAQ;YACR,YAAY;YACZ,SACC;YACD;UACD;QACD;MACD;IACD;AACA,WAAO,MAAM,YAAY,KAAK,IAAI;EACnC;AACA,SAAO;AACR;AC3FO,SAASC,QAAO,KAAa,QAA4B;AAC/D,MAAI,EAAE,SAAS,QAAQ,MAAM,IAAI,UAAU;IAC1C,OAAO,CAAC;IACR,QAAQ,CAAC;IACT,SAAS;EACV;AACA,MAAI,WAAW,IAAI,WAAW,MAAM,IACjC,IAAI,MAAM,GAAG,EAAE,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,IACnC,WAAW;AAKd,MAAI,IAAI,WAAW,GAAG,GAAG;AACxB,UAAM,IAAI,IAAI,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AACnD,QAAI,QAAQ,SAAS,CAAC,GAAG;AACxB,YAAM,IAAI,QAAQ,IAAI,CAAC,KAAK,GAAG;IAChC;EACD;AAEA,MAAI,CAAC,SAAS,SAAS,GAAG,EAAG,aAAY;AACzC,MAAI,CAAC,MAAM,QAAQ,IAAI,IAAI,QAAQ,UAAU,EAAE,EAAE,MAAM,GAAG;AAC1D,QAAM,cAAc,IAAI,gBAAgB,QAAQ;AAChD,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,SAAS,CAAC,CAAC,GAAG;AACvD,QAAI,SAAS,KAAM;AACnB,gBAAY,IAAI,KAAK,OAAO,KAAK,CAAC;EACnC;AACA,MAAI,QAAQ;AACX,QAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,YAAM,aAAa,KAAK,MAAM,GAAG,EAAE,OAAO,CAAC,MAAM,EAAE,WAAW,GAAG,CAAC;AAClE,iBAAW,CAAC,OAAO,GAAG,KAAK,WAAW,QAAQ,GAAG;AAChD,cAAM,QAAQ,OAAO,KAAK;AAC1B,eAAO,KAAK,QAAQ,KAAK,KAAK;MAC/B;IACD,OAAO;AACN,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AAClD,eAAO,KAAK,QAAQ,IAAI,GAAG,IAAI,OAAO,KAAK,CAAC;MAC7C;IACD;EACD;AAEA,SAAO,KAAK,MAAM,GAAG,EAAE,IAAI,kBAAkB,EAAE,KAAK,GAAG;AACvD,MAAI,KAAK,WAAW,GAAG,EAAG,QAAO,KAAK,MAAM,CAAC;AAC7C,MAAI,mBAAmB,YAAY,SAAS;AAC5C,qBACC,iBAAiB,SAAS,IAAI,IAAI,gBAAgB,GAAG,QAAQ,OAAO,KAAK,IAAI;AAC9E,MAAI,CAAC,SAAS,WAAW,MAAM,GAAG;AACjC,WAAO,GAAG,QAAQ,GAAG,IAAI,GAAG,gBAAgB;EAC7C;AACA,QAAM,OAAO,IAAI,IAAI,GAAG,IAAI,GAAG,gBAAgB,IAAI,QAAQ;AAC3D,SAAO;AACR;ACvCO,IAAM,cAAc,OAO1B,KACA,YAOI;AAjCL,MAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AAkCC,QAAM;IACL;IACA,KAAK;IACL,SAAS;EACV,IAAI,MAAM,kBAAkB,KAAK,OAAO;AACxC,QAAM,QAAQ,SAAS,IAAI;AAC3B,QAAM,aAAa,IAAI,gBAAgB;AACvC,QAAM,UAAS,KAAA,KAAK,WAAL,OAAA,KAAe,WAAW;AACzC,QAAM,OAAOA,QAAO,OAAO,IAAI;AAC/B,QAAM,OAAO,QAAQ,IAAI;AACzB,QAAM,UAAU,MAAM,WAAW,IAAI;AACrC,QAAM,SAAS,UAAU,OAAO,IAAI;AACpC,MAAI,UAAU,cAAA,eAAA,CAAA,GACV,IAAA,GADU;IAEb,KAAK;IACL;IACA;IACA;IACA;EACD,CAAA;AAIA,aAAW,aAAa,MAAM,WAAW;AACxC,QAAI,WAAW;AACd,YAAM,MAAM,MAAM,UAAU,OAAO;AACnC,UAAI,eAAe,QAAQ;AAC1B,kBAAU;MACX;IACD;EACD;AACA,MACE,YAAa,WACb,OAAQ,QAAgB,WAAW,cACpC,SAAO,KAAA,WAAA,OAAA,SAAA,QAAS,SAAT,OAAA,SAAA,GAAe,UAAS,YAC9B;AACD,QAAI,EAAE,YAAY,UAAU;AAC3B,cAAQ,SAAS;IAClB;EACD;AAEA,QAAM,EAAE,cAAAC,cAAa,IAAI,WAAW,MAAM,UAAU;AACpD,MAAI,WAAW,MAAM,MAAM,QAAQ,KAAK,OAAO;AAC/CA,gBAAa;AAEb,QAAM,kBAAkB;IACvB;IACA,SAAS;EACV;AAEA,aAAW,cAAc,MAAM,YAAY;AAC1C,QAAI,YAAY;AACf,YAAM,IAAI,MAAM,WAAW,cAAA,eAAA,CAAA,GACvB,eAAA,GADuB;QAE1B,YAAU,KAAA,WAAA,OAAA,SAAA,QAAS,gBAAT,OAAA,SAAA,GAAsB,iBAC7B,SAAS,MAAM,IACf;MACJ,CAAA,CAAC;AACD,UAAI,aAAa,UAAU;AAC1B,mBAAW;MACZ,WAAW,aAAa,QAAQ;AAC/B,mBAAW,EAAE;MACd;IACD;EACD;AAKA,MAAI,SAAS,IAAI;AAChB,UAAM,UAAU,QAAQ,WAAW;AACnC,QAAI,CAAC,SAAS;AACb,aAAO;QACN,MAAM;QACN,OAAO;MACR;IACD;AACA,UAAM,eAAe,mBAAmB,QAAQ;AAChD,UAAM,iBAAiB;MACtB,MAAM;MACN;MACA,SAAS;IACV;AACA,QAAI,iBAAiB,UAAU,iBAAiB,QAAQ;AACvD,YAAM,OAAO,MAAM,SAAS,KAAK;AACjC,YAAMC,WAAS,KAAA,QAAQ,eAAR,OAAA,KAAsB;AACrC,YAAM,OAAO,MAAMA,QAAO,IAAI;AAC9B,qBAAe,OAAO;IACvB,OAAO;AACN,qBAAe,OAAO,MAAM,SAAS,YAAY,EAAE;IACpD;AAKA,QAAI,WAAA,OAAA,SAAA,QAAS,QAAQ;AACpB,UAAI,QAAQ,UAAU,CAAC,QAAQ,mBAAmB;AACjD,uBAAe,OAAO,MAAM;UAC3B,QAAQ;UACR,eAAe;QAChB;MACD;IACD;AAEA,eAAW,aAAa,MAAM,WAAW;AACxC,UAAI,WAAW;AACd,cAAM,UAAU,cAAA,eAAA,CAAA,GACZ,cAAA,GADY;UAEf,YAAU,KAAA,WAAA,OAAA,SAAA,QAAS,gBAAT,OAAA,SAAA,GAAsB,iBAC7B,SAAS,MAAM,IACf;QACJ,CAAA,CAAC;MACF;IACD;AAEA,QAAI,WAAA,OAAA,SAAA,QAAS,OAAO;AACnB,aAAO,eAAe;IACvB;AAEA,WAAO;MACN,MAAM,eAAe;MACrB,OAAO;IACR;EACD;AACA,QAAM,UAAS,KAAA,WAAA,OAAA,SAAA,QAAS,eAAT,OAAA,KAAuB;AACtC,QAAM,eAAe,MAAM,SAAS,KAAK;AACzC,QAAM,iBAAiB,eAAe,YAAY;AAClD,QAAM,cAAc,iBAAiB,MAAM,OAAO,YAAY,IAAI;AAIlE,QAAM,eAAe;IACpB;IACA;IACA,SAAS;IACT,OAAO,cAAA,eAAA,CAAA,GACH,WAAA,GADG;MAEN,QAAQ,SAAS;MACjB,YAAY,SAAS;IACtB,CAAA;EACD;AACA,aAAW,WAAW,MAAM,SAAS;AACpC,QAAI,SAAS;AACZ,YAAM,QAAQ,cAAA,eAAA,CAAA,GACV,YAAA,GADU;QAEb,YAAU,KAAA,WAAA,OAAA,SAAA,QAAS,gBAAT,OAAA,SAAA,GAAsB,iBAC7B,SAAS,MAAM,IACf;MACJ,CAAA,CAAC;IACF;EACD;AAEA,MAAI,WAAA,OAAA,SAAA,QAAS,OAAO;AACnB,UAAM,gBAAgB,oBAAoB,QAAQ,KAAK;AACvD,UAAM,iBAAgB,KAAA,QAAQ,iBAAR,OAAA,KAAwB;AAC9C,QAAI,MAAM,cAAc,mBAAmB,eAAe,QAAQ,GAAG;AACpE,iBAAW,WAAW,MAAM,SAAS;AACpC,YAAI,SAAS;AACZ,gBAAM,QAAQ,eAAe;QAC9B;MACD;AACA,YAAM,QAAQ,cAAc,SAAS,aAAa;AAClD,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,KAAK,CAAC;AACzD,aAAO,MAAM,YAAY,KAAK,cAAA,eAAA,CAAA,GAC1B,OAAA,GAD0B;QAE7B,cAAc,gBAAgB;MAC/B,CAAA,CAAC;IACF;EACD;AAEA,MAAI,WAAA,OAAA,SAAA,QAAS,OAAO;AACnB,UAAM,IAAI;MACT,SAAS;MACT,SAAS;MACT,iBAAiB,cAAc;IAChC;EACD;AACA,SAAO;IACN,MAAM;IACN,OAAO,cAAA,eAAA,CAAA,GACH,WAAA,GADG;MAEN,QAAQ,SAAS;MACjB,YAAY,SAAS;IACtB,CAAA;EACD;AACD;;;AC3NA,IAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,IAAM,UAAU,CAAC,YAAY,WAAW,SAAS;AACjD,WAAW,MAAM,IAAI,SAAS;AAC9B,WAAW,YAAY,UAAU,WAAW;AAC5C,IAAM,MAAM,IAAI,MAAM,UAAU;AAAA,EAC9B,IAAI,GAAG,MAAM;AACX,UAAM,OAAO,QAAQ;AACrB,WAAO,KAAK,IAAI,KAAK,SAAS,IAAI;AAAA,EACpC;AAAA,EACA,IAAI,GAAG,MAAM;AACX,UAAM,OAAO,QAAQ;AACrB,WAAO,QAAQ,QAAQ,QAAQ;AAAA,EACjC;AAAA,EACA,IAAI,GAAG,MAAM,OAAO;AAClB,UAAM,OAAO,QAAQ,IAAI;AACzB,SAAK,IAAI,IAAI;AACb,WAAO;AAAA,EACT;AAAA,EACA,eAAe,GAAG,MAAM;AACtB,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,UAAM,OAAO,QAAQ,IAAI;AACzB,WAAO,KAAK,IAAI;AAChB,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,UAAM,OAAO,QAAQ,IAAI;AACzB,WAAO,OAAO,KAAK,IAAI;AAAA,EACzB;AACF,CAAC;AACD,SAAS,UAAU,KAAK;AACtB,SAAO,MAAM,QAAQ,UAAU;AACjC;AACA,IAAM,UAAU,OAAO,YAAY,eAAe,QAAQ,OAAO,iBAAwB;AAGzF,IAAM,SAAS,YAAY,UAAU,UAAU,IAAI,IAAI;;;ACrCvD,IAAM,kBAAN,cAA8B,MAAM;AAAA,EAClC,YAAY,SAAS,OAAO;AAC1B,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AACF;;;ACNO,IAAI,QAAQ,OAAO,OAAO;;;ACAjC,IAAI,gBAAgB,CAAC;AACrB,IAAI,UAAU;AACd,IAAM,2BAA2B;AAC1B,IAAI,QAAQ;AAEZ,IAAI,OAAO,CAAC,iBAAiB;AAClC,MAAI,YAAY,CAAC;AACjB,MAAI,QAAQ;AAAA,IACV,MAAM;AACJ,UAAI,CAAC,MAAM,IAAI;AACb,cAAM,OAAO,MAAM;AAAA,QAAC,CAAC,EAAE;AAAA,MACzB;AACA,aAAO,MAAM;AAAA,IACf;AAAA,IACA,IAAI;AAAA,IACJ,OAAO,UAAU;AACf,YAAM,KAAK,UAAU,KAAK,QAAQ;AAElC,aAAO,MAAM;AACX,iBAAS,IAAI,UAAU,0BAA0B,IAAI,cAAc,UAAS;AAC1E,cAAI,cAAc,CAAC,MAAM,UAAU;AACjC,0BAAc,OAAO,GAAG,wBAAwB;AAAA,UAClD,OAAO;AACL,iBAAK;AAAA,UACP;AAAA,QACF;AAEA,YAAI,QAAQ,UAAU,QAAQ,QAAQ;AACtC,YAAI,CAAC,OAAO;AACV,oBAAU,OAAO,OAAO,CAAC;AACzB,cAAI,CAAC,EAAE,MAAM,GAAI,OAAM,IAAI;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,IACA,OAAO,UAAU,YAAY;AAC3B;AACA,UAAI,mBAAmB,CAAC,cAAc;AACtC,eAAS,YAAY,WAAW;AAC9B,sBAAc;AAAA,UACZ;AAAA,UACA,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,UAAI,kBAAkB;AACpB,aAAK,UAAU,GAAG,UAAU,cAAc,QAAQ,WAAW,0BAA0B;AACnF,wBAAc,OAAO;AAAA,YACnB,cAAc,UAAU,CAAC;AAAA,YACzB,cAAc,UAAU,CAAC;AAAA,YACzB,cAAc,UAAU,CAAC;AAAA,UAC3B;AAAA,QACJ;AACA,sBAAc,SAAS;AAAA,MACzB;AAAA,IACF;AAAA;AAAA;AAAA,IAGA,MAAM;AAAA,IAAC;AAAA,IACP,IAAI,UAAU;AACZ,UAAI,WAAW,MAAM;AACrB,UAAI,aAAa,UAAU;AACzB,cAAM,QAAQ;AACd,cAAM,OAAO,QAAQ;AAAA,MACvB;AAAA,IACF;AAAA,IACA,UAAU,UAAU;AAClB,UAAI,SAAS,MAAM,OAAO,QAAQ;AAClC,eAAS,MAAM,KAAK;AACpB,aAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,EACT;AAEA,MAAI,MAAuC;AACzC,UAAM,KAAK,IAAI,MAAM;AACnB,kBAAY,CAAC;AACb,YAAM,KAAK;AACX,YAAM,IAAI;AAAA,IACZ;AAAA,EACF;AAEA,SAAO;AACT;;;ACtFO,SAAS,WAAW,QAAQ,MAAM,UAAU;AACjD,MAAI,UAAU,IAAI,IAAI,IAAI,EAAE,IAAI,MAAS;AACzC,SAAO,OAAO,OAAO,CAAC,OAAO,UAAU,YAAY;AACjD,QAAI,QAAQ,IAAI,OAAO,GAAG;AACxB,eAAS,OAAO,UAAU,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AACH;;;ACDA,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,kBAAkB;AAEjB,IAAI,KAAK,CAAC,QAAQ,UAAU,UAAU,gBAAgB;AAC3D,SAAO,SAAS,OAAO,UAAU,CAAC;AAClC,MAAI,CAAC,OAAO,OAAO,WAAW,eAAe,GAAG;AAC9C,WAAO,OAAO,WAAW,eAAe,IAAI,YAAY,gBAAc;AAEpE,aAAO,OAAO,QAAQ,EAAE,YAAY,CAAC,OAAO,OAAO,EAAE,KAAK,GAAG,QAAQ;AAAA,QACnE,QAAQ,CAAC;AAAA,QACT,GAAG;AAAA,MACL,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO,OAAO,QAAQ,IAAI,OAAO,OAAO,QAAQ,KAAK,CAAC;AACtD,SAAO,OAAO,QAAQ,EAAE,KAAK,QAAQ;AACrC,SAAO,MAAM;AACX,QAAI,mBAAmB,OAAO,OAAO,QAAQ;AAC7C,QAAI,QAAQ,iBAAiB,QAAQ,QAAQ;AAC7C,qBAAiB,OAAO,OAAO,CAAC;AAChC,QAAI,CAAC,iBAAiB,QAAQ;AAC5B,aAAO,OAAO,OAAO,QAAQ;AAC7B,aAAO,OAAO,WAAW,eAAe,EAAE;AAC1C,aAAO,OAAO,OAAO,WAAW,eAAe;AAAA,IACjD;AAAA,EACF;AACF;AAiFO,IAAI,sBAAsB;AAE1B,IAAI,UAAU,CAAC,QAAQ,eAAe;AAC3C,MAAI,WAAW,aAAW;AACxB,QAAI,UAAU,WAAW,OAAO;AAChC,QAAI,QAAS,QAAO,OAAO,OAAO,EAAE,KAAK,OAAO;AAAA,EAClD;AACA,SAAO,GAAG,QAAQ,UAAU,OAAO,kBAAgB;AACjD,QAAI,eAAe,OAAO;AAC1B,WAAO,SAAS,IAAI,SAAS;AAC3B,UAAI,CAAC,OAAO,MAAM,CAAC,OAAO,QAAQ;AAChC,eAAO,SAAS;AAChB,qBAAa;AAAA,MACf;AACA,aAAO,aAAa,GAAG,IAAI;AAAA,IAC7B;AAEA,QAAI,YAAY,OAAO;AACvB,WAAO,OAAO,OAAO,IAAI,CAAC;AAC1B,WAAO,MAAM,MAAM;AACjB,gBAAU;AACV,iBAAW,MAAM;AACf,YAAI,OAAO,UAAU,CAAC,OAAO,IAAI;AAC/B,iBAAO,SAAS;AAChB,mBAAS,WAAW,OAAO,OAAO,OAAO,EAAG,SAAQ;AACpD,iBAAO,OAAO,OAAO,IAAI,CAAC;AAAA,QAC5B;AAAA,MACF,GAAG,mBAAmB;AAAA,IACxB;AAEA,QAAI,MAAuC;AACzC,UAAI,cAAc,OAAO,KAAK;AAC9B,aAAO,KAAK,IAAI,MAAM;AACpB,iBAAS,WAAW,OAAO,OAAO,OAAO,EAAG,SAAQ;AACpD,eAAO,OAAO,OAAO,IAAI,CAAC;AAC1B,eAAO,SAAS;AAChB,oBAAY;AAAA,MACd;AAAA,IACF;AAEA,WAAO,MAAM;AACX,aAAO,SAAS;AAChB,aAAO,MAAM;AAAA,IACf;AAAA,EACF,CAAC;AACH;;;AC7JA,IAAM,WAAW,OAAO,WAAW;AACnC,IAAM,eAAe,CAAC,iBAAiB,MAAM,QAAQ,YAAY;AAC/D,QAAM,QAAQ,KAAK;AAAA,IACjB,MAAM;AAAA,IACN,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,IACd,SAAS,MAAM;AACb,aAAO,GAAG;AAAA,IACZ;AAAA,EACF,CAAC;AACD,QAAM,KAAK,MAAM;AACf,UAAM,OAAO,OAAO,YAAY,aAAa,QAAQ;AAAA,MACnD,MAAM,MAAM,IAAI,EAAE;AAAA,MAClB,OAAO,MAAM,IAAI,EAAE;AAAA,MACnB,WAAW,MAAM,IAAI,EAAE;AAAA,IACzB,CAAC,IAAI;AACL,WAAO,OAAO,MAAM;AAAA,MAClB,GAAG;AAAA,MACH,MAAM,UAAU,SAAS;AACvB,cAAM,IAAI;AAAA,UACR,MAAM,QAAQ;AAAA,UACd,OAAO;AAAA,UACP,WAAW;AAAA,UACX,cAAc;AAAA,UACd,SAAS,MAAM,MAAM;AAAA,QACvB,CAAC;AACD,cAAM,MAAM,YAAY,OAAO;AAAA,MACjC;AAAA,MACA,MAAM,QAAQ,SAAS;AACrB,cAAM,EAAE,QAAQ,IAAI;AACpB,cAAM,gBAAgB,OAAO,QAAQ,UAAU,WAAW,QAAQ,QAAQ,QAAQ,OAAO;AACzF,cAAM,eAAe,QAAQ,gBAAgB;AAC7C,YAAI,iBAAiB,eAAe,cAAe;AACnD,cAAM,IAAI;AAAA,UACR,OAAO,QAAQ;AAAA,UACf,MAAM;AAAA,UACN,WAAW;AAAA,UACX,cAAc;AAAA,UACd,SAAS,MAAM,MAAM;AAAA,QACvB,CAAC;AACD,cAAM,MAAM,UAAU,OAAO;AAAA,MAC/B;AAAA,MACA,MAAM,UAAU,SAAS;AACvB,cAAM,eAAe,MAAM,IAAI;AAC/B,cAAM,IAAI;AAAA,UACR,WAAW,aAAa,SAAS;AAAA,UACjC,MAAM,aAAa;AAAA,UACnB,OAAO;AAAA,UACP,cAAc;AAAA,UACd,SAAS,MAAM,MAAM;AAAA,QACvB,CAAC;AACD,cAAM,MAAM,YAAY,OAAO;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AACA,oBAAkB,MAAM,QAAQ,eAAe,IAAI,kBAAkB,CAAC,eAAe;AACrF,MAAI,YAAY;AAChB,aAAW,YAAY,iBAAiB;AACtC,aAAS,UAAU,MAAM;AACvB,UAAI,UAAU;AACZ;AAAA,MACF;AACA,UAAI,WAAW;AACb,WAAG;AAAA,MACL,OAAO;AACL,gBAAQ,OAAO,MAAM;AACnB,qBAAW,MAAM;AACf,eAAG;AAAA,UACL,GAAG,CAAC;AACJ,sBAAY;AACZ,iBAAO,MAAM;AACX,kBAAM,IAAI;AACV,qBAAS,IAAI;AAAA,UACf;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;", "names": ["_a", "getURL", "clearTimeout", "parser"]}