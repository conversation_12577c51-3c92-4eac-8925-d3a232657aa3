import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { format } from "date-fns";
import { useBankTransactions } from "../api/useBankTransactions";
import BankTransfer from "./BankTransfer";
import { formatCurrency } from "@/utils/currencyHelpers";
import { History, ArrowDownToLine, ArrowUpFromLine, SendHorizontal } from "lucide-react";

interface Transaction {
    transaction_type: 'bank_deposit' | 'bank_withdrawal' | 'bank_transfer' | 'shop_purchase' | 'player_trade';
    cash: number;
    transactionFee: number;
    initiatorId: number | null;
    secondPartyId: number | null;
    createdAt: Date;
    cashBalance?: number;
    bankBalance?: number;
}

interface TransactionHistoryProps {
    historyLimit?: number;
}

const TransactionHistory = ({ historyLimit = 10 }: TransactionHistoryProps) => {
    const isMobile = useCheckMobileScreen();
    const { isLoading, error, data: rawData } = useBankTransactions();
    const { data: currentUser } = useFetchCurrentUser();

    // Limit the data to the specified number of transactions
    const data = rawData ? rawData.slice(0, historyLimit) : [];

    if (error) return <div>An error has occurred: {error.message}</div>;

    const formatDate = (date: Date) => {
        // const parseDate = parseISO(date);
        let formattedDate: string;
        if (isMobile) {
            formattedDate = format(date, "dd/MM kk:mm");
        } else {
            formattedDate = format(date, "dd/MM/y kk:mm");
        }
        return formattedDate;
    };

    const formatTransaction = (transaction: Transaction) => {
        if (transaction.transaction_type === "bank_withdrawal") {
            return "Withdrawal";
        } else if (transaction.transaction_type === "bank_deposit") {
            return "Deposit";
        } else if (transaction.transaction_type === "bank_transfer") {
            return "Transfer";
        }
        return transaction.transaction_type;
    };

    const getTransactionIcon = (transaction: Transaction) => {
        if (transaction.transaction_type === "bank_withdrawal") {
            return ArrowUpFromLine;
        } else if (transaction.transaction_type === "bank_deposit") {
            return ArrowDownToLine;
        } else if (transaction.transaction_type === "bank_transfer") {
            return SendHorizontal;
        }
        return History;
    };

    const formatTransactionAmount = (transaction: Transaction) => {
        const isReceiving =
            transaction.transaction_type === "bank_transfer" && transaction.secondPartyId === currentUser?.id;
        const isWithdrawing = transaction.transaction_type === "bank_withdrawal";
        const isDepositing = transaction.transaction_type === "bank_deposit";
        const isTransferring =
            transaction.transaction_type === "bank_transfer" && transaction.secondPartyId !== currentUser?.id;

        let amountClass = "";
        let prefix = "";

        if (isReceiving || isWithdrawing) {
            amountClass = "text-success";
            prefix = "+";
        } else if (isTransferring || isDepositing) {
            amountClass = "text-error";
            prefix = "-";
        }

        return (
            <span className={`font-medium ${amountClass}`}>
                {prefix}
                {formatCurrency(transaction.cash)}
            </span>
        );
    };

    return (
        <div className="card bg-[#1a2f4a] border border-[#2a4a7c] shadow-xl">
            <div className="card-body">
                <h2 className="card-title mb-4 text-white">
                    <History className="w-6 h-6 text-blue-400" />
                    Transaction History
                    <span className="text-sm text-gray-400">Last {historyLimit}</span>
                </h2>

                {isLoading ? (
                    <div className="flex justify-center py-8">
                        <span className="loading loading-spinner loading-lg text-blue-400"></span>
                    </div>
                ) : data.length === 0 ? (
                    <div className="text-center py-8 text-gray-400">
                        <History className="w-12 h-12 mx-auto mb-2 opacity-50" />
                        <p>No transactions yet</p>
                    </div>
                ) : (
                    <div className="overflow-x-auto">
                        <table className="table table-sm">
                            <thead>
                                <tr className="text-gray-400 border-b border-[#2a4a7c]">
                                    <th className="w-8 bg-transparent"></th>
                                    <th className="bg-transparent">Date</th>
                                    <th className="bg-transparent">Type</th>
                                    <th className="bg-transparent">Details</th>
                                    <th className="text-right bg-transparent">Amount</th>
                                    <th className="text-right hidden sm:table-cell bg-transparent">Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                {data.map((transaction, i) => {
                                    const Icon = getTransactionIcon(transaction);
                                    const isTransfer = transaction.transaction_type === "bank_transfer";
                                    const isReceiving = isTransfer && transaction.secondPartyId === currentUser?.id;

                                    return (
                                        <tr
                                            key={i}
                                            className="hover:bg-[#0d1b2a] border-b border-[#2a4a7c] text-gray-300"
                                        >
                                            <td className="bg-transparent">
                                                <div
                                                    className={`p-1.5 rounded-lg inline-flex ${
                                                        transaction.transaction_type === "bank_deposit"
                                                            ? "bg-green-900/30 text-green-400"
                                                            : transaction.transaction_type === "bank_withdrawal"
                                                              ? "bg-yellow-900/30 text-yellow-400"
                                                              : isReceiving
                                                                ? "bg-green-900/30 text-green-400"
                                                                : "bg-blue-900/30 text-blue-400"
                                                    }`}
                                                >
                                                    <Icon className="w-4 h-4" />
                                                </div>
                                            </td>
                                            <td className="font-medium bg-transparent">
                                                {formatDate(transaction.createdAt)}
                                            </td>
                                            <td className="bg-transparent">
                                                <span
                                                    className={`badge badge-sm ${
                                                        transaction.transaction_type === "bank_deposit"
                                                            ? "bg-green-900/30 text-green-400 border-green-800"
                                                            : transaction.transaction_type === "bank_withdrawal"
                                                              ? "bg-yellow-900/30 text-yellow-400 border-yellow-800"
                                                              : "bg-blue-900/30 text-blue-400 border-blue-800"
                                                    }`}
                                                >
                                                    {formatTransaction(transaction)}
                                                </span>
                                            </td>
                                            <td className="bg-transparent">
                                                {isTransfer ? (
                                                    <BankTransfer
                                                        transaction={transaction}
                                                        currentUser={currentUser?.id?.toString() || ""}
                                                    />
                                                ) : (
                                                    <span className="text-gray-400">-</span>
                                                )}
                                            </td>
                                            <td className="text-right font-mono bg-transparent">
                                                {formatTransactionAmount(transaction)}
                                            </td>
                                            <td className="text-right font-mono hidden sm:table-cell text-gray-400 bg-transparent">
                                                {transaction.bankBalance ? formatCurrency(transaction.bankBalance) : '-'}
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
        </div>
    );
};

export default TransactionHistory;
