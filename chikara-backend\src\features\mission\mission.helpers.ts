import gameConfig from "../../config/gameConfig.js";
import * as FocusService from "../../core/focus.service.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as NotificationService from "../../core/notification.service.js";
import * as UserService from "../../core/user.service.js";
import { missionTiers } from "../../data/missions.js";
import * as MissionRepository from "../../repositories/mission.repository.js";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import * as TalentHelper from "../talents/talents.helpers.js";
import * as ShrineHelper from "../shrine/shrine.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { NotificationTypes } from "../../types/notification.js";
import { LogErrorStack } from "../../utils/log.js";
import { emitMissionCompleted } from "../../core/events/index.js";
import ms from "ms";

const HOUR_IN_MS = ms("1h");
const itemAmounts = [1, 2, 4];

function getRandomReward(min: number, max: number) {
    return Math.floor(Math.random() * (max - min + 1) + min);
}

const getRandomItemReward = (itemNames: string[]) => {
    return itemNames[Math.floor(Math.random() * itemNames.length)];
};

const getRandomRewardType = () => {
    // Randomly shuffle an array of reward types
    const rewards = ["exp", "cash", "item"];
    return rewards.sort(() => 0.5 - Math.random());
};

export const GenerateMissionsForTier = async (
    tierLevel: number,
    date: Date,
    currentTier: { name: string; description: string }[]
) => {
    const rewardTypes = getRandomRewardType();
    const missions = [];

    for (let i = 0; i < 3; i++) {
        const missionType = rewardTypes[i];
        const { name, description } = currentTier[i];

        const mission: {
            missionName: string;
            description: string;
            tier: number;
            duration: number;
            levelReq: number;
            hoursReq: number;
            missionDate: Date;
            rewardType: string;
            minCashReward?: number;
            maxCashReward?: number;
            minExpReward?: number;
            maxExpReward?: number;
            itemRewardId?: number;
            itemRewardQuantity?: number;
        } = {
            missionName: name,
            description: description,
            tier: tierLevel,
            duration: gameConfig.MISSION_DURATIONS_HOURS[i] * HOUR_IN_MS,
            levelReq: missionTiers[tierLevel].level,
            hoursReq: missionTiers[tierLevel].reqHours,
            missionDate: date,
            rewardType: missionType,
        };

        switch (missionType) {
            case "cash": {
                mission.minCashReward = missionTiers[tierLevel].cashAmounts[i].minCash;
                mission.maxCashReward = missionTiers[tierLevel].cashAmounts[i].maxCash;

                break;
            }
            case "exp": {
                mission.minExpReward = missionTiers[tierLevel].expAmounts[i].minExp;
                mission.maxExpReward = missionTiers[tierLevel].expAmounts[i].maxExp;

                break;
            }
            case "item": {
                const selectedItemName = getRandomItemReward(missionTiers[tierLevel].itemRewardNames);
                const item = await ItemRepository.findItemByName(selectedItemName);
                
                if (!item) {
                    // Skip this mission if the item doesn't exist
                    continue;
                }
                
                mission.itemRewardId = item.id;
                mission.itemRewardQuantity = itemAmounts[i % itemAmounts.length];

                break;
            }
            // No default
        }

        missions.push(mission);
    }

    return missions;
};

export const CompleteMission = async (userId: number) => {
    const user = await UserRepository.getUserById(userId);
    if (!user) {
        LogErrorStack({
            message: "User not found with id: " + userId,
            error: new Error("User not found with id: " + userId),
        });
        return;
    }

    if (!user.currentMission) {
        throw new Error("Mission not found");
    }
    const currentMission = await MissionRepository.getMissionById(user.currentMission);
    if (!currentMission) {
        throw new Error("Mission not found");
    }

    const {
        minExpReward,
        maxExpReward,
        minCashReward,
        maxCashReward,
        duration,
        missionName,
        itemRewardId,
        itemRewardQuantity,
    } = currentMission;

    const hours = Math.floor(duration / HOUR_IN_MS);

    let expReward = 0;
    let itemReward = "";
    let cashReward = 0;
    const learnerTalent = await TalentHelper.UserHasLearnerTalent(user.id);

    if (minExpReward > 0 && maxExpReward > 0) {
        expReward = getRandomReward(minExpReward, maxExpReward);
        if (learnerTalent) {
            expReward = Math.round(expReward * (learnerTalent.modifier || 1));
        }
        await UserService.AddXPToUser(user, expReward);
    }
    if (minCashReward > 0 && maxCashReward > 0) {
        cashReward = getRandomReward(minCashReward, maxCashReward);
        if (learnerTalent) {
            cashReward *= learnerTalent.modifier || 1;
        }

        // Apply shrine yenEarnings buff if active
        const yenEarningsBuff = await ShrineHelper.dailyBuffIsActive("yenEarnings", user.id);
        if (yenEarningsBuff) {
            cashReward = Math.round(cashReward * yenEarningsBuff);
        }

        user.cash = user.cash + cashReward;
    }
    if (itemRewardId) {
        const item = await ItemRepository.findItemById(itemRewardId);
        if (!item) {
            throw new Error("Item not found");
        }
        await InventoryService.AddItemToUser({
            userId: user.id,
            itemId: item.id,
            amount: itemRewardQuantity || 1,
            isTradeable: true,
        });
        itemReward = item.name;
    }

    user.currentMission = null;
    user.missionEnds = null;

    await MissionRepository.updateUser(user);

    // Award focus for mission hours completed
    await FocusService.addMissionHourFocus(user.id, hours);

    await emitMissionCompleted({
        userId: user.id,
        missionId: currentMission.id,
        hours,
    });
    NotificationService.NotifyUser(user.id, NotificationTypes.mission_completed, {
        missionName,
        exp: expReward,
        cash: cashReward,
        itemReward,
        itemRewardQuantity,
    });

    logAction({
        action: "MISSION_COMPLETED",
        userId: userId,
        info: {
            missionId: currentMission.id,
            missionName: currentMission.missionName,
        },
    });
    return;
};
