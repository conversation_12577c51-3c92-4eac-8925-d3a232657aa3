import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { ChevronRight, Scroll, Volume2, VolumeX, X, WifiOff } from "lucide-react";
import { Link } from "react-router-dom";
import { useSessionStore, useSocketStore } from "../../../app/store/stores";
import ChatDropdownButton from "./ChatDropdownButton";
import { api } from "@/helpers/api";
import type { User } from "@/types/user";
import { Socket } from "socket.io-client";
import type { ChatRoom } from "../types/chat";
import { useState } from "react";
import ChatRulesModal from "./ChatRulesModal";

interface ChatTopPanelProps {
    muteChat: boolean;
    setMuteChat: (mute: boolean) => void;
    currentUser: User;
    socket: Socket;
    setHideChat: (hide: boolean) => void;
    chatRoom: ChatRoom;
    fullSize: boolean;
}

export default function ChatTopPanel({
    muteChat,
    setMuteChat,
    currentUser,
    socket,
    setHideChat,
    chatRoom,
    fullSize,
}: ChatTopPanelProps) {
    const { hidePollNotification, setHidePollNotification } = useSessionStore();
    const { isSocketConnected } = useSocketStore();
    const { data: availablePolls } = useQuery(api.suggestions.getAvailablePolls.queryOptions());
    const [showRulesModal, setShowRulesModal] = useState(false);

    return (
        <div>
            <div className="-mx-2 relative h-16 border-slate-300 border-b dark:border-gray-600/25">
                <div className="mx-2 flex h-full md:mr-5">
                    <div className="relative mx-1 my-auto">
                        <ButtonWrapper>
                            {muteChat ? (
                                <VolumeX size={20} color="#938ea2" onClick={() => setMuteChat(false)} />
                            ) : (
                                <Volume2 size={20} color="#938ea2" onClick={() => setMuteChat(true)} />
                            )}
                        </ButtonWrapper>
                    </div>

                    <div className="m-auto flex pr-6 text-sm md:pr-0">
                        <ChatDropdownButton chatRoom={chatRoom} currentUser={currentUser} />
                    </div>

                    {/* Chat Rules Modal */}
                    <div
                        className="relative mx-1 my-auto"
                        onClick={() => setShowRulesModal(true)}
                        title="View Chat Rules"
                    >
                        <ButtonWrapper>
                            <Scroll size={20} color="#938ea2" />
                        </ButtonWrapper>
                    </div>
                    <div
                        className="relative mx-1 my-auto hidden translate-x-0 transition duration-200 lg:block"
                        onClick={() => setHideChat(true)}
                    >
                        <ButtonWrapper>
                            <ChevronRight size={20} color="#938ea2" />
                        </ButtonWrapper>
                    </div>
                </div>

                <div
                    className={cn(
                        fullSize ? "md:-bottom-12 -bottom-10 md:h-12" : "-bottom-10",
                        "absolute left-0 z-2 h-11 w-[calc(100%-1px)] bg-linear-to-b from-[#191924] to-transparent"
                    )}
                >
                    {!hidePollNotification && availablePolls?.length > 0 ? (
                        <div className="relative flex h-12 w-full rounded-b-lg border-2 border-black bg-indigo-500/90 text-center text-gray-400 text-sm dark:text-gray-200">
                            <Link to="/polls" className="m-auto cursor-pointer">
                                <p className="m-auto animate-pulse text-custom-yellow text-lg font-display">
                                    Vote in the new Poll
                                </p>
                            </Link>

                            <div
                                className="-translate-y-1/2 absolute top-1/2 right-2 z-10 cursor-pointer rounded-lg border-2 border-transparent p-1 hover:border-black hover:bg-indigo-600 hover:text-white"
                                onClick={() => {
                                    setHidePollNotification(true);
                                }}
                            >
                                <X className="size-6" />
                            </div>
                        </div>
                    ) : null}

                    {/* Disconnection Warning */}
                    {!isSocketConnected && (
                        <div className="bg-base-300 backdrop-blur-sm border-b border-error/80 animate-pulse px-3 py-1.5 text-center">
                            <div className="flex items-center justify-center gap-2 text-warning text-sm font-medium">
                                <WifiOff size={16} className="animate-pulse" />
                                <span>Connection lost. Attempting to reconnect...</span>
                            </div>
                        </div>
                    )}
                </div>

                {/* Chat Rules Modal */}
                <ChatRulesModal open={showRulesModal} onOpenChange={setShowRulesModal} />
            </div>
        </div>
    );
}

interface ButtonWrapperProps {
    children: React.ReactNode;
}

const ButtonWrapper = ({ children }: ButtonWrapperProps) => {
    return (
        <button className="size-9 cursor-pointer rounded-md border-[#1F1F2D] border-b bg-[#28283C] shadow-[0_1px_0_0_#303045_inset,0_2px_2px_0_rgba(0,0,0,0.25)] transition hover:brightness-110">
            <div className="flex items-center justify-center">{children}</div>
        </button>
    );
};
