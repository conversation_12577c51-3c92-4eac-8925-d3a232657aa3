import React from "react";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import { DisplayNPCImage } from "@/components/DisplayNPCImage";
import { Crosshair, Droplets, Flame, Shield, Skull, Swords, Timer, Trophy } from "lucide-react";
import { BattlePlayer, CombatLogEntry } from "../types/battle";

// Action text configuration using template strings
// eslint-disable-next-line no-unused-vars
const ACTION_TEMPLATES: Record<string, string | ((isActor: boolean) => string)> = {
    battle_start: "{actor} started a battle with {target}",
    melee: "{actor} struck {target} with a melee attack{damage}.",
    ranged: "{actor} hit {target} with a ranged attack{damage}.",
    attack: "{actor} attacked {target}{damage}.",
    battle_win: (isActor: boolean) =>
        isActor
            ? "{actor} defeated {target} and claimed victory!"
            : "{actor} defeated {target}. Better luck next time!",
    flee_failed: "{actor} tried to flee from the battle but failed!",
    flee_success: "{actor} successfully fled from the battle!",
    rage: "{actor} went into a rage!",
    spray: "{actor} sprayed {target} with a barrage of attacks{damage}!",
};

export const getActionIcon = (action: string, isActor: boolean) => {
    switch (action?.toLowerCase()) {
        case "attack":
        case "melee":
            return <Swords color="black" className="size-4" />;
        case "ranged":
            return <Crosshair color="black" className="size-4" />;
        case "flee_success":
        case "flee_failed":
        case "flee":
            return <Timer color="black" className="size-4" />;
        case "battle_win":
            if (isActor) {
                return <Trophy color="black" className="size-4" />;
            }
            return <Skull color="black" className="size-4" />;
        case "rage":
            return <Flame color="black" className="size-4" />;
        case "spray":
            return <Droplets color="black" className="size-4" />;
        default:
            return <Shield color="black" className="size-4" />;
    }
};

export const getActionColor = (action: string, isActor: boolean): string => {
    switch (action?.toLowerCase()) {
        case "attack":
        case "ranged":
        case "melee":
        case "flee_failed":
        case "rage":
        case "spray":
            return "text-purple-400 bg-purple-400/10";
        case "victory":
        case "flee_success":
            return "text-green-400 bg-green-400/10";
        case "battle_start":
            return "text-yellow-400 bg-yellow-400/10";
        case "battle_win":
            if (isActor) {
                return "text-green-400 bg-green-400/10";
            }
            return "text-red-400 bg-red-400/10";
        default:
            return "text-gray-400 bg-gray-400/10";
    }
};

export const getActionText = (action: string, isActor: boolean): string => {
    switch (action?.toLowerCase()) {
        case "attack":
        case "melee":
            return "MELEE";
        case "ranged":
            return "RANGED";
        case "flee_success":
            return "FLEE SUCCESS";
        case "flee_failed":
            return "FLEE FAILED";
        case "battle_win":
            if (isActor) {
                return "VICTORY";
            }
            return "DEFEAT";
        case "battle_start":
            return "BATTLE STARTED";
        case "rage":
            return "RAGE";
        case "spray":
            return "SPRAY";
        default:
            return action?.toUpperCase();
    }
};

export const getAvatar = (id: string, player: BattlePlayer, enemy: BattlePlayer) => {
    const user = player.id === id ? player : enemy;
    if (user.userType === "npc") {
        return <DisplayNPCImage className="mx-0.5 size-5 rounded-full" src={user.avatar} />;
    } else {
        return <DisplayAvatar className="mx-0.5 size-5 rounded-full" src={user} />;
    }
};

export const getPlayerName = (id: string, player: BattlePlayer, enemy: BattlePlayer): React.ReactNode => {
    const username = id === player.id ? player.username : enemy.username;
    return <span className={`font-semibold ${id === player.id ? "text-red-400" : "text-blue-400"}`}>{username}</span>;
};

// Utility to replace placeholders in template strings with React nodes or plain text
const renderTemplate = (
    template: string,
    actorName: React.ReactNode,
    targetName: React.ReactNode,
    damageText: string
): React.ReactNode => {
    const TOKEN_REGEX = /(\{actor\}|\{target\}|\{damage\})/g;

    const segments = template.split(TOKEN_REGEX).filter(Boolean);

    return (
        <>
            {segments.map((segment, idx) => {
                switch (segment) {
                    case "{actor}":
                        return <React.Fragment key={idx}>{actorName}</React.Fragment>;
                    case "{target}":
                        return <React.Fragment key={idx}>{targetName}</React.Fragment>;
                    case "{damage}":
                        return <React.Fragment key={idx}>{damageText}</React.Fragment>;
                    default:
                        return <React.Fragment key={idx}>{segment}</React.Fragment>;
                }
            })}
        </>
    );
};

// Generates the detailed action description by applying the chosen template
const getActionContent = (
    action: string,
    log: CombatLogEntry,
    player: BattlePlayer,
    actorName: React.ReactNode,
    targetName: React.ReactNode,
    damageText: string
): React.ReactNode => {
    const templateEntry = ACTION_TEMPLATES[action];

    // Determine which template to use and resolve it to a string
    let templateString: string;
    if (typeof templateEntry === "function") {
        templateString = templateEntry(log.actorId === player.id);
    } else if (templateEntry) {
        templateString = templateEntry;
    } else {
        templateString = "{actor} used " + log.action + " on {target}{damage}.";
    }

    return renderTemplate(templateString, actorName, targetName, damageText);
};

export const getDetailedActionText = (
    log: CombatLogEntry,
    player: BattlePlayer,
    enemy: BattlePlayer
): React.ReactNode => {
    const actorName = getPlayerName(log.actorId, player, enemy);
    const targetName = getPlayerName(log.targetId, player, enemy);
    const damageText = log.damage ? ` for ${log.damage} damage` : "";
    const action = log?.action?.toLowerCase();

    return getActionContent(action, log, player, actorName, targetName, damageText);
};
