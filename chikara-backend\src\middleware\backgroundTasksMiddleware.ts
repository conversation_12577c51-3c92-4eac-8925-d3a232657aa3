import gameConfig from "../config/gameConfig.js";
import * as UserRepository from "../repositories/user.repository.js";
import * as DailyQuestHelper from "../features/dailyquest/dailyquest.helpers.js";
import * as MissionHelper from "../features/mission/mission.helpers.js";
import { UserModel } from "../lib/db.js";
import { getNow } from "../utils/dateHelpers.js";
import { logger } from "../utils/log.js";
import { NextFunction, Request, Response } from "express";

// Background tasks middleware
export const checkMissionCompletion = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const user = req.user as UserModel;
        const currentTime = getNow();

        if (user?.missionEnds && currentTime.getTime() > Number(user.missionEnds)) {
            await MissionHelper.CompleteMission(user.id);
        }
    } catch (error) {
        logger.error("Error completing mission:" + error);
    }
    next();
};

export const checkDailyFatigueReset = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const user = req.user as UserModel;

        if (user) {
            // Check if daily fatigue should be reset
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            const lastReset = user.lastFatigueReset ? new Date(user.lastFatigueReset) : null;
            const shouldReset = !lastReset || lastReset < today;

            if (shouldReset) {
                // Reset daily fatigue and update last reset time
                await UserRepository.updateUserStats(
                    { id: user.id },
                    {
                        dailyFatigueUsed: 0,
                        lastFatigueReset: today,
                    }
                );

                // Update the user object in the request so subsequent middleware/controllers have the updated values
                user.dailyFatigueUsed = 0;
                user.lastFatigueReset = today;
            }
        }
    } catch (error) {
        logger.error("Error checking daily fatigue reset:" + error);
    }
    next();
};

export const checkDailyQuestGenerated = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const user = req.user as UserModel;

        if (user && (user.level || 0) < gameConfig.DAILY_QUESTS_LEVEL_GATE) {
            return next();
        }

        await DailyQuestHelper.GenerateDailyQuestsForUser(user);
    } catch (error) {
        logger.error("Error generating daily quests:" + error);
    }
    next();
};

export default {
    checkMissionCompletion,
    checkDailyFatigueReset,
    checkDailyQuestGenerated,
};
