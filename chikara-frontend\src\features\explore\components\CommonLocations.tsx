import { useNavigate } from "react-router-dom";
import arcadeIcon from "@/assets/icons/arcade.png";
import auctionIcon from "@/assets/icons/auction.webp";
import bankIcon from "@/assets/icons/bankicon.webp";
import casinoIcon from "@/assets/icons/casino.webp";
import trainingIcon from "@/assets/icons/gymicon.webp";
import hospitalIcon from "@/assets/icons/hospitalicon.webp";
import jailIcon from "@/assets/icons/jailicon.webp";
import jobsIcon from "@/assets/icons/jobs.webp";
import shopsIcon from "@/assets/icons/shopsicon.webp";
import shrineIcon from "@/assets/icons/shrineicon.webp";
import { checkLevelGate } from "@/helpers/levelGates";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";

interface CommonLocation {
    id: string;
    name: string;
    description: string;
    icon: string;
    route: string;
    color: string;
    gateKey?: string; // Key for level gate checking
}

const COMMON_LOCATIONS: CommonLocation[] = [
    {
        id: "training",
        name: "Training",
        description: "Improve your combat stats and grow stronger",
        icon: trainingIcon,
        route: "/training",
        color: "bg-yellow-500 dark:bg-yellow-600",
    },
    {
        id: "shops",
        name: "Shops",
        description: "Buy and sell items",
        icon: shopsIcon,
        route: "/shops",
        color: "bg-green-500 dark:bg-green-600",
    },
    {
        id: "bank",
        name: "Bank",
        description: "Manage your finances and investments",
        icon: bankIcon,
        route: "/bank",
        color: "bg-teal-500 dark:bg-teal-600",
    },
    {
        id: "shrine",
        name: "Shrine",
        description: "Donate to the shrine for blessings",
        icon: shrineIcon,
        route: "/shrine",
        color: "bg-amber-500 dark:bg-amber-600",
    },
    {
        id: "hospital",
        name: "Hospital",
        description: "Recover from injuries and restore health",
        icon: hospitalIcon,
        route: "/hospital",
        color: "bg-pink-500 dark:bg-pink-600",
    },
    {
        id: "jail",
        name: "Jail",
        description: "Serve time or visit incarcerated players",
        icon: jailIcon,
        route: "/jail",
        color: "bg-slate-600 dark:bg-slate-700",
    },
    {
        id: "casino",
        name: "Casino",
        description: "Try your luck with games of chance",
        icon: casinoIcon,
        route: "/casino",
        color: "bg-purple-500 dark:bg-purple-600",
    },
    {
        id: "job",
        name: "Part-Time Job",
        description: "Work part-time to earn money and experience",
        icon: jobsIcon,
        route: "/job",
        color: "bg-red-500 dark:bg-red-600",
        gateKey: "job",
    },
    {
        id: "market",
        name: "Market",
        description: "Trade items in the player marketplace",
        icon: auctionIcon,
        route: "/market",
        color: "bg-blue-500 dark:bg-blue-600",
        gateKey: "market",
    },
    {
        id: "arcade",
        name: "Arcade",
        description: "Play mini-games",
        icon: arcadeIcon,
        route: "/arcade",
        color: "bg-purple-500 dark:bg-purple-600",
        gateKey: "arcade",
    },
];

export const CommonLocations = () => {
    const navigate = useNavigate();
    const { data: currentUser } = useFetchCurrentUser();

    const handleCommonLocationClick = (location: CommonLocation) => {
        navigate(location.route);
    };

    return (
        <div className="mb-4 lg:mb-0 border border-base-100 bg-base-100/50 rounded-lg p-4">
            <div className="space-y-1 w-full flex flex-col justify-center items-center">
                <h2 className="card-title">
                    <span className="badge badge-primary badge-lg text-stroke-0">Common Locations</span>
                </h2>
                <p className="text-xs text-base-content/70 mb-4">Available in every district</p>
            </div>
            <div className="grid grid-cols-5 sm:grid-cols-3 lg:grid-cols-1 gap-1">
                {COMMON_LOCATIONS.map((location) => {
                    const gateResult =
                        location.gateKey && currentUser?.level
                            ? checkLevelGate(location.gateKey, currentUser?.level)
                            : null;
                    const isLocked = gateResult?.isLocked;
                    const description = isLocked ? gateResult.message : location.description;

                    return (
                        <button
                            key={location.id}
                            disabled={isLocked}
                            className={cn(
                                "btn h-auto min-h-0 p-2 lg:p-3 flex-col lg:flex-row justify-start",
                                isLocked ? "btn-disabled" : "hover:btn-outline",
                                "group transition-all duration-300"
                            )}
                            onClick={() => handleCommonLocationClick(location)}
                        >
                            <div className="flex flex-col lg:flex-row items-center lg:items-center text-center lg:text-left space-y-1 lg:space-y-0 lg:space-x-3 w-full">
                                <div className="indicator">
                                    <div
                                        className={cn(
                                            "avatar placeholder transition-transform duration-200",
                                            !isLocked && "group-hover:scale-110"
                                        )}
                                    >
                                        <div
                                            className={cn(
                                                "w-8 h-8 lg:w-10 lg:h-10 rounded-xl",
                                                location.id === "training" &&
                                                    "bg-gradient-to-br from-warning to-warning/60",
                                                location.id === "shops" &&
                                                    "bg-gradient-to-br from-success to-success/60",
                                                location.id === "bank" && "bg-gradient-to-br from-info to-info/60",
                                                location.id === "shrine" &&
                                                    "bg-gradient-to-br from-accent to-accent/60",
                                                location.id === "hospital" &&
                                                    "bg-gradient-to-br from-error to-error/60",
                                                location.id === "jail" &&
                                                    "bg-gradient-to-br from-neutral to-neutral/60",
                                                location.id === "casino" &&
                                                    "bg-gradient-to-br from-secondary to-secondary/60",
                                                location.id === "job" && "bg-gradient-to-br from-primary to-primary/60",
                                                location.id === "market" && "bg-gradient-to-br from-info to-info/60",
                                                location.id === "arcade" &&
                                                    "bg-gradient-to-br from-secondary to-secondary/60"
                                            )}
                                        >
                                            <img
                                                src={location.icon}
                                                alt=""
                                                className="hidden size-full rounded-xl md:block"
                                            />
                                        </div>
                                    </div>
                                    {isLocked && (
                                        <span className="indicator-item badge badge-warning badge-xs">🔒</span>
                                    )}
                                </div>
                                <div className="flex-1 min-w-0">
                                    <h3 className="text-xs lg:text-sm font-bold text-base-content line-clamp-1">
                                        {location.name}
                                    </h3>
                                    <p
                                        className={cn(
                                            "text-xs line-clamp-1 lg:line-clamp-2 mt-0.5 lg:mt-1 hidden sm:block lg:block",
                                            isLocked ? "text-warning" : "text-base-content/60"
                                        )}
                                    >
                                        {description}
                                    </p>
                                </div>
                            </div>
                        </button>
                    );
                })}
            </div>
        </div>
    );
};
