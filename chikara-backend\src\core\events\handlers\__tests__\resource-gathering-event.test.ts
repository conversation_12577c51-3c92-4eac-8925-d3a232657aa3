import { describe, expect, it, vi, beforeEach } from "vitest";
import { handleResourceGatheredEvent } from "../item-event-handlers.js";
import { ResourceGatheredPayload } from "../../event-types.js";
import { LocationTypes } from "@prisma/client";

// Mock the quest service
vi.mock("../../../quest.service.js", () => ({
    handleResourceGathering: vi.fn(),
}));

describe("Resource Gathering Event Handler", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("handleResourceGatheredEvent", () => {
        it("should handle mining resource gathering events", async () => {
            const payload: ResourceGatheredPayload = {
                userId: 123,
                itemId: 456,
                quantity: 3,
                activityType: "mining",
                location: LocationTypes.shibuya,
            };

            await handleResourceGatheredEvent(payload);

            // Should call quest service with correct parameters
            const questService = await import("../../../quest.service.js");
            expect(vi.mocked(questService.handleResourceGathering)).toHaveBeenCalledWith(123, 456, "mining", 3);

            // Note: Achievement updates are not currently implemented for resource gathering
        });

        it("should handle scavenging resource gathering events", async () => {
            const payload: ResourceGatheredPayload = {
                userId: 789,
                itemId: 101,
                quantity: 1,
                activityType: "scavenging",
                location: LocationTypes.alley,
            };

            await handleResourceGatheredEvent(payload);

            // Should call quest service with correct parameters
            const questService = await import("../../../quest.service.js");
            expect(vi.mocked(questService.handleResourceGathering)).toHaveBeenCalledWith(789, 101, "scavenging", 1);

            // Note: Achievement updates are not currently implemented for resource gathering
        });

        it("should handle foraging resource gathering events", async () => {
            const payload: ResourceGatheredPayload = {
                userId: 555,
                itemId: 777,
                quantity: 2,
                activityType: "foraging",
                location: LocationTypes.themepark,
            };

            await handleResourceGatheredEvent(payload);

            // Should call quest service with correct parameters
            const questService = await import("../../../quest.service.js");
            expect(vi.mocked(questService.handleResourceGathering)).toHaveBeenCalledWith(555, 777, "foraging", 2);

            // Note: Achievement updates are not currently implemented for resource gathering
        });

        it("should handle events without location", async () => {
            const payload: ResourceGatheredPayload = {
                userId: 999,
                itemId: 888,
                quantity: 5,
                activityType: "mining",
            };

            await handleResourceGatheredEvent(payload);

            // Should still call quest service
            const questService = await import("../../../quest.service.js");
            expect(vi.mocked(questService.handleResourceGathering)).toHaveBeenCalledWith(999, 888, "mining", 5);

            // Note: Achievement updates are not currently implemented for resource gathering
        });
    });
});
