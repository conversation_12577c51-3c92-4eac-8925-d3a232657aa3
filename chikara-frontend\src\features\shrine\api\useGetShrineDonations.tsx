import { api, type QueryOptions } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { type AppRouterClient } from "@/lib/orpc";

export type ShrineDonation = Awaited<ReturnType<AppRouterClient["shrine"]["getDonations"]>>[number];

export const useGetShrineDonations = (options: QueryOptions = {}) => {
    return useQuery(
        api.shrine.getDonations.queryOptions({
            staleTime: 30000, // 30 seconds
            ...options,
        })
    );
};
