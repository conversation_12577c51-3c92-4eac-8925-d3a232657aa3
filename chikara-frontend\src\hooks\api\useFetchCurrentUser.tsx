import { api, QueryOptions } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useAuthStore, usePersistStore } from "../../app/store/stores";

const useFetchCurrentUser = (options: QueryOptions = {}) => {
    const { staleCurrentUserData, setStaleCurrentUserData, gameConfig } = usePersistStore();
    const authed = useAuthStore((state) => state.authed);

    const query = useQuery(
        api.user.getCurrentUserInfo.queryOptions({
            placeholderData: staleCurrentUserData,
            enabled: !!authed,
            staleTime: 30000,
            ...options,
        })
    );

    // Handle side effects when data changes
    useEffect(() => {
        if (query.data) {
            if (query.data !== staleCurrentUserData) {
                try {
                    setStaleCurrentUserData(query.data);
                } catch (error) {
                    console.error("Failed to store user data:", error);
                    // Continue without storing stale data if serialization fails
                }
            }
        }
    }, [query.data, staleCurrentUserData, setStaleCurrentUserData, gameConfig]);

    return query;
};

export default useFetchCurrentUser;
