# Chikara Academy PBBG - Game Summary

## Overview

Chikara Academy is an immersive persistent browser-based game (PBBG) that transports players into a vibrant world where martial arts, strategy, and adventure converge. Set in a modern urban environment with mystical elements, players take on the role of aspiring martial artists seeking to master their craft while navigating complex social dynamics, gang rivalries, and personal growth.

The game is GUI and image based and does not contain a game world or active character control.

The game seamlessly integrates traditional PBBG mechanics with modern gaming elements, offering a unique blend of:

- Strategic turn-based combat with deep tactical choices
- Rich character progression systems that affect gameplay
- Dynamic social interactions through gangs and chat rooms
- An player-driven economy with multiple currencies and trading systems
- Roguelike elements that ensure no two play sessions are identical
- Daily activities and long-term progression goals

Players can forge their path through various gameplay styles - whether as a lone wolf perfecting their combat techniques, a social player building alliances within gangs, or an economic mastermind trading in the marketplace.

## Core Gameplay Features

### Character Progression

- **Leveling & Experience System**

    - Dynamic XP gain from diverse activities: combat, quests, crafting, exploration
    - Milestone-based rewards at key level thresholds
    - Activity-specific experience tracking for specialized progression
    - Prestige system for long-term advancement

- **Stats & Talents**

    - Core attributes affecting gameplay:
        - Strength: Determines physical damage and carrying capacity
        - Dexterity: Influences accuracy, dodge chance, and critical hits
        - Intelligence: Affects ability effectiveness and crafting success
        - Stamina: Controls energy pool and regeneration rates
    - Specialized talent trees with unique combat and utility abilities
    - Respec system allowing build experimentation
    - Temporary and permanent stat boost mechanisms

- **Health & Stamina Management**
    - Strategic resource management during encounters
    - Multiple recovery methods: items, resting, special abilities
    - Stamina-based activity limitations
    - Health regeneration systems varying by location and status

### Combat System

- **Turn-Based Battles**

    - 1v1 Turn based battles
    - Multiple attack types: melee attacks, ranged attacks (limited ammo pool), up to 4 equipped combat skills
    - Combat skills can do damage, CC, buffs, debuffs etc
    - Can run away from battles you initiate (chance to fail)

- **Battle Variants**

    - Traditional 1v1 PvP and PvE battles
    - Rooftop combat featuring NPC boss battles with unique mechanics
    - Tournament-style competitions with special rules
    - Roguelike mode with procedurally generated enemies

- **Status Effects & Abilities**
    - Comprehensive combat modifier system:
        - Temporary buffs and debuffs
        - Damage-over-time effects
        - Healing and regeneration
        - Crowd control abilities
        - Special condition states
    - Ability combinations and synergies
    - Counter-play options for all status effects

### Roguelike Mode

- **Dynamic Map Generation**

    - Procedurally generated layouts ensuring unique experiences
    - Multiple difficulty tiers affecting rewards and challenges
    - Special themed events and seasonal variations
    - Hidden paths and secret areas
    - Progressive difficulty scaling

- **Encounter Choices**

    - Diverse node types:
        - Combat encounters with varying difficulty
        - Merchant interactions for mid-run purchases
        - Rest sites for recovery
        - Challenge rooms with high risk/reward
        - Story events affecting future runs
    - Decision points impacting run trajectory
    - Resource management between encounters

- **Path Balancing**
    - Risk/reward decision making
    - Multiple viable strategies for completion
    - Adaptive difficulty based on player performance
    - Special modifiers affecting entire runs
    - Meta-progression system between attempts

### Questing & Story Progression

- **Diverse Quest Types**

    - Main storyline missions advancing the core narrative
    - Daily and weekly challenges for regular rewards
    - Gang-specific missions affecting faction standing
    - Tutorial quests introducing game mechanics
    - Random events providing unexpected opportunities
    - Special seasonal and event-based questlines

- **Interactive Dialogue**

    - Branching conversation paths with meaningful choices
    - Character relationship system affecting dialogue options
    - Hidden dialogue options based on player stats/achievements
    - Dynamic NPC responses reflecting world state
    - Quest-specific dialogue affecting outcomes

- **Quest Chains**
    - Multi-stage storylines with branching paths
    - Reputation-based quest unlocks
    - Time-sensitive mission sequences
    - Achievement-linked special quests
    - Cross-character quest progression

### Crafting & In-Game Economy

- **Shops:** Several shops are available throughout the game world, each offering a unique selection of items. Some shops may operate on a reputation system, where higher reputation with the shop owner unlocks access to better items or discounts.

- **Shop Currencies:** Shops can utilize different currencies for purchases, including Yen (the primary in-game currency), Gang Creds (earned through gang activities), and Classroom Points (earned through specific class-related actions).

- **Item Crafting**

    - Complex crafting system with multiple components:
        - Resource gathering from various activities
        - Recipe discovery through exploration and achievements
        - Quality-based crafting outcomes
        - Crafting specializations and mastery levels
    - Equipment modification and enhancement
    - Consumable item creation
    - Special event crafting opportunities

- **Marketplace Mechanics**

    - Multiple currency systems:
        - Standard Yen for basic transactions
        - Gang credits for faction-specific items
        - Premium currency for special features
        - Event tokens for limited-time offerings
    - Dynamic player-driven market prices
    - Auction house for rare item trading
    - Timed market events and sales
    - Cross-server trading opportunities

- **Resource Management**
    - Strategic inventory management
    - Multiple storage options:
        - Personal inventory
        - Gang storage
        - Premium storage expansions
    - Resource conversion systems
    - Investment opportunities
    - Resource node control and competition

### Social & Community Features

- **Gangs & Factions**

    - Hierarchical gang structure with roles and permissions
    - Inter-gang warfare and territory control
    - Collaborative gang activities:
        - Group missions
        - Resource gathering
        - Territory defense
    - Gang progression and perks system
    - Alliance networks between gangs

- **Real-Time Interaction**

    - Comprehensive communication systems:
        - Global chat
        - Gang chat
        - Private messaging
        - Trade chat
        - Combat announcements
    - Friend system with social features
    - Activity feed showing important events
    - Real-time notifications for various game events

- **Progress & Achievements**

    - Extensive achievement system:
        - Combat milestones
        - Crafting achievements
        - Exploration records
        - Social accomplishments
        - Collection completions
    - Leaderboards for various activities
    - Personal statistics tracking
    - Achievement-based rewards and titles

    - **Missions:** Missions are timed activities that reward players upon completion.

- **Shrine:** Players can donate to a shrine to unlock global buffs that benefit all players.
- **Infirmary:** Players can heal their characters at the infirmary for a cost.
- **Chat:** A chat system allows players to communicate with each other.
- **Player PvP Bounties**
- **Casino**
- **Exams**
- **Events**
- **Jail**
- **Private Messages**
- **Real Estate Purchasing**
- **Pets**

## Technical Overview

- **Backend & Infrastructure**

    - Modern Node.js/Express.js architecture
    - Robust database management with MySQL/Prisma
    - Microservices architecture for scalability
    - Comprehensive API documentation
    - Automated testing and deployment

- **Performance & Scalability**

    - Redis implementation for:
        - Session management
        - Caching
        - Real-time data
    - BullMQ for reliable task queuing
    - Socket.io for real-time communications
    - Load balancing and auto-scaling
    - Performance monitoring and optimization

- **Modular Architecture**
    - Service-oriented design:
        - Battle system service
        - Quest management service
        - Economy service
        - Social system service
        - Roguelike system service
    - Plugin architecture for easy expansion
    - Event-driven communication between services
    - Comprehensive error handling and logging
