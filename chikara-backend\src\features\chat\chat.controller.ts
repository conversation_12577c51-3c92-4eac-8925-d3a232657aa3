import gameConfig from "../../config/gameConfig.js";
import { SendNotification } from "../../config/socket.js";
import * as ChatHelpers from "./chat.helpers.js";
import * as chatRepository from "../../repositories/chat.repository.js";
import { UserModel } from "../../lib/db.js";
import { NotificationTypes } from "../../types/notification.js";
import { getRejectedMessageError } from "../../utils/contentFilter.js";
import { LogErrorStack, logger } from "../../utils/log.js";
import { Server } from "socket.io";

interface MessageData {
    message: string;
    room: {
        id: number;
    };
    parentMessageId?: number;
}

let roomNames: Record<number, string> = {};

export async function loadRoomNames(): Promise<void> {
    const rooms = await chatRepository.findAllChatRooms();
    roomNames = rooms.reduce((acc: Record<number, string>, room) => {
        acc[room.id] = room.name;
        return acc;
    }, {});
}

export function startRoomNamesRefreshInterval(): void {
    loadRoomNames().catch((error) => {
        logger.error("Failed to load room names: " + error);
    });

    setInterval(() => {
        loadRoomNames().catch((error) => {
            logger.error("Failed to refresh room names: " + error);
        });
    }, 600_000); // Checks every 10 mins
}

export const getRoomName = (roomId: number): string | undefined => {
    return roomNames[roomId];
};

export const handleChatMessage = async (
    io: Server,
    msg: MessageData,
    currentUser: UserModel<{
        select: {
            id: true;
            username: true;
            avatar: true;
            userType: true;
            level: true;
            chatBannedUntil: true;
        };
    }>
) => {
    if (!roomNames[msg.room.id]) {
        logger.info("Attempt to send message to non-existent room");
        return null;
    }

    try {
        if (!ChatHelpers.UserCanSendMessage(currentUser, msg)) {
            // If user is not chat banned but message was rejected, it's likely because of blacklisted words
            if (!currentUser.chatBannedUntil) {
                SendNotification(currentUser.id, {
                    type: NotificationTypes.temporary_notification,
                    details: getRejectedMessageError(),
                });
            }
            return null;
        }

        const messageData = {
            message: msg.message,
            userId: currentUser.id,
            chatRoomId: msg.room.id,
            ...(msg.parentMessageId && { parentMessageId: msg.parentMessageId }),
        };

        const parentMessage = msg.parentMessageId
            ? await chatRepository.findMessageWithUser(msg.parentMessageId)
            : null;

        const newMessage = await chatRepository.createChatMessageWithUser(messageData);

        if (!newMessage) {
            LogErrorStack({
                message: "Failed to create chat message",
                error: new Error("Failed to create chat message"),
            });
            return null;
        }

        const messageToEmit = {
            ...newMessage,
            user: {
                id: currentUser.id,
                username: currentUser.username,
                avatar: currentUser.avatar,
                userType: currentUser.userType,
                level: currentUser.level,
            },
            ...(msg.parentMessageId && parentMessage ? { parentMessage } : {}),
        };

        io?.to(roomNames[msg.room.id]).emit("chat message", messageToEmit);

        if (gameConfig.DISCORD_CHAT_WEBHOOK_ENABLED && process.env.DISCORD_WEBHOOK_URL) {
            ChatHelpers.sendToDiscordChat(msg.message, currentUser);
        }

        return newMessage;
    } catch (error) {
        LogErrorStack({ message: "Failed to process chat message:", error });
        return null;
    }
};

export const getChatHistory = async (roomId: number, limit = 10) => {
    try {
        const requestedLimit =
            limit && !Number.isNaN(limit) && Number.parseInt(String(limit)) > 0 ? Number.parseInt(String(limit)) : 10;

        const messages = await chatRepository.findChatMessages(
            roomId,
            Math.min(requestedLimit, gameConfig.MAX_CHAT_HISTORY_LENGTH)
        );

        // Transform the response to rename chat_message to parentMessage for consistency
        const transformedMessages = messages.map((message) => {
            const { chat_message, ...rest } = message;
            return {
                ...rest,
                parentMessage: chat_message,
            };
        });

        return { data: transformedMessages };
    } catch (error) {
        LogErrorStack({ message: `Failed to fetch chat history`, error });
        return { error: "Failed to fetch chat history", statusCode: 400 };
    }
};

export const getChatRooms = async () => {
    try {
        const rooms = await chatRepository.findAllChatRooms();
        return { data: rooms };
    } catch (error) {
        LogErrorStack({ message: `Failed to fetch chat rooms`, error });
        return { error: "Failed to fetch chat rooms", statusCode: 400 };
    }
};

/**
 * Create and send a system announcement to a chat room
 * @param announcementType The type of announcement
 * @param message The message content
 * @param roomId Optional chat room ID (defaults to global chat)
 * @param currentUser The admin user making the request
 */
export const createAnnouncement = async (
    announcementType: string,
    message: string,
    roomId = 1,
    currentUser: UserModel
) => {
    try {
        // Check if the user is an admin
        if (currentUser.userType !== "admin" && currentUser.userType !== "prefect") {
            return { error: "Unauthorized access", statusCode: 403 };
        }

        if (!roomNames[roomId]) {
            return { error: "Invalid chat room", statusCode: 400 };
        }

        await ChatHelpers.SendAnnouncementMessage(announcementType, message, roomId);

        return {
            data: {
                success: true,
                message: "Announcement sent successfully",
            },
        };
    } catch (error) {
        LogErrorStack({ message: `Failed to send announcement`, error });
        return { error: "Failed to send announcement", statusCode: 500 };
    }
};
