import gameConfig from "../../config/gameConfig.js";
import * as UserService from "../../core/user.service.js";
import * as NotificationRepository from "../../repositories/notification.repository.js";
import * as User<PERSON>elper from "../user/user.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { LogErrorStack } from "../../utils/log.js";
import * as UserRepository from "../../repositories/user.repository.js";

export const getNotificationList = async (userId: number, limit = 0) => {
    try {
        const requestedLimit = Math.min(Math.max(limit, 0), gameConfig.MAX_NOTIFICATION_HISTORY_LENGTH);
        const notifications = await NotificationRepository.findNotifications(userId, requestedLimit);
        return { data: notifications };
    } catch (error) {
        LogErrorStack({ message: "Failed to fetch notifications:", error });
        return { error: "Failed to fetch notifications", statusCode: 400 };
    }
};

export const getNumberOfUnreadNotifications = async (userId: number) => {
    try {
        const unreadCount = await NotificationRepository.countUnreadNotifications(userId);
        return { data: { unread: unreadCount } };
    } catch (error) {
        LogErrorStack({ message: "Failed to get unread notifications:", error });
        return { error: "Failed to get unread notifications", statusCode: 400 };
    }
};

export const MarkNotificationRead = async (userId: number, notificationId: number) => {
    try {
        const notification = await NotificationRepository.findNotificationById(notificationId);

        if (!notification || notification.userId != userId) {
            return { error: "Invalid notification", statusCode: 400 };
        }

        notification.read = true;
        await NotificationRepository.updateNotification(notification);
        return { data: true };
    } catch (error) {
        LogErrorStack({ message: "Failed to mark notification read:", error });
        return { error: "Failed to mark notification read", statusCode: 400 };
    }
};

export const saveFCMToken = async (userId: number, token: string) => {
    try {
        if (!token || !userId) {
            return { error: "Invalid token or user id", statusCode: 400 };
        }

        const pushToken = await NotificationRepository.findPushTokenByToken(token);

        if (pushToken) {
            return { data: "Token already exists", statusCode: 208 };
        }

        await NotificationRepository.createPushToken(userId, token);
        return { data: "Successfully saved token" };
    } catch (error) {
        // if (error instanceof Error && error.name === "SequelizeUniqueConstraintError") {
        //     return { data: "Token already exists", statusCode: 208 };
        // }
        LogErrorStack({ message: "Failed to save FCM token:", error });
        return { error: "Failed to save FCM token", statusCode: 400 };
    }
};

export const removeFCMToken = async (userId: number, token: string) => {
    try {
        if (!token || !userId) {
            return { error: "Invalid token or user id", statusCode: 400 };
        }

        await NotificationRepository.deletePushToken(userId, token);
        return { data: { message: "Token deleted" } };
    } catch (error) {
        LogErrorStack({ message: "Failed to save FCM token:", error });
        return { error: "Failed to save FCM token", statusCode: 400 };
    }
};

export const UpdatePushNotificationSettings = async (userId: number, pushEnabled: boolean) => {
    try {
        const currentUser = await UserRepository.getUserById(userId);
        if (!currentUser) {
            return { error: "User not found", statusCode: 404 };
        }

        const updateValues: { pushNotificationsEnabled?: boolean } = {};
        const detailsChangedLog = [];

        if (pushEnabled !== undefined && pushEnabled !== currentUser.pushNotificationsEnabled) {
            updateValues.pushNotificationsEnabled = pushEnabled;
            detailsChangedLog.push("Set push notifications enabled to " + pushEnabled);
        }

        await UserService.updateUser(currentUser.id, updateValues);

        logAction({
            action: "PUSH_NOTIFICATION_SETTINGS_UPDATED",
            userId: userId,
            info: {
                pushNotificationsEnabled: pushEnabled,
            },
        });

        return { data: UserHelper.GetSafeUserDict(currentUser) };
    } catch (error) {
        LogErrorStack({ error });
        return { error: "Something went wrong", statusCode: 400 };
    }
};
