import { DisplayItem } from "@/components/DisplayItem";
import { formatCraftTime } from "@/features/crafting/helpers/craftingHelpers";
import { getUserItemCount } from "@/helpers/getUserItemCount";
import { cn } from "@/lib/utils";
import type { InventoryItem } from "@/types/item";
import { AlertCircle, Clock, Hammer } from "lucide-react";
import useStartCraft from "../api/useStartCraft";
import type { CraftingRecipe, CraftingRecipeInput, CraftingRecipeOutput, CraftingSkills } from "../types/crafting";

interface CraftableItemProps {
    recipe: CraftingRecipe;
    outputItem: CraftingRecipeOutput;
    userInventory: InventoryItem[] | undefined;
    isCraftQueueFull: boolean;
}

// Helper function to convert skill type enum to readable string
const getSkillName = (skillType: CraftingSkills): string | null => {
    switch (skillType) {
        case "fabrication":
            return "Fabrication";
        case "electronics":
            return "Electronics";
        case "chemistry":
            return "Chemistry";
        case "outfitting":
            return "Outfitting";
        default:
            return null;
    }
};

// Check if player has enough materials to craft an item
const canCraftItem = (recipe: CraftingRecipe, inventory: InventoryItem[] = []): boolean => {
    if (!recipe || !recipe.inputs || !Array.isArray(recipe.inputs)) return false;
    if (!inventory || !Array.isArray(inventory)) return false;

    // Check all inputs against inventory
    return recipe.inputs.every((input: CraftingRecipeInput) => {
        const required = input.amount || 1;
        const available = getUserItemCount(inventory, input.id);
        return available >= required;
    });
};

export default function CraftableItem({ recipe, outputItem, userInventory, isCraftQueueFull }: CraftableItemProps) {
    const { mutate: startCraft, isPending } = useStartCraft();
    const canCraft = canCraftItem(recipe, userInventory);
    const disabled = !canCraft || isPending || isCraftQueueFull;

    const handleCraft = () => {
        startCraft({
            recipeId: recipe.id,
            amount: 1, // Default to crafting 1 item
        });
    };

    return (
        <div
            className={cn(
                "flex flex-col rounded-lg border transition-colors overflow-hidden",
                "bg-gray-800/50 border-gray-700/50 hover:bg-gray-800/80"
            )}
        >
            {/* Item Header */}
            <div className="p-2">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <div className="size-12 rounded-lg flex items-center justify-center">
                            <DisplayItem itemTypeFrame item={outputItem} className="" />
                        </div>
                        <div>
                            <h4 className="text-white text-sm font-medium">
                                {outputItem.name}
                                {outputItem.amount > 1 && (
                                    <span className="ml-1 text-green-400 font-bold">x{outputItem.amount}</span>
                                )}
                            </h4>
                            <div className="flex items-center gap-1 text-xs">
                                <span className="text-purple-300">
                                    {getSkillName(recipe.requiredSkillType)}
                                    {/* Lv{recipe.requiredSkillLevel} */}
                                </span>
                                <span className="text-gray-500">•</span>
                                <span className="text-gray-400 flex items-center gap-1">
                                    <Clock className="size-3" />
                                    {formatCraftTime(recipe.craftTime)}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div className="flex items-center gap-2">
                        {!canCraft && (
                            <div className="size-6 rounded-full bg-red-900/30 border border-red-700/50 flex items-center justify-center">
                                <AlertCircle className="size-4 text-red-400" />
                            </div>
                        )}
                        <button
                            disabled={disabled}
                            className={cn(
                                "flex items-center justify-center gap-1 px-3 py-1.5 rounded-md text-sm font-medium",
                                disabled
                                    ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                                    : "bg-purple-600 hover:bg-purple-700 text-white"
                            )}
                            onClick={handleCraft}
                        >
                            <Hammer className={cn("size-4", isPending && "animate-pulse")} />
                            <span className="text-stroke-sm">{isPending ? "Crafting..." : "Craft"}</span>
                        </button>
                    </div>
                </div>
                {/* Materials Section */}
                <div className="divider divider-primary my-2">Materials</div>
                <div className="space-y-2">
                    {recipe.inputs.map((input: CraftingRecipeInput) => {
                        const required = input.amount || 1;
                        const available = getUserItemCount(userInventory || [], input.id);
                        const hasEnough = available >= required;

                        return (
                            <div
                                key={input.id}
                                className="flex items-center justify-between p-2 rounded-lg bg-base-200/50"
                            >
                                <div className="flex items-center gap-2">
                                    <div className="avatar">
                                        <div className="w-8 rounded">
                                            <DisplayItem noBackground item={input} className="" />
                                        </div>
                                    </div>
                                    <span className="text-sm font-medium">{input.name}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <progress
                                        value={Math.min(available, required)}
                                        max={required}
                                        className={cn(
                                            "progress w-20 h-2",
                                            hasEnough ? "progress-success" : "progress-error"
                                        )}
                                    ></progress>
                                    <span
                                        className={cn(
                                            "badge badge-sm font-bold text-stroke-0",
                                            hasEnough ? "badge-success" : "badge-error"
                                        )}
                                    >
                                        {available}/{required}
                                    </span>
                                </div>
                            </div>
                        );
                    })}
                </div>{" "}
            </div>
        </div>
    );
}
