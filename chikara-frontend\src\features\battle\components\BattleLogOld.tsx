import { <PERSON><PERSON><PERSON>, <PERSON>, Sparkles } from "lucide-react";
import { BattlePlayer, CombatLogEntry } from "../types/battle";
import { useAutoAnimate } from "@formkit/auto-animate/react";
import {
    getActionColor,
    getActionIcon,
    getActionText,
    getAvatar,
    getDetailedActionText,
} from "../helpers/battleLogHelpers";

interface CombatLogProps {
    logs: CombatLogEntry[];
    player: BattlePlayer;
    enemy: BattlePlayer;
}

const BattleLog: React.FC<CombatLogProps> = ({ logs, player, enemy }) => {
    const [animationParent] = useAutoAnimate();

    const formatTime = (timestamp: number) => {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
    };

    return (
        <div className="relative h-48 lg:h-96 rounded-lg border border-gray-700 bg-gray-800 shadow-xl">
            {/* Scroll indicator */}
            <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-b from-gray-700/50 to-transparent pointer-events-none z-10" />
            <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-t from-gray-700/50 to-transparent pointer-events-none z-10" />

            <div className="h-full overflow-y-auto p-2 md:p-3 scroll-smooth" style={{ scrollBehavior: "smooth" }}>
                {logs.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-full text-gray-400">
                        <p className="text-xs md:text-sm text-gray-400 text-center">No combat actions to display</p>
                    </div>
                ) : (
                    <div ref={animationParent} className="space-y-0.5 md:space-y-1">
                        {[...logs].reverse().map((log) => (
                            <div
                                key={log.id}
                                className="group border-gray-700 border-b px-1 md:px-2 py-1 md:py-1.5 pb-1 md:pb-2 transition-colors last:border-0 hover:bg-gray-800/50"
                            >
                                <div className="flex items-center gap-1 md:gap-2">
                                    {String(log.round) && (
                                        <div className="mr-1 md:mr-2 rounded-full bg-blue-400 md:size-6 relative">
                                            <span className="text-xs md:text-sm font-semibold text-white absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                                {String(log.round)}
                                            </span>
                                        </div>
                                    )}
                                    <div className="flex items-center rounded-md border border-gray-700 p-0.5 md:p-1 text-gray-400 text-xs shadow-xl">
                                        {getAvatar(log.actorId, player, enemy)}
                                        <ArrowRight className="mx-0.5 md:mx-1 size-3 md:size-4" />
                                        {getAvatar(log.targetId, player, enemy)}
                                        {log.damage !== undefined && log.damage > 0 && (
                                            <div className="ml-0.5 md:ml-1 flex items-center text-red-400 text-xs md:text-sm">
                                                -{log.damage} <Heart className="ml-0.5 md:ml-1 size-2.5 md:size-3.5" />
                                            </div>
                                        )}
                                    </div>
                                    <div
                                        className={`flex items-center rounded-md px-1 md:px-1.5 py-0.5 ${getActionColor(log.action, log.actorId === player.id)}`}
                                    >
                                        {getActionIcon(log.action, log.actorId === player.id)}
                                        <span className="ml-0.5 md:ml-1 font-medium text-xs">
                                            {getActionText(log.action, log.actorId === player.id)}
                                        </span>
                                    </div>

                                    {/* Detailed action description - hidden on mobile */}
                                    <span className="hidden md:inline ml-4 text-gray-300 text-sm">
                                        {getDetailedActionText(log, player, enemy)}
                                    </span>

                                    <span className="ml-auto font-mono text-blue-300 text-xs font-semibold">
                                        {formatTime(log.timestamp)}
                                    </span>
                                </div>

                                {((log.details?.statusEffects && log.details.statusEffects.length > 0) ||
                                    log.remainingHealth) && (
                                    <div className="mt-0.5 md:mt-1 ml-2 md:ml-4 flex items-center gap-2 md:gap-3 text-xs">
                                        {log.details &&
                                            log.details.statusEffects &&
                                            log.details.statusEffects.length > 0 && (
                                                <div className="flex items-center text-blue-400">
                                                    <Sparkles className="mr-0.5 md:mr-1 size-2 md:size-3" />
                                                    {log.details?.statusEffects.join(", ")}
                                                </div>
                                            )}
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default BattleLog;
