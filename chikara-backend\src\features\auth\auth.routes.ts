import { handleResponse } from "../../utils/routeHandler.js";
import { publicAuth, isLoggedInAuth, adminAuth } from "../../lib/orpc.js";
import * as AuthController from "./auth.controller.js";
import authSchema from "./auth.validation.js";
import { checkCode, codeList, generateCode, massEmailCodes, referralCodeList } from "./registrationcode.controller.js";

export const authRouter = {
    // Public endpoint: Register a new user
    register: publicAuth.input(authSchema.register).handler(async ({ input, context }) => {
        const { code, username, email, password } = input;
        // Get headers from context
        const headers = context?.headers || {};
        const response = await AuthController.registerUser(headers, username, email, password, code);
        return handleResponse(response);
    }),

    // Authenticated endpoint: Get user's referral codes
    getReferralCodes: isLoggedInAuth.handler(async ({ context }) => {
        const response = await referralCodeList(context.user.id, context.user.username);
        return handleResponse(response);
    }),

    // Public endpoint: Check if a registration code is valid
    checkCode: publicAuth.input(authSchema.checkCodeSchema).handler(async ({ input }) => {
        const response = await checkCode(input.code);
        return handleResponse(response);
    }),
};

// Admin endpoints - separated to be included in the main admin router
export const authAdminRouter = {
    // Admin endpoint: Get all registration codes
    getCodes: adminAuth.handler(async () => {
        const response = await codeList();
        return handleResponse(response);
    }),

    // Admin endpoint: Generate a new registration code
    generateCode: adminAuth.input(authSchema.generateCodeSchema).handler(async ({ input }) => {
        const response = await generateCode(input.note);
        return handleResponse(response);
    }),

    // Admin endpoint: Mass email registration codes
    massEmailCodes: adminAuth.input(authSchema.massEmailSchema).handler(async ({ input, context }) => {
        const { emails, note } = input;
        const response = await massEmailCodes(emails, note, context.user.id);
        return handleResponse(response);
    }),
};
