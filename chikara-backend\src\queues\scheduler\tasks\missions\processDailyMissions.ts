import missions from "../../../../data/missions.js";
import * as MissionHelper from "../../../../features/mission/mission.helpers.js";
import { db } from "../../../../lib/db.js";
import { getToday, getTomorrow } from "../../../../utils/dateHelpers.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";
import { Prisma } from "@prisma/client";

interface Mission {
    id: number;
    name: string;
    description: string;
    // Add other mission properties as needed
}

type MissionTier = Record<string, Mission[]>;

function selectRandomMissions(missionList: Mission[], count: number): Mission[] {
    const listCopy = [...missionList];
    const shuffled = listCopy.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
}

async function generateMissionsForDate(date: Date): Promise<void> {
    const missionSets = missions as unknown as MissionTier;
    const dailyMissions: Record<string, Mission[]> = {};

    for (const tier of Object.keys(missionSets)) {
        dailyMissions[tier] = selectRandomMissions(missionSets[tier], 3);
    }

    const missionsToCreate: Prisma.daily_missionCreateManyInput[] = [];
    for (const tier of Object.keys(dailyMissions)) {
        const tierLevel = Number.parseInt(tier.replace("tier", ""));
        const tierData = await MissionHelper.GenerateMissionsForTier(tierLevel, date, dailyMissions[tier]);

        // Ensure each mission has a proper Date object for missionDate and exclude rewardType
        const processedTierData = tierData.map((mission) => {
            // Create a new object without the rewardType field
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { rewardType, ...missionWithoutRewardType } = mission;

            return {
                ...missionWithoutRewardType,
                missionDate: mission.missionDate,
            };
        });

        missionsToCreate.push(...processedTierData);
    }

    try {
        await db.daily_mission.createMany({
            data: missionsToCreate,
        });
        logger.info(`Stored new daily missions for ${date}`);
    } catch (error: unknown) {
        const err = error as Error;
        logger.error(`Error storing daily missions for ${date}: ${err.message}`, err);
        throw error;
    }
}

async function processDailyMissions(): Promise<void> {
    const tomorrow = getTomorrow();

    try {
        const count = await db.daily_mission.count({
            where: {
                missionDate: tomorrow,
            },
        });

        if (count === 0) {
            logger.profile("processDailyMissions");
            await generateMissionsForDate(tomorrow);
            logger.profile("processDailyMissions");
        } else {
            logger.info("Daily missions for tomorrow are already set.");
        }
    } catch (error) {
        LogErrorStack({ error: "Failed to generate tomorrow's daily missions: " + error });
    }
}

async function checkAndProcessMissions(): Promise<void> {
    const today = getToday();

    try {
        const count = await db.daily_mission.count({
            where: {
                missionDate: today,
            },
        });

        if (count === 0) {
            logger.info("No missions for today. Generating now.");
            await generateMissionsForDate(today);
        } else {
            logger.info("Missions for today are already set.");
        }
    } catch (error) {
        LogErrorStack({ error: "Failed to check/process today's missions: " + error });
    }
}

export default async function processDailyMissionsTask(): Promise<void> {
    await checkAndProcessMissions();
    await processDailyMissions();
}
