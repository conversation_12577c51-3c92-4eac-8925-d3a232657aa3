import { logger, LogErrorStack } from "../../../utils/log.js";
import { ItemDroppedPayload, ItemCraftedPayload, ResourceGatheredPayload } from "../event-types.js";
import * as AchievementService from "../../achievement.service.js";
import * as QuestService from "../../quest.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";

/**
 * Handle item dropped events for quest objectives and achievements
 */
export const handleItemDroppedEvent = async (payload: ItemDroppedPayload): Promise<void> => {
    try {
        const { userId, itemId, quantity } = payload;

        // Get user data for processing
        const user = await UserRepository.getUserById(userId);
        if (!user) {
            logger.error(`User not found for item dropped event: ${userId}`);
            return;
        }

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handleFetchItem(userId, itemId, quantity);

        // Note: No specific achievement for item collection in current schema
        // Items collected are tracked through quest objectives instead

        logger.debug(`Processed item dropped event for user ${userId}, item ${itemId}, quantity ${quantity}`);
    } catch (error) {
        LogErrorStack({ message: "Error handling item dropped event", error });
    }
};

/**
 * Handle item crafted events for quest objectives and achievements
 */
export const handleItemCraftedEvent = async (payload: ItemCraftedPayload): Promise<void> => {
    try {
        const { userId, itemId, quantity } = payload;

        // Get user data for processing
        const user = await UserRepository.getUserById(userId);
        if (!user) {
            logger.error(`User not found for item crafted event: ${userId}`);
            return;
        }

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handleCraftItem(userId, itemId, quantity);

        // Handle achievements
        await AchievementService.UpdateUserAchievement(userId, "craftsCompleted", quantity);

        logger.debug(`Processed item crafted event for user ${userId}, item ${itemId}, quantity ${quantity}`);
    } catch (error) {
        LogErrorStack({ message: "Error handling item crafted event", error });
    }
};

/**
 * Handle resource gathered events for quest objectives and achievements
 */
export const handleResourceGatheredEvent = async (payload: ResourceGatheredPayload): Promise<void> => {
    try {
        const { userId, itemId, quantity, activityType } = payload;

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handleResourceGathering(userId, itemId, activityType, quantity);

        // Note: Specific achievements for mining, scavenging, and foraging are not currently implemented
        // in the user_achievements table. If needed, they can be added to the schema later.

        logger.debug(
            `Processed resource gathered event for user ${userId}, item ${itemId}, quantity ${quantity}, activity ${activityType}`
        );
    } catch (error) {
        LogErrorStack({ message: "Error handling resource gathered event", error });
    }
};
