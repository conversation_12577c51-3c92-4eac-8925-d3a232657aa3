import { api } from "@/helpers/api";
import { formatCurrency } from "@/utils/currencyHelpers";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

/**
 * Hook for purchasing a property
 */
export const usePurchaseProperty = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.property.purchaseProperty.mutationOptions({
            onSuccess: () => {
                toast.success("Property purchased successfully!");
                // Invalidate relevant queries
                queryClient.invalidateQueries({
                    queryKey: api.property.getHousingList.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.property.getUserProperties.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "Failed to purchase property");
            },
        })
    );
};

/**
 * Hook for selling a property
 */
export const useSellProperty = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.property.sellProperty.mutationOptions({
            onSuccess: (data) => {
                toast.success(`Property sold for ${formatCurrency(data.soldFor)}!`);
                // Invalidate relevant queries
                queryClient.invalidateQueries({
                    queryKey: api.property.getHousingList.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.property.getUserProperties.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "Failed to sell property");
            },
        })
    );
};

