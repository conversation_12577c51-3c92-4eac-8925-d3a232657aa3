import { defaultMockUser } from "../../../__tests__/prismaModelMocks.js";
import AchievementHelpers from "../../../core/achievement.service.js";
import * as MissionController from "../mission.controller.js";
import * as MissionRepository from "../../../repositories/mission.repository.js";
import * as ShrineHelpers from "../../shrine/shrine.helpers.js";
import { logAction } from "../../../lib/actionLogger.js";
import { getToday } from "../../../utils/dateHelpers.js";
import { LogErrorStack } from "../../../utils/log.js";
import { beforeEach, describe, expect, it, vi } from "vitest";
import * as UserRepository from "../../../repositories/user.repository.js";

// Mock dependencies
vi.mock("@/repositories/mission.repository.js");
vi.mock("@/repositories/user.repository.js");
vi.mock("@/features/shrine/shrine.helpers.js");
vi.mock("@/core/achievement.service.js");
vi.mock("@/utils/dateHelpers.js");

const mockMissionRepository = vi.mocked(MissionRepository);
const mockUserRepository = vi.mocked(UserRepository);
const mockShrineHelpers = vi.mocked(ShrineHelpers);
const mockAchievementHelpers = vi.mocked(AchievementHelpers);
const mockGetToday = vi.mocked(getToday);
const mockLogAction = vi.mocked(logAction);
const mockLogError = vi.mocked(LogErrorStack);

describe("Mission Controller", () => {
    const mockDate = new Date("2023-01-01");
    const mockMissions = [
        {
            id: 1,
            missionName: "Test Mission 1",
            duration: 7200000, // 2 hours in ms
            levelReq: 5,
            hoursReq: 0,
            rewardType: "cash",
            minCashReward: 100,
            maxCashReward: 200,
            tier: 1,
            missionDate: mockDate,
        },
        {
            id: 2,
            missionName: "Test Mission 2",
            duration: 14400000, // 4 hours in ms
            levelReq: 10,
            hoursReq: 15,
            rewardType: "exp",
            minExpReward: 500,
            maxExpReward: 1000,
            tier: 2,
            missionDate: mockDate,
        },
    ];

    const mockUser = {
        ...defaultMockUser,
        id: 1,
        level: 10,
        currentMission: null,
        missionEnds: null,
        hospitalisedUntil: null,
        jailedUntil: null,
    };

    beforeEach(() => {
        vi.clearAllMocks();
        mockGetToday.mockReturnValue(mockDate);
    });

    describe("missionList", () => {
        it("should return missions for today without shrine buff", async () => {
            mockMissionRepository.getMissionsByDate.mockResolvedValue(mockMissions);
            mockShrineHelpers.dailyBuffIsActive.mockResolvedValue(null);

            const result = await MissionController.missionList();

            expect(mockMissionRepository.getMissionsByDate).toHaveBeenCalledWith(mockDate);
            expect(mockShrineHelpers.dailyBuffIsActive).not.toHaveBeenCalled();
            expect(result).toEqual({ data: mockMissions });
        });

        it("should apply shrine buff to mission durations when active", async () => {
            const shrineBuffValue = 0.5; // 50% reduction
            const expectedMissions = mockMissions.map((mission) => ({
                ...mission,
                duration: Math.round(mission.duration * shrineBuffValue),
            }));

            mockMissionRepository.getMissionsByDate.mockResolvedValue(mockMissions);
            mockShrineHelpers.dailyBuffIsActive.mockResolvedValue(shrineBuffValue);

            const result = await MissionController.missionList(1);

            expect(mockShrineHelpers.dailyBuffIsActive).toHaveBeenCalledWith("mission", 1);
            expect(result.data[0].duration).toBe(3600000); // 7200000 * 0.5
            expect(result.data[1].duration).toBe(7200000); // 14400000 * 0.5
        });
    });

    describe("currentMission", () => {
        const mockMission = {
            id: 1,
            missionName: "Current Mission",
            duration: 7200000,
            levelReq: 5,
            hoursReq: 0,
        };

        it("should return error when no mission ID provided", async () => {
            const result = await MissionController.currentMission(null);

            expect(result).toEqual({
                error: "Not on a mission!",
                statusCode: 400,
            });
        });

        it("should return mission data when valid mission ID provided", async () => {
            mockMissionRepository.getMissionById.mockResolvedValue(mockMission);

            const result = await MissionController.currentMission(1);

            expect(mockMissionRepository.getMissionById).toHaveBeenCalledWith(1);
            expect(result).toEqual({ data: mockMission });
        });
    });

    describe("startMission", () => {
        const mockMission = {
            id: 1,
            missionName: "Test Mission",
            duration: 7200000,
            levelReq: 5,
            hoursReq: 0,
            tier: 1,
        };

        it("should return error when no mission ID provided", async () => {
            const result = await MissionController.startMission(1, null);

            expect(result).toEqual({
                error: "Mission ID is required",
                statusCode: 400,
            });
        });

        it("should return error when mission not found", async () => {
            mockMissionRepository.getMissionByIdAndDate.mockResolvedValue(null);

            const result = await MissionController.startMission(1, 1);

            expect(result).toEqual({
                error: "Mission not found",
                statusCode: 404,
            });
        });

        it("should return error when user not found", async () => {
            mockMissionRepository.getMissionByIdAndDate.mockResolvedValue(mockMission);
            mockUserRepository.getUserById.mockResolvedValue(null);

            const result = await MissionController.startMission(1, 1);

            expect(result).toEqual({
                error: "User not found",
                statusCode: 404,
            });
        });

        it("should return error when user already on a mission", async () => {
            const userOnMission = {
                ...mockUser,
                currentMission: 2,
                missionEnds: BigInt(Date.now() + 3600000),
            };

            mockMissionRepository.getMissionByIdAndDate.mockResolvedValue(mockMission);
            mockUserRepository.getUserById.mockResolvedValue(userOnMission);

            const result = await MissionController.startMission(1, 1);

            expect(result).toEqual({
                error: "Already on a mission!",
                statusCode: 400,
            });
        });

        it("should return error when user is hospitalized", async () => {
            const hospitalizedUser = {
                ...mockUser,
                hospitalisedUntil: BigInt(Date.now() + 3600000),
            };

            mockMissionRepository.getMissionByIdAndDate.mockResolvedValue(mockMission);
            mockUserRepository.getUserById.mockResolvedValue(hospitalizedUser);

            const result = await MissionController.startMission(1, 1);

            expect(result).toEqual({
                error: "Can't start a mission while incapacitated",
                statusCode: 400,
            });
        });

        it("should return error when user is jailed", async () => {
            const jailedUser = {
                ...mockUser,
                jailedUntil: BigInt(Date.now() + 3600000),
            };

            mockMissionRepository.getMissionByIdAndDate.mockResolvedValue(mockMission);
            mockUserRepository.getUserById.mockResolvedValue(jailedUser);

            const result = await MissionController.startMission(1, 1);

            expect(result).toEqual({
                error: "Can't start a mission while incapacitated",
                statusCode: 400,
            });
        });

        it("should return error when user level is too low", async () => {
            const lowLevelUser = {
                ...mockUser,
                level: 3,
            };
            const highLevelMission = {
                ...mockMission,
                levelReq: 10,
            };

            mockMissionRepository.getMissionByIdAndDate.mockResolvedValue(highLevelMission);
            mockUserRepository.getUserById.mockResolvedValue(lowLevelUser);
            mockAchievementHelpers.GetUserAchievement.mockResolvedValue(50);

            const result = await MissionController.startMission(1, 1);

            expect(result).toEqual({
                error: "Not Eligible",
                statusCode: 400,
            });
        });

        it("should return error when user doesn't have enough mission hours", async () => {
            const missionWithHoursReq = {
                ...mockMission,
                hoursReq: 100,
            };

            mockMissionRepository.getMissionByIdAndDate.mockResolvedValue(missionWithHoursReq);
            mockUserRepository.getUserById.mockResolvedValue(mockUser);
            mockAchievementHelpers.GetUserAchievement.mockResolvedValue(50); // User has 50 hours, mission requires 100

            const result = await MissionController.startMission(1, 1);

            expect(result).toEqual({
                error: "Not Eligible",
                statusCode: 400,
            });
        });

        it("should successfully start mission without shrine buff", async () => {
            const mockStartTime = Date.now();
            vi.spyOn(Date, "now").mockReturnValue(mockStartTime);

            mockMissionRepository.getMissionByIdAndDate.mockResolvedValue(mockMission);
            mockUserRepository.getUserById.mockResolvedValue(mockUser);
            mockAchievementHelpers.GetUserAchievement.mockResolvedValue(50);
            mockShrineHelpers.dailyBuffIsActive.mockResolvedValue(null);
            mockMissionRepository.updateUserMissionStart.mockResolvedValue();

            const result = await MissionController.startMission(1, 1);

            const expectedEndTime = mockStartTime + mockMission.duration;
            expect(mockShrineHelpers.dailyBuffIsActive).toHaveBeenCalledWith("mission", 1);
            expect(mockMissionRepository.updateUserMissionStart).toHaveBeenCalledWith(mockUser, 1, expectedEndTime);
            expect(mockLogAction).toHaveBeenCalledWith({
                action: "MISSION_STARTED",
                userId: 1,
                info: {
                    missionId: 1,
                    missionName: "Test Mission",
                },
            });
            expect(result).toEqual({ data: "Mission started successfully" });
        });

        it("should successfully start mission with shrine buff", async () => {
            const mockStartTime = Date.now();
            const shrineBuffValue = 0.5;
            vi.spyOn(Date, "now").mockReturnValue(mockStartTime);

            mockMissionRepository.getMissionByIdAndDate.mockResolvedValue(mockMission);
            mockUserRepository.getUserById.mockResolvedValue(mockUser);
            mockAchievementHelpers.GetUserAchievement.mockResolvedValue(50);
            mockShrineHelpers.dailyBuffIsActive.mockResolvedValue(shrineBuffValue);
            mockMissionRepository.updateUserMissionStart.mockResolvedValue();

            const result = await MissionController.startMission(1, 1);

            const expectedEndTime = mockStartTime + mockMission.duration * shrineBuffValue;
            expect(mockShrineHelpers.dailyBuffIsActive).toHaveBeenCalledWith("mission", 1);
            expect(mockMissionRepository.updateUserMissionStart).toHaveBeenCalledWith(mockUser, 1, expectedEndTime);
            expect(result).toEqual({ data: "Mission started successfully" });
        });

        it("should handle internal errors", async () => {
            mockMissionRepository.getMissionByIdAndDate.mockRejectedValue(new Error("Database error"));

            const result = await MissionController.startMission(1, 1);

            expect(mockLogError).toHaveBeenCalled();
            expect(result).toEqual({
                error: "Failed to start mission due to an internal error",
                statusCode: 500,
            });
        });
    });

    describe("cancelMission", () => {
        it("should return error when user not found", async () => {
            mockUserRepository.getUserById.mockResolvedValue(null);

            const result = await MissionController.cancelMission(1);

            expect(result).toEqual({
                error: "User not found",
                statusCode: 404,
            });
        });

        it("should return error for banned user (temporary check)", async () => {
            const bannedUser = { ...mockUser, id: 14 };
            mockUserRepository.getUserById.mockResolvedValue(bannedUser);

            const result = await MissionController.cancelMission(14);

            expect(result).toEqual({
                error: "Banned from mission cancelling",
                statusCode: 400,
            });
        });

        it("should return error when user not on a mission", async () => {
            const userNotOnMission = {
                ...mockUser,
                currentMission: null,
                missionEnds: null,
            };
            mockUserRepository.getUserById.mockResolvedValue(userNotOnMission);

            const result = await MissionController.cancelMission(1);

            expect(result).toEqual({
                error: "Not on a mission!",
                statusCode: 400,
            });
        });

        it("should successfully cancel mission", async () => {
            const userOnMission = {
                ...mockUser,
                currentMission: 5,
                missionEnds: BigInt(Date.now() + 3600000),
            };
            mockUserRepository.getUserById.mockResolvedValue(userOnMission);
            mockMissionRepository.updateUserMissionCancel.mockResolvedValue();

            const result = await MissionController.cancelMission(1);

            expect(mockMissionRepository.updateUserMissionCancel).toHaveBeenCalledWith(userOnMission);
            expect(mockLogAction).toHaveBeenCalledWith({
                action: "MISSION_CANCELLED",
                userId: 1,
                info: {
                    missionId: 5,
                },
            });
            expect(result).toEqual({ data: "Mission cancelled successfully" });
        });

        it("should handle internal errors", async () => {
            mockUserRepository.getUserById.mockRejectedValue(new Error("Database error"));

            const result = await MissionController.cancelMission(1);

            expect(mockLogError).toHaveBeenCalled();
            expect(result).toEqual({
                error: "Failed to cancel mission due to an internal error",
                statusCode: 500,
            });
        });
    });
});
