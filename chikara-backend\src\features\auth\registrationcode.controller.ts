import crypto from "node:crypto";
import { logAction } from "../../lib/actionLogger.js";
import { db } from "../../lib/db.js";
import EmailHelpers from "../../utils/email.js";
import { LogErrorStack } from "../../utils/log.js";
import gameConfig from "../../config/gameConfig.js";

export const codeList = async () => {
    const codes = await db.registration_code.findMany();
    return { data: codes };
};

const createCode = async (note: string, referrerId: number | null = null) => {
    const codeData: { code: string; note: string; referrerId?: number } = {
        code: crypto.randomUUID(),
        note: note,
    };

    if (referrerId) {
        codeData.referrerId = referrerId;
    }

    return await db.registration_code.create({ data: codeData });
};

export const referralCodeList = async (userId: number, username: string) => {
    const codes = await db.registration_code.findMany({
        where: { referrerId: userId },
    });

    if (!codes || codes.length < gameConfig.REFERRAL_CODE_LIMIT) {
        const codesToCreate = gameConfig.REFERRAL_CODE_LIMIT - codes.length;
        for (let i = 0; i < codesToCreate; i++) {
            const code = await createCode(`Referred by ${username}`, userId);
            codes.push(code);
        }
    }
    return { data: codes };
};

export const generateCode = async (note: string) => {
    if (!note) {
        return { error: "Set a note to identify user!", statusCode: 400 };
    }
    const code = await createCode(note);
    return { data: { code } };
};

export const checkCode = async (code: string) => {
    const registrationCode = await db.registration_code.findFirst({
        where: { code: code, claimerId: null },
    });

    return {
        statusCode: registrationCode ? 200 : 400,
        data: registrationCode ? { valid: true } : { valid: false },
    };
};

export const massEmailCodes = async (emails: string[], note: string, userId: number) => {
    if (!emails || emails.length === 0) {
        return { error: "No emails provided", statusCode: 400 };
    }

    const results: { email: string; status: string; code?: string; error?: string }[] = [];

    await Promise.all(
        emails.map(async (email) => {
            try {
                const code = await db.registration_code.create({
                    data: {
                        code: crypto.randomUUID(),
                        note: note || email,
                    },
                });
                await EmailHelpers.RegistrationCodeEmail(email, code.code);

                logAction({
                    action: "ALPHA_KEY_SENT",
                    userId: userId,
                    info: {
                        code: code.code,
                    },
                });
                results.push({ email: email, status: "Success", code: code.code });
            } catch (error) {
                LogErrorStack({ message: `Failed to send email to ${email}`, error });
                results.push({ email: email, status: "Failed", error: (error as Error).message });
            }
        })
    );

    return { data: results };
};
