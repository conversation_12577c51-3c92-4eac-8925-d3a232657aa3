import Button from "@/components/Buttons/DaisyButton";
import { useGetHousingList } from "../api/useGetHousingList";
import { usePurchaseProperty } from "../api/usePropertyMutations";
import { useGetUserProperties } from "../api/useGetUserProperties";
import { Property } from "../types/property";
import { Home, DollarSign, Coins, Package, TrendingUp, RefreshCw } from "lucide-react";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";

interface PropertyCardProps {
    property: Property;
    userCash: number;
    onPurchase: (_propertyId: number) => void;
    isPurchasing: boolean;
    existingPropertyOfSameType?: {
        cost: number;
        name: string;
    };
}

const PropertyCard = ({
    property,
    userCash,
    onPurchase,
    isPurchasing,
    existingPropertyOfSameType,
}: PropertyCardProps) => {
    const isReplacement = !!existingPropertyOfSameType;
    const sellValue = existingPropertyOfSameType ? Math.floor(existingPropertyOfSameType.cost * 0.2) : 0;
    const netCost = isReplacement ? property.cost - sellValue : property.cost;
    const canAfford = userCash >= netCost;

    return (
        <div className="card bg-base-300 shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-primary/20 hover:border-primary/40">
            <div className="card-body">
                <div className="flex items-start gap-4">
                    <div className="flex size-12 items-center justify-center rounded-lg bg-indigo-500/20">
                        <Home className="h-6 w-6 text-indigo-400" />
                    </div>
                    <div className="flex-1">
                        <h3 className="card-title text-xl">{property.name}</h3>
                        <p className="text-base-content/70 mb-4">{property.description}</p>

                        <div className="stats stats-horizontal bg-base-100 shadow-md mb-4">
                            <div className="stat py-2">
                                <div className="stat-figure text-success">
                                    <Coins className="w-6 h-6" />
                                </div>
                                <div className="stat-title text-xs">{isReplacement ? "Net Cost" : "Price"}</div>
                                <div className="stat-value text-lg text-success">${netCost.toLocaleString()}</div>
                                {isReplacement && (
                                    <div className="stat-desc text-xs">
                                        ${property.cost.toLocaleString()} - ${sellValue.toLocaleString()} trade-in
                                    </div>
                                )}
                            </div>
                            <div className="stat py-2">
                                <div className="stat-figure text-info">
                                    <Package className="w-6 h-6" />
                                </div>
                                <div className="stat-title text-xs">Slots</div>
                                <div className="stat-value text-lg text-info">{property.slots}</div>
                            </div>
                        </div>

                        {isReplacement && (
                            <div className="alert mb-4">
                                <div className="flex items-center gap-2 text-sm">
                                    <RefreshCw color="orange" className="w-4 h-4" />
                                    <span className="text-stroke-0 text-yellow-500">
                                        This will replace your existing {existingPropertyOfSameType.name}
                                    </span>
                                </div>
                            </div>
                        )}

                        <Button
                            fullWidth
                            variant="primary"
                            isLoading={isPurchasing}
                            onClick={() => onPurchase(property.id)}
                        >
                            <span className="flex gap-2">
                                {canAfford ? (
                                    <>
                                        <TrendingUp className="w-5 h-5" />
                                        Purchase Property
                                    </>
                                ) : (
                                    <>
                                        <DollarSign className="w-5 h-5" />
                                        Insufficient Funds
                                    </>
                                )}
                            </span>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export const PropertyList = () => {
    const { data: properties, isLoading, error } = useGetHousingList();
    const { data: user } = useFetchCurrentUser();
    const { data: userProperties } = useGetUserProperties();
    const purchaseProperty = usePurchaseProperty();

    const handlePurchase = (propertyId: number) => {
        purchaseProperty.mutate({ propertyId });
    };

    const getExistingPropertyOfSameType = (propertyType: string) => {
        if (!userProperties) return undefined;

        const existing = userProperties.find((up) => up.property.propertyType === propertyType);
        return existing
            ? {
                  cost: existing.property.cost,
                  name: existing.property.name,
              }
            : undefined;
    };

    const isPropertyOwned = (propertyId: number) => {
        if (!userProperties) return false;
        return userProperties.some((up) => up.property.id === propertyId);
    };

    // Filter out properties the user already owns
    const availableProperties = properties?.filter((property) => !isPropertyOwned(property.id)) || [];

    if (isLoading) {
        return (
            <div className="flex justify-center items-center py-16">
                <span className="loading loading-dots loading-lg text-primary"></span>
            </div>
        );
    }

    if (error) {
        return (
            <div className="alert alert-error shadow-lg">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="stroke-current shrink-0 h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                </svg>
                <span>Failed to load properties. Please try again later.</span>
            </div>
        );
    }

    if (!properties || properties.length === 0) {
        return (
            <div className="hero bg-base-300 rounded-lg py-12">
                <div className="hero-content text-center">
                    <div className="max-w-md">
                        <Home className="w-16 h-16 mx-auto mb-4 text-primary" />
                        <h3 className="text-2xl font-bold mb-2">No Properties Available</h3>
                        <p className="text-base-content/70">Check back later for new properties to purchase!</p>
                    </div>
                </div>
            </div>
        );
    }

    if (availableProperties.length === 0) {
        return (
            <div className="hero bg-base-300 rounded-lg py-12">
                <div className="hero-content text-center">
                    <div className="max-w-md">
                        <Home className="w-16 h-16 mx-auto mb-4 text-primary" />
                        <h3 className="text-2xl font-bold mb-2">No New Properties Available</h3>
                        <p className="text-base-content/70">You already own all available properties!</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold flex items-center gap-3">Available Properties</h2>
            </div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-1">
                {availableProperties.map((property) => (
                    <PropertyCard
                        key={property.id}
                        property={property}
                        userCash={user?.cash || 0}
                        isPurchasing={purchaseProperty.isPending}
                        existingPropertyOfSameType={getExistingPropertyOfSameType(property.propertyType)}
                        onPurchase={handlePurchase}
                    />
                ))}
            </div>
        </div>
    );
};
