import gameConfig from "../../config/gameConfig.js";
import * as UserService from "../../core/user.service.js";
import { BattlePlayer } from "../battle/types/battle.types.js";
import * as talentsRepository from "../../repositories/talents.repository.js";
import { TALENT_NAMES } from "./talents.types.js";
import * as TalentCache from "../../lib/cache/talentCache.js";
import { TalentModel, ExtUserModel, UserTalentModel } from "../../lib/db.js";
import { handleInternalError, LogErrorStack, logger } from "../../utils/log.js";

/**
 * Retrieves all cached talents.
 */
export const GetAllTalents = async () => {
    return await TalentCache.getAllTalents();
};

/**
 * Retrieves a talent by its ID.
 */
export const GetTalentById = async (talentId: number) => {
    const talent = await TalentCache.getTalentById(talentId);
    if (!talent) {
        LogErrorStack({
            error: new Error(`Talent with ID ${talentId} does not exist.`),
        });
        return null;
    }
    return talent;
};

/**
 * Retrieves a talent by its name.
 * @param {string} talentName - The name of the talent to retrieve.
 */
export const GetTalentByName = async (talentName: string) => {
    const talents = await TalentCache.getAllTalents();
    const talent = talents.find((tal) => tal.name === talentName);
    if (!talent) {
        LogErrorStack({
            error: new Error(`Talent with name ${talentName} does not exist.`),
        });
        return null;
    }
    return talent;
};

/**
 * Retrieves a user's talents and calculates total tree points based on their levels.
 */

type TalentInfo = Omit<UserTalentModel, "createdAt" | "updatedAt"> & {
    talentInfo?: TalentModel;
};

interface UserTalents {
    treePoints: Record<string, number>;
    talentList?: TalentInfo[];
}
export const GetUserTalents = async (userId: number) => {
    const userTalents: UserTalents = {
        treePoints: {},
    };

    userTalents.talentList = await talentsRepository.findUserTalents(userId);

    // Retrieve all talents from the cache
    const talents = await TalentCache.getAllTalents();
    const talentsMap = new Map(talents.map((talent) => [talent.id, talent]));

    // Calculate tree points based on user talents
    for (const talent of userTalents.talentList) {
        const talentInfo = talentsMap.get(talent.talentId);
        if (!talentInfo) {
            LogErrorStack({
                error: new Error(`Talent with ID ${talent.talentId} not found in cache.`),
            });
            continue;
        }
        talent.talentInfo = talentInfo;
        const tree = talentInfo.tree;
        if (!userTalents.treePoints[tree]) {
            userTalents.treePoints[tree] = 0;
        }

        userTalents.treePoints[tree] += talent.level ?? 0;
    }

    return userTalents;
};

/**
 * Levels up a user's talent by updating their talent information and tree points.
 */
export const LevelUpTalent = async (userId: number, talentId: number) => {
    const existingTalent = await talentsRepository.findUserTalent(userId, talentId);

    if (existingTalent && existingTalent.level) {
        return await talentsRepository.updateUserTalentLevel(userId, talentId, existingTalent.level + 1);
    }
    return await talentsRepository.createUserTalent(userId, talentId);
};

/**
 * Retrieves all talent modifiers for a given talent name.
 * @param talentName - The name of the talent to fetch modifiers for.
 */
export const GetAllTalentModifiers = async (talentName: string) => {
    const talent = await GetTalentByName(talentName);
    if (!talent) {
        return handleInternalError(`Talent does not exist: ${talentName}`);
    }
    const modifiers = [talent.tier1Modifier, talent.tier2Modifier, talent.tier3Modifier];
    return modifiers;
};

/**
 * Retrieves the secondary modifier for a given talent name.
 * @param talentName - The name of the talent to retrieve the secondary modifier for.
 */
export const GetTalentSecondaryModifier = async (talentName: string) => {
    const talent = await GetTalentByName(talentName);
    if (!talent) {
        return handleInternalError(`Talent does not exist: ${talentName}`);
    }
    return talent.secondaryModifier;
};

/**
 * Retrieves the modifier for a given talent and level.
 * @param talent - The talent object.
 * @param level - The level of the talent.
 * @returns The modifier corresponding to the talent's level.
 */
export const GetTalentModifierByLevel = (talent: TalentModel, level: number) => {
    if (level === 1) {
        return talent.tier1Modifier;
    }
    if (level === 2) {
        return talent.tier2Modifier;
    }
    if (level === 3) {
        return talent.tier3Modifier;
    }

    LogErrorStack({ error: new Error("Invalid talent level: " + level) });
    return null;
};

/**
 * Checks if a user has a specific talent.
 */
interface NewUserTalent extends UserTalentModel {
    modifier?: number;
    secondaryModifier?: number;
}

export const UserHasTalent = async (userId: number | string, talentName: string) => {
    const talent = await GetTalentByName(talentName);
    if (!talent) {
        return null;
    }

    const userTalent = await talentsRepository.findUserTalent(userId, talent.id);

    if (!userTalent || userTalent.level === null) {
        return null;
    }
    const newUserTalent: NewUserTalent = userTalent;
    const talentModifier = GetTalentModifierByLevel(talent, userTalent.level);

    if (!talentModifier) {
        LogErrorStack({ error: new Error("Invalid talent modifier for talent: " + talent.name) });
        return null;
    }
    newUserTalent.modifier = talentModifier;
    newUserTalent.secondaryModifier = talent.secondaryModifier ?? undefined;
    return newUserTalent;
};

// Create a factory function for specific talent checks
const createTalentCheckFunction = (talentName: string) => {
    return async (userId: number | string) => {
        return await UserHasTalent(userId, talentName);
    };
};

// Define all user-specific talent checks using the factory function
export const UserHasMeleeDamageIncreaseTalent = createTalentCheckFunction(TALENT_NAMES.MELEE_DAMAGE_INCREASE);
export const UserHasBerserkerTalent = createTalentCheckFunction(TALENT_NAMES.BERSERKER);
export const UserHasBullyTalent = createTalentCheckFunction(TALENT_NAMES.BULLY);
export const UserHasOffensiveOffhandsTalent = createTalentCheckFunction(TALENT_NAMES.OFFENSIVE_OFFHANDS);
export const UserHasCombatRegenerationTalent = createTalentCheckFunction(TALENT_NAMES.COMBAT_REGENERATION);

// Defense talents
export const UserHasActiveDefenceTalent = createTalentCheckFunction(TALENT_NAMES.ACTIVE_DEFENCE);
export const UserHasStrongBonesTalent = createTalentCheckFunction(TALENT_NAMES.STRONG_BONES);
export const UserHasGoodStomachTalent = createTalentCheckFunction(TALENT_NAMES.GOOD_STOMACH);
export const UserHasShieldBearerTalent = createTalentCheckFunction(TALENT_NAMES.SHIELDBEARER);
export const UserHasMitigationTalent = createTalentCheckFunction(TALENT_NAMES.MITIGATION);
export const UserHasDeflectDamageTalent = createTalentCheckFunction(TALENT_NAMES.DEFLECT_DAMAGE);

// Intelligence talents
export const UserHasHealthyCasterTalent = createTalentCheckFunction(TALENT_NAMES.HEALTHY_CASTER);
export const UserHasCunningRatTalent = createTalentCheckFunction(TALENT_NAMES.CUNNING_RAT);
export const UserHasSpeedCrafterTalent = createTalentCheckFunction(TALENT_NAMES.SPEED_CRAFTER);
export const UserHasMultiTaskerTalent = createTalentCheckFunction(TALENT_NAMES.MULTITASKER);
export const UserHasInvestorTalent = createTalentCheckFunction(TALENT_NAMES.INVESTOR);
export const UserHasLearnerTalent = createTalentCheckFunction(TALENT_NAMES.LEARNER);
export const UserHasReviveTalent = createTalentCheckFunction(TALENT_NAMES.REVIVE);

// Endurance talents
export const UserHasEnergeticTalent = createTalentCheckFunction(TALENT_NAMES.ENERGETIC);
export const UserHasBuiltTalent = createTalentCheckFunction(TALENT_NAMES.BUILT);
export const UserHasMuggerTalent = createTalentCheckFunction(TALENT_NAMES.MUGGER);
export const UserHasCowardTalent = createTalentCheckFunction(TALENT_NAMES.COWARD);
export const UserHasOutOfCombatRegenerationTalent = createTalentCheckFunction(TALENT_NAMES.OUTSIDE_COMBAT_REGENERATION);
export const UserHasRecoveryTalent = createTalentCheckFunction(TALENT_NAMES.RECOVERY);
export const UserHasAbilityEfficiencyTalent = createTalentCheckFunction(TALENT_NAMES.ABILITY_EFFICIENCY);
export const UserHasFreeMovementTalent = createTalentCheckFunction(TALENT_NAMES.FREE_MOVEMENT);
export const UserHasRejuvenationTalent = createTalentCheckFunction(TALENT_NAMES.REJUVENATION);

// Dexterity talents
export const UserHasRangerTalent = createTalentCheckFunction(TALENT_NAMES.RANGER);
export const UserHasEscapeArtistTalent = createTalentCheckFunction(TALENT_NAMES.ESCAPE_ARTIST);
export const UserHasQuiverTalent = createTalentCheckFunction(TALENT_NAMES.QUIVER);
export const UserHasQuickTurnTakerTalent = createTalentCheckFunction(TALENT_NAMES.QUICK_TURN_TAKER);
export const UserHasShadowStepTalent = createTalentCheckFunction(TALENT_NAMES.SHADOW_STEP);

/**
 * Resets a user's talents and updates their attributes accordingly.
 * @param userId - The ID of the user.
 * @param currentUser - The current user object.
 */
export const ResetTalents = async (userId: number, currentUser: ExtUserModel) => {
    try {
        // Reset equipped abilities
        await talentsRepository.resetUserEquippedAbilities(userId);

        // Get quest-rewarded talent points by calculating the difference between
        // the user's current talent points plus any spent points, and the base talent points
        const userTalents = await GetUserTalents(userId);
        const spentTalentPoints = Object.values(userTalents.treePoints).reduce((acc, points) => acc + points, 0);
        const baseTalentPoints = currentUser.level - gameConfig.TALENTS_LEVEL_GATE;
        const questRewardedTalentPoints = (currentUser.talentPoints || 0) + spentTalentPoints - baseTalentPoints;

        const updateValues: {
            talentPoints?: number;
            maxActionPoints?: { decrement: number };
            health?: { decrement: number };
            currentHealth?: { decrement: number };
        } = {};

        // Reset talent points to the base amount plus any quest-rewarded points
        updateValues.talentPoints = baseTalentPoints + Math.max(0, questRewardedTalentPoints);

        // Handle energetic talent effects
        const energeticTalent = await UserHasEnergeticTalent(userId);
        if (energeticTalent && typeof energeticTalent !== "boolean") {
            let decrement = 0;
            if (energeticTalent.level === 1) {
                decrement = 1;
            }
            if (energeticTalent.level === 2) {
                decrement = 3;
            }
            if (energeticTalent.level === 3) {
                decrement = 5;
            }
            updateValues.maxActionPoints = { decrement };
        }

        // Handle built talent effects
        const builtTalent = await UserHasBuiltTalent(userId);
        if (builtTalent && typeof builtTalent !== "boolean" && builtTalent.modifier) {
            const decrement = builtTalent.modifier;
            updateValues.health = { decrement };
            updateValues.currentHealth = { decrement };
        }

        // Delete all user talents
        await talentsRepository.deleteAllUserTalents(userId);

        // Save user changes
        await UserService.updateUser(currentUser.id, updateValues);

        logger.debug(`Talents reset for user with ID: ${userId}`);
    } catch (error) {
        LogErrorStack({ message: "Failed to reset user talents", error });
    }
};

/**
 * Checks if the healthy caster talent is active for the user based on their current health.
 * @param  user - The user object.
 * @returns  - True if the talent is active, false otherwise.
 */
export const HealthyCasterTalentActiveForUser = async (user: BattlePlayer) => {
    const healthyCasterTalent = await UserHasHealthyCasterTalent(user.id);
    if (healthyCasterTalent && typeof healthyCasterTalent !== "boolean" && healthyCasterTalent.modifier) {
        const userPercentHpRemaining = (user.currentHealth / user.maxHealth) * 100;
        return userPercentHpRemaining >= healthyCasterTalent.modifier;
    }
    return false;
};

export const GetEquippedAbilities = async (user: ExtUserModel) => {
    try {
        const userEquipped = await talentsRepository.findUserEquippedAbilities(user.id);
        if (!userEquipped) {
            return [];
        }

        const equippedAbilities = [];

        if (userEquipped?.equippedAbility1Id) {
            equippedAbilities.push(userEquipped.equippedAbility1Id);
        }
        if (userEquipped?.equippedAbility2Id) {
            equippedAbilities.push(userEquipped.equippedAbility2Id);
        }
        if (userEquipped?.equippedAbility3Id) {
            equippedAbilities.push(userEquipped.equippedAbility3Id);
        }
        if (userEquipped?.equippedAbility4Id) {
            equippedAbilities.push(userEquipped.equippedAbility4Id);
        }

        if (equippedAbilities.length === 0) {
            return [];
        }

        // Get talent details and user levels for each equipped ability
        const abilityPromises = equippedAbilities.map(async (abilityId) => {
            const talent = await GetTalentById(abilityId);
            if (!talent) return null;

            // Get user's level in this talent
            const userTalent = await talentsRepository.findUserTalent(user.id, abilityId);
            const userLevel = userTalent?.level || 1; // Default to level 1 if not found

            // Calculate current modifier based on user's level
            const currentModifier = GetTalentModifierByLevel(talent, userLevel);

            return {
                name: talent.name,
                staminaCost: talent.staminaCost || 0,
                currentModifier,
                secondaryModifier: talent.secondaryModifier,
            };
        });

        const abilities = await Promise.all(abilityPromises);
        return abilities.filter((ability) => ability !== null);
    } catch (error) {
        LogErrorStack({ message: "Failed to fetch equipped abilities", error });
        return [];
    }
};
