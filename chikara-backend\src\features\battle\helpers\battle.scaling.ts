import gameConfig from "../../../config/gameConfig.js";

/**
 * These constants are the new balancing knobs for your game.
 * They define how much "effective power" a character gets per stat level,
 */

// --- Resource Pool Scaling (Stamina) ---
// Stamina governs the combat skill resource pool.

// BASE_STAMINA is the skill resource a character has at Level 1 Endurance.
// STAMINA_PER_ENDURANCE_LEVEL determines how much skill resource is gained.
// A level 100 Endurance character will have 100 + (100 * 5) = 600 max Stamina for skills.

// Stamina regeneration scaling - each intelligence level increases regen rate
// At level 1: 100% base regen, at level 100: 149% base regen (1.49x)
export const STAMINA_REGEN_PER_INTELLIGENCE_LEVEL = 0.005; // 0.5% increase per level

/**
 * Calculate stamina regeneration modifier based on intelligence level
 * @param intelligenceLevel - The character's intelligence level (1-100)
 * @returns Multiplier for base stamina regeneration (1.0 = 100%, 1.49 = 149% at level 100)
 */
export const getStaminaRegenModifier = (intelligenceLevel: number): number => {
    // Start at 100% for level 1, increase by 0.5% per level
    return 1 + (intelligenceLevel - 1) * STAMINA_REGEN_PER_INTELLIGENCE_LEVEL;
};

export const getEffectiveMaxStamina = (enduranceLevel: number): number => {
    return gameConfig.BASE_STAMINA + enduranceLevel * gameConfig.STAMINA_PER_ENDURANCE_LEVEL;
};

// --- Secondary Stat Effect Scaling ---

/**
 * Calculate critical hit chance from dexterity level
 * @param dexterityLevel - The character's dexterity level (1-100)
 * @returns Critical hit chance as a decimal (0.0 = 0%, 0.3 = 30% at level 100)
 */
export const getCriticalHitChance = (dexterityLevel: number): number => {
    // 0.3% critical hit chance per level, capped at 30% at level 100
    return Math.min((dexterityLevel - 1) * 0.003, 0.3);
};

/**
 * Calculate evasion chance from endurance level
 * @param enduranceLevel - The character's endurance level (1-100)
 * @returns Evasion chance as a decimal (0.0 = 0%, 0.25 = 25% at level 100)
 */
export const getEvasionChance = (enduranceLevel: number): number => {
    // 0.25% evasion chance per level, capped at 25% at level 100
    return Math.min((enduranceLevel - 1) * 0.0025, 0.25);
};

/**
 * Calculate armor penetration from strength level
 * @param strengthLevel - The character's strength level (1-100)
 * @returns Armor penetration as a decimal (0.0 = 0%, 0.2 = 20% at level 100)
 */
export const getArmorPenetration = (strengthLevel: number): number => {
    // 0.2% armor penetration per level, capped at 20% at level 100
    return Math.min((strengthLevel - 1) * 0.002, 0.2);
};

/**
 * Calculate unavoidable force (anti-evasion) from strength level
 * @param strengthLevel - The character's strength level (1-100)
 * @returns Anti-evasion multiplier (1.0 = no reduction, 0.5 = 50% evasion reduction at level 100)
 */
export const getUnavoidableForce = (strengthLevel: number): number => {
    // Reduces opponent's evasion by up to 50% at level 100
    return Math.max(1 - (strengthLevel - 1) * 0.005, 0.5);
};

/**
 * Calculate DoT resistance from defence level
 * @param defenceLevel - The character's defence level (1-100)
 * @returns DoT resistance as a decimal (0.0 = 0%, 0.4 = 40% at level 100)
 */
export const getDotResistance = (defenceLevel: number): number => {
    // 0.4% DoT resistance per level, capped at 40% at level 100
    return Math.min((defenceLevel - 1) * 0.004, 0.4);
};

/**
 * Calculate impact resistance (CC duration reduction) from intelligence level
 * @param intelligenceLevel - The character's intelligence level (1-100)
 * @returns Impact resistance as a decimal (0.0 = 0%, 0.3 = 30% at level 100)
 */
export const getImpactResistance = (intelligenceLevel: number): number => {
    // 0.3% impact resistance per level, capped at 30% at level 100
    return Math.min((intelligenceLevel - 1) * 0.003, 0.3);
};

/**
 * Calculate initiative modifier from vitality level
 * @param vitalityLevel - The character's vitality level (1-100)
 * @returns Initiative modifier as a decimal (0.0 = 0%, 0.2 = 20% at level 100)
 */
export const getInitiativeModifier = (vitalityLevel: number): number => {
    // 0.2% initiative bonus per level, capped at 20% at level 100
    return Math.min((vitalityLevel - 1) * 0.002, 0.2);
};

/**
 * Calculate healing improvement from vitality level
 * @param vitalityLevel - The character's vitality level (1-100)
 * @returns Healing improvement multiplier (1.0 = 100%, 1.3 = 130% at level 100)
 */
export const getHealingImprovement = (vitalityLevel: number): number => {
    // 0.3% healing improvement per level, capped at 30% improvement at level 100
    return 1 + Math.min((vitalityLevel - 1) * 0.003, 0.3);
};

// DAMAGE SYSTEM: Weapon damage is base damage, stats provide percentage increases
// A level 100 Str/Dex character will have +495% damage (5.95x weapon damage)

/**
 * Calculate percentage modifier from strength level for melee damage
 * @param strengthLevel - The character's strength level (1-100)
 * @returns Percentage modifier (1.0 = 100%, 1.05 = 105% damage)
 */
export const getStrengthPercentageModifier = (strengthLevel: number): number => {
    // 5% increase per level, starting at 100% for level 1
    return 1 + (strengthLevel - 1) * 0.05;
};

/**
 * Calculate percentage modifier from dexterity level for ranged damage
 * @param dexterityLevel - The character's dexterity level (1-100)
 * @returns Percentage modifier (1.0 = 100%, 1.05 = 105% damage)
 */
export const getDexterityPercentageModifier = (dexterityLevel: number): number => {
    // 5% increase per level, starting at 100% for level 1
    return 1 + (dexterityLevel - 1) * 0.05;
};

// DEFENCE SYSTEM: Mix of percentage reduction and flat reduction
// A level 100 Defence character will have 48% damage reduction + 99 flat reduction

/**
 * Calculate percentage damage reduction from defence level
 * @param defenceLevel - The character's defence level (1-100)
 * @returns Percentage damage reduction (0.0 = 0%, 0.48 = 48% reduction)
 */
export const getDefencePercentageReduction = (defenceLevel: number): number => {
    // 1.5% damage reduction per level, capped at 60% reduction at level 40+
    const percentageReduction = (defenceLevel - 1) * 0.015;
    return Math.min(percentageReduction, 0.6); // Cap at 60% reduction
};

/**
 * Calculate flat damage reduction from defence level
 * @param defenceLevel - The character's defence level (1-100)
 * @returns Flat damage reduction amount
 */
export const getDefenceFlatReduction = (defenceLevel: number): number => {
    // 1 flat damage reduction per level, starting from level 1
    return defenceLevel - 1;
};
