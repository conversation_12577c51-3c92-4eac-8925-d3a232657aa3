import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from "../chat/chat.helpers.js";
import LeaderboardRepository from "../../repositories/leaderboard.repository.js";
import { createCache } from "../../utils/cacheHelper.js";
import { logger } from "../../utils/log.js";
import gameConfig from "../../config/gameConfig.js";

interface BaseLeaderboardEntry {
    id: number;
    username: string;
    avatar: string | null;
    [key: string]: unknown;
}

interface LevelLeaderboardEntry extends BaseLeaderboardEntry {
    level: number;
}

interface AchievementLeaderboardEntry extends BaseLeaderboardEntry {
    user_achievements: {
        battleWins?: number | null;
        roguelikeMapsCompleted?: number | null;
        craftsCompleted?: number | null;
        npcBattleWins?: number | null;
        questsCompleted?: number | null;
        totalMuggingGain?: number | null;
        totalMuggingLoss?: number | null;
        totalCasinoProfitLoss?: number | null;
        totalMissionHours?: number | null;
        totalBountyRewards?: number | null;
        marketItemsSold?: number | null;
        marketMoneyMade?: number | null;
    } | null;
}

interface ProcessedTotalStatsEntry {
    id: number;
    username: string;
    avatar: string | null;
}

interface LeaderboardData {
    level: LevelLeaderboardEntry[];
    pvpwins: AchievementLeaderboardEntry[];
    money: BaseLeaderboardEntry[];
    hench: BaseLeaderboardEntry[];
    defensive: BaseLeaderboardEntry[];
    zones: AchievementLeaderboardEntry[];
    joblevel: BaseLeaderboardEntry[];
    crafts: AchievementLeaderboardEntry[];
    npcwins: AchievementLeaderboardEntry[];
    quests: AchievementLeaderboardEntry[];
    intelligent: BaseLeaderboardEntry[];
    dexterous: BaseLeaderboardEntry[];
    endurance: BaseLeaderboardEntry[];
    vitality: BaseLeaderboardEntry[];
    muggingGain: AchievementLeaderboardEntry[];
    muggingLoss: AchievementLeaderboardEntry[];
    casinoWinner: AchievementLeaderboardEntry[];
    casinoLoser: AchievementLeaderboardEntry[];
    totalMissionHours: AchievementLeaderboardEntry[];
    totalStats: ProcessedTotalStatsEntry[];
    totalBountyRewards: AchievementLeaderboardEntry[];
    marketItemsSold: AchievementLeaderboardEntry[];
    marketMoneyMade: AchievementLeaderboardEntry[];
}

interface LeaderboardResponse {
    data: LeaderboardData;
    lastFetch: number;
}

const leaderboardCache = createCache<LeaderboardResponse>("2h");

export const getLeaderBoards = async () => {
    if (leaderboardCache.isValid()) {
        const cachedData = leaderboardCache.get();
        if (cachedData) {
            return cachedData.data;
        }
    }

    const [
        level,
        pvpwins,
        money,
        hench,
        defensive,
        intelligent,
        dexterous,
        endurance,
        vitality,
        zones,
        joblevel,
        crafts,
        npcwins,
        quests,
        muggingGain,
        muggingLoss,
        casinoWinner,
        casinoLoser,
        totalMissionHours,
        totalStats,
        totalBountyRewards,
        marketItemsSold,
        marketMoneyMade,
    ] = await Promise.all([
        LeaderboardRepository.getLevelLeaderboard(),
        LeaderboardRepository.getPvpWinsLeaderboard(),
        LeaderboardRepository.getMoneyLeaderboard(),
        LeaderboardRepository.getStrengthLeaderboard(),
        LeaderboardRepository.getDefenceLeaderboard(),
        LeaderboardRepository.getIntelligenceLeaderboard(),
        LeaderboardRepository.getDexterityLeaderboard(),
        LeaderboardRepository.getEnduranceLeaderboard(),
        LeaderboardRepository.getVitalityLeaderboard(),
        LeaderboardRepository.getZonesLeaderboard(),
        LeaderboardRepository.getJobLevelLeaderboard(),
        LeaderboardRepository.getCraftsLeaderboard(),
        LeaderboardRepository.getNpcWinsLeaderboard(),
        LeaderboardRepository.getQuestsLeaderboard(),
        LeaderboardRepository.getMuggingGainLeaderboard(),
        LeaderboardRepository.getMuggingLossLeaderboard(),
        LeaderboardRepository.getCasinoWinnerLeaderboard(),
        LeaderboardRepository.getCasinoLoserLeaderboard(),
        LeaderboardRepository.getTotalMissionHoursLeaderboard(),
        LeaderboardRepository.getTotalStatsLeaderboard(),
        LeaderboardRepository.getTotalBountyRewardsLeaderboard(),
        LeaderboardRepository.getMarketItemsSoldLeaderboard(),
        LeaderboardRepository.getMarketMoneyMadeLeaderboard(),
    ]);

    // Post-process totalStats to sort by total stats sum
    const processedTotalStats = totalStats
        .map((user) => ({
            ...user,
            totalStatsSum:
                user.strength + user.defence + user.dexterity + user.intelligence + user.endurance + user.vitality,
        }))
        .sort((a, b) => b.totalStatsSum - a.totalStatsSum)
        .slice(0, gameConfig.USERS_PER_BOARD) // Limit to USERS_PER_BOARD after sorting
        .map(({ id, username, avatar }) => ({ id, username, avatar }));

    const leaderboardData = {
        level,
        pvpwins,
        money,
        hench,
        defensive,
        zones,
        joblevel,
        crafts,
        npcwins,
        quests,
        intelligent,
        dexterous,
        endurance,
        vitality,
        muggingGain,
        muggingLoss,
        casinoWinner,
        casinoLoser,
        totalMissionHours,
        totalStats: processedTotalStats,
        totalBountyRewards,
        marketItemsSold,
        marketMoneyMade,
    };

    const response = { data: leaderboardData, lastFetch: Date.now() };
    leaderboardCache.set(response);
    logger.info("Leaderboard cache updated");

    return response;
};

type EmoteRanking = Awaited<ReturnType<typeof ChatHelper.GetEmoteUsageRanking>>;
interface ChatEmoteLeaderboardResponse {
    data: EmoteRanking;
}
const chatEmoteLeaderboardCache = createCache<ChatEmoteLeaderboardResponse>("2h");

export const getChatEmoteLeaderboards = async () => {
    if (chatEmoteLeaderboardCache.isValid()) {
        const cachedData = chatEmoteLeaderboardCache.get();
        if (cachedData) {
            return cachedData.data;
        }
    }

    const emoteRanking = await ChatHelper.GetEmoteUsageRanking();

    const response = { data: emoteRanking };
    chatEmoteLeaderboardCache.set(response);
    logger.info("Chat emote leaderboard cache updated");

    return response;
};
