generator client {
  provider = "prisma-client-js"
}

generator json {
  provider = "prisma-json-types-generator"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model action_log {
  id            Int      @id @default(autoincrement())
  logType       String?  @db.VarChar(255)
  info          Json     @default("{}")
  createdAt     DateTime @default(now()) @db.DateTime(0)
  updatedAt     DateTime @updatedAt @db.DateTime(0)
  playerId      Int?
  secondPartyId Int?
  player        user?    @relation("actionsAsPlayer", fields: [playerId], references: [id])
  secondParty   user?    @relation("actionsAsSecondParty", fields: [secondPartyId], references: [id])

  @@index([playerId])
  @@index([secondPartyId])
}

model game_config {
  id          Int      @id @default(autoincrement())
  key         String   @unique @db.VarChar(255)
  value       Json
  category    String?  @db.VarChar(100)
  isPublic    Boolean  @default(true)
  createdAt   DateTime @default(now()) @db.DateTime(0)
  updatedAt   DateTime @updatedAt @db.DateTime(0)

  @@index([category])
  @@index([isPublic])
}

model auction_item {
  id          Int               @id @default(autoincrement())
  quantity    Int               @default(1) @db.UnsignedInt
  deposit     Int               @db.UnsignedInt
  buyoutPrice Int               @db.UnsignedInt
  endsAt      DateTime          @db.DateTime(0)
  status      AuctionItemStatus
  bankFunds   Boolean           @default(false)
  createdAt   DateTime          @default(now()) @db.DateTime(0)
  updatedAt   DateTime          @updatedAt @db.DateTime(0)
  itemId      Int?
  sellerId    Int?
  item        item?             @relation(fields: [itemId], references: [id])
  user        user?             @relation(fields: [sellerId], references: [id])

  @@index([itemId])
  @@index([sellerId])
}

model bank_transaction {
  id                     Int                  @id @default(autoincrement())
  transaction_type       BankTransactionTypes
  cash                   Int                  @db.UnsignedInt
  transactionFee         Int                  @db.UnsignedInt
  initiatorCashBalance   Int                  @db.UnsignedInt
  initiatorBankBalance   Int                  @db.UnsignedInt
  secondPartyCashBalance Int?                 @db.UnsignedInt
  secondPartyBankBalance Int?                 @db.UnsignedInt
  createdAt              DateTime             @default(now()) @db.DateTime(0)
  updatedAt              DateTime             @updatedAt @db.DateTime(0)
  initiatorId            Int?
  secondPartyId          Int?
  userId                 Int?
  initiator              user?                @relation("initiatedBankTransactions", fields: [initiatorId], references: [id])
  secondParty            user?                @relation("secondPartyBankTransactions", fields: [secondPartyId], references: [id])
  user                   user?                @relation("userBankTransactions", fields: [userId], references: [id])

  @@index([initiatorId])
  @@index([secondPartyId])
  @@index([userId])
}

model battle_log {
  id         Int      @id @default(autoincrement())
  victory    Boolean
  createdAt  DateTime @default(now()) @db.DateTime(0)
  updatedAt  DateTime @updatedAt @db.DateTime(0)
  attackerId Int?
  defenderId Int?
  attacker   user?    @relation("battlesAsAttacker", fields: [attackerId], references: [id])
  defender   user?    @relation("battlesAsDefender", fields: [defenderId], references: [id])

  @@index([attackerId])
  @@index([defenderId])
}

model bounty {
  id          Int      @id @default(autoincrement())
  amount      Int
  reason      String?  @db.VarChar(255)
  active      Boolean? @default(true)
  createdAt   DateTime @default(now()) @db.DateTime(0)
  updatedAt   DateTime @updatedAt @db.DateTime(0)
  placerId    Int?
  targetId    Int?
  claimedById Int?
  placer      user?    @relation("placedBounties", fields: [placerId], references: [id])
  target      user?    @relation("targetedBounties", fields: [targetId], references: [id])
  claimer     user?    @relation("claimedBounties", fields: [claimedById], references: [id])

  @@index([claimedById])
  @@index([placerId])
  @@index([targetId])
}

model chat_message {
  id               Int            @id @default(autoincrement())
  message          String         @db.VarChar(255)
  hidden           Boolean?       @default(false)
  announcementType String?        @db.VarChar(50)
  createdAt        DateTime       @default(now()) @db.DateTime(0)
  updatedAt        DateTime       @updatedAt @db.DateTime(0)
  chatRoomId       Int?
  userId           Int?
  parentMessageId  Int?
  chat_room        chat_room?     @relation(fields: [chatRoomId], references: [id])
  user             user?          @relation(fields: [userId], references: [id])
  chat_message     chat_message?  @relation("chat_messageTochat_message", fields: [parentMessageId], references: [id])
  parent_message   chat_message[] @relation("chat_messageTochat_message")

  @@index([chatRoomId])
  @@index([parentMessageId])
  @@index([userId])
}

model chat_room {
  id           Int            @id @default(autoincrement())
  name         String         @db.VarChar(255)
  gangId       Int?
  chat_message chat_message[]
  gang         gang?          @relation(fields: [gangId], references: [id])

  @@index([gangId])
}

model crafting_recipe {
  id                  Int                   @id @default(autoincrement())
  cost                Int
  craftTime           Int                   @db.UnsignedInt
  isUnlockable        Boolean               @default(false)
  requiredSkillType   CraftingSkills?
  requiredSkillLevel  Int                   @default(0) @db.UnsignedInt
  item                item[]
  recipe_item         recipe_item[]
  user_crafting_queue user_crafting_queue[]
  user_recipe         user_recipe[]
}

model creature {
  id              Int               @id @default(autoincrement())
  name            String            @db.VarChar(255)
  image           String            @db.VarChar(255)
  minFloor        Int               @default(0)
  maxFloor        Int               @default(99999)
  boss            Boolean
  health          Int               @default(1)
  currentHealth   Int?
  strength        Int               @default(1)
  defence         Int               @default(1)
  weaponDamage    Int               @default(1)
  location        LocationTypes?
  statType        CreatureStatTypes
  createdAt       DateTime          @default(now()) @db.DateTime(0)
  updatedAt       DateTime          @updatedAt @db.DateTime(0)
  quest_objective quest_objective[]
}

model daily_mission {
  id                 Int      @id @default(autoincrement())
  tier               Int
  missionName        String   @db.VarChar(255)
  description        String   @db.Text
  missionDate        DateTime @db.Date
  duration           Int      @db.UnsignedInt
  minCashReward      Int      @default(0) @db.UnsignedInt
  maxCashReward      Int      @default(0) @db.UnsignedInt
  minExpReward       Int      @default(0) @db.UnsignedInt
  maxExpReward       Int      @default(0) @db.UnsignedInt
  levelReq           Int      @default(0) @db.UnsignedInt
  hoursReq           Int      @default(0) @db.UnsignedInt
  itemRewardQuantity Int?     @db.UnsignedInt
  createdAt          DateTime @default(now()) @db.DateTime(0)
  updatedAt          DateTime @updatedAt @db.DateTime(0)
  itemRewardId       Int?
  item               item?    @relation(fields: [itemRewardId], references: [id])

  @@index([itemRewardId])
}

model drop_chance {
  id             Int             @id @default(autoincrement())
  dropRate       Float
  dropChanceType DropChanceTypes
  quantity       Int             @default(1) @db.UnsignedInt
  location       LocationTypes?
  minLevel       Int?
  maxLevel       Int?
  scavengeType   String?         @db.VarChar(255)
  createdAt      DateTime        @default(now()) @db.DateTime(0)
  updatedAt      DateTime        @updatedAt @db.DateTime(0)
  itemId         Int?
  item           item?           @relation(fields: [itemId], references: [id])

  @@index([itemId])
  @@index([dropChanceType, location, minLevel, maxLevel])
}

model equipped_item {
  id         Int        @id @default(autoincrement())
  createdAt  DateTime   @default(now()) @db.DateTime(0)
  updatedAt  DateTime   @updatedAt @db.DateTime(0)
  userId     Int?
  slot       EquipSlots
  userItemId Int?
  user       user?      @relation(fields: [userId], references: [id])
  user_item  user_item? @relation(fields: [userItemId], references: [id])

  @@unique([userId, slot])
  @@index([userItemId])
  @@index([userId])
}

model game_stats {
  id         Int      @id @default(autoincrement())
  stats_type String   @db.VarChar(255)
  info       String   @db.VarChar(2000)
  createdAt  DateTime @default(now()) @db.DateTime(0)
  updatedAt  DateTime @updatedAt @db.DateTime(0)
  playerId   Int?
  user       user?    @relation(fields: [playerId], references: [id])

  @@index([playerId])
}

model gang {
  id                 Int           @id @default(autoincrement())
  name               String        @db.VarChar(255)
  about              String?       @db.Text
  avatar             String?       @db.VarChar(255)
  treasury_balance   Int?          @default(0) @db.UnsignedInt
  hideout_level      Int?          @default(0) @db.UnsignedInt
  materialsResource  Int?          @default(0) @db.UnsignedInt
  essenceResource    Int?          @default(0) @db.UnsignedInt
  dailyEssenceGained Int?          @default(0) @db.UnsignedInt
  toolsResource      Int?          @default(0) @db.UnsignedInt
  techResource       Int?          @default(0) @db.UnsignedInt
  weeklyRespect      Int?          @default(0) @db.UnsignedInt
  totalRespect       Int?          @default(0) @db.UnsignedInt
  gangMOTD           String?       @db.Text
  createdAt          DateTime      @default(now()) @db.DateTime(0)
  updatedAt          DateTime      @updatedAt @db.DateTime(0)
  ownerId            Int
  chat_room          chat_room[]
  user               user?         @relation("gangOwner", fields: [ownerId], references: [id])
  gang_invite        gang_invite[]
  gang_log           gang_log[]
  gang_member        gang_member[]
  members            user[]        @relation("gangMembers")

  @@index([ownerId])
}

model gang_invite {
  id                                 Int             @id @default(autoincrement())
  inviteType                         GangInviteTypes
  createdAt                          DateTime        @default(now()) @db.DateTime(0)
  updatedAt                          DateTime        @updatedAt @db.DateTime(0)
  gangId                             Int?
  senderId                           Int?
  recipientId                        Int?
  gang                               gang?           @relation(fields: [gangId], references: [id])
  user_gang_invite_senderIdTouser    user?           @relation("gang_invite_senderIdTouser", fields: [senderId], references: [id])
  user_gang_invite_recipientIdTouser user?           @relation("gang_invite_recipientIdTouser", fields: [recipientId], references: [id])

  @@index([gangId])
  @@index([recipientId])
  @@index([senderId])
}

model gang_log {
  id                                Int      @id @default(autoincrement())
  action                            String?  @db.VarChar(255)
  info                              String   @db.Text
  createdAt                         DateTime @default(now()) @db.DateTime(0)
  updatedAt                         DateTime @updatedAt @db.DateTime(0)
  gangId                            Int?
  gangMemberId                      Int?
  secondPartyId                     Int?
  gang                              gang?    @relation(fields: [gangId], references: [id])
  user_gang_log_gangMemberIdTouser  user?    @relation("gang_log_gangMemberIdTouser", fields: [gangMemberId], references: [id])
  user_gang_log_secondPartyIdTouser user?    @relation("gang_log_secondPartyIdTouser", fields: [secondPartyId], references: [id])

  @@index([gangId])
  @@index([gangMemberId])
  @@index([secondPartyId])
}

model gang_member {
  id                Int      @id @default(autoincrement())
  rank              Int      @default(0) @db.UnsignedInt
  payoutShare       Float    @default(0) @db.Float
  weeklyMaterials   Int?     @default(0) @db.UnsignedInt
  weeklyEssence     Int?     @default(0) @db.UnsignedInt
  weeklyTools       Int?     @default(0) @db.UnsignedInt
  weeklyRespect     Int?     @default(0) @db.UnsignedInt
  totalContribution Int?     @default(0) @db.UnsignedInt
  createdAt         DateTime @default(now()) @db.DateTime(0)
  updatedAt         DateTime @updatedAt @db.DateTime(0)
  gangId            Int?
  userId            Int?
  gang              gang?    @relation(fields: [gangId], references: [id])
  user              user?    @relation(fields: [userId], references: [id])

  @@index([gangId])
  @@index([userId])
}

model item {
  id           Int          @id @default(autoincrement())
  name         String       @db.Text
  itemType     ItemTypes
  rarity       ItemRarities
  level        Int          @default(1)
  about        String?      @db.Text
  cashValue    Int?         @default(0)
  image        String?      @db.VarChar(255)
  damage       Int?         @default(0)
  armour       Int?         @default(0)
  health       Int?         @default(0)
  energy       Int?         @default(0)
  actionPoints Int?         @default(0)
  baseAmmo     Int?         @default(0)

  /// [ItemEffects]
  itemEffects Json?

  createdAt       DateTime          @default(now()) @db.DateTime(0)
  updatedAt       DateTime          @updatedAt @db.DateTime(0)
  recipeUnlockId  Int?              @unique
  petUnlockId     Int?              @unique
  auction_item    auction_item[]
  daily_mission   daily_mission[]
  drop_chance     drop_chance[]
  crafting_recipe crafting_recipe?  @relation(fields: [recipeUnlockId], references: [id])
  pet             pet?              @relation(fields: [petUnlockId], references: [id])
  recipe_item     recipe_item[]
  shop_listing    shop_listing[]
  user_item       user_item[]
  quest_reward    quest_reward[]
  daily_quest     daily_quest[]
  quest_objective quest_objective[]

  @@index([recipeUnlockId])
}

model job {
  id                  Int      @id @default(autoincrement())
  name                String   @db.VarChar(255)
  description         String?  @db.VarChar(255)
  avatar              String?  @db.VarChar(255)
  payFormula          String?  @db.VarChar(255)
  strengthFormula     String?  @db.VarChar(255)
  intelligenceFormula String?  @db.VarChar(255)
  dexterityFormula    String?  @db.VarChar(255)
  defenceFormula      String?  @db.VarChar(255)
  enduranceFormula    String?  @db.VarChar(255)
  vitalityFormula     String?  @db.VarChar(255)
  createdAt           DateTime @default(now()) @db.DateTime(0)
  updatedAt           DateTime @updatedAt @db.DateTime(0)
  user                user[]
}

model lottery {
  id            Int             @id @default(autoincrement())
  drawDate      DateTime        @unique(map: "drawDate") @db.DateTime(0)
  prizeAmount   Int             @default(0) @db.UnsignedInt
  entries       Int             @default(0) @db.UnsignedInt
  createdAt     DateTime        @default(now()) @db.DateTime(0)
  updatedAt     DateTime        @updatedAt @db.DateTime(0)
  winnerId      Int?
  user          user?           @relation(fields: [winnerId], references: [id])
  lottery_entry lottery_entry[]

  @@index([winnerId])
}

model lottery_entry {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)
  lotteryId Int?
  userId    Int?
  lottery   lottery? @relation(fields: [lotteryId], references: [id])
  user      user?    @relation(fields: [userId], references: [id])

  @@index([lotteryId])
  @@index([userId])
}

model notification {
  id               Int      @id @default(autoincrement())
  notificationType String   @db.VarChar(100)
  details          String?  @db.Text
  read             Boolean?
  createdAt        DateTime @default(now()) @db.DateTime(0)
  updatedAt        DateTime @updatedAt @db.DateTime(0)
  userId           Int?
  user             user?    @relation(fields: [userId], references: [id])

  @@index([userId])
}

model poll {
  id            Int             @id @default(autoincrement())
  title         String          @db.VarChar(255)
  ended         Boolean         @default(false)
  showResults   Boolean         @default(false)
  createdAt     DateTime        @default(now()) @db.DateTime(0)
  updatedAt     DateTime        @updatedAt @db.DateTime(0)
  poll_response poll_response[]
}

model poll_response {
  id        Int      @id @default(autoincrement())
  answer    Json
  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)
  userId    Int?
  pollId    Int?
  user      user?    @relation(fields: [userId], references: [id])
  poll      poll?    @relation(fields: [pollId], references: [id])

  @@index([pollId])
  @@index([userId])
}

model private_message {
  id                                    Int      @id @default(autoincrement())
  message                               String   @db.Text
  read                                  Boolean?
  isGlobal                              Boolean? @default(false)
  createdAt                             DateTime @default(now()) @db.DateTime(0)
  updatedAt                             DateTime @updatedAt @db.DateTime(0)
  senderId                              Int?
  receiverId                            Int?
  user_private_message_senderIdTouser   user?    @relation("private_message_senderIdTouser", fields: [senderId], references: [id])
  user_private_message_receiverIdTouser user?    @relation("private_message_receiverIdTouser", fields: [receiverId], references: [id])

  @@index([receiverId])
  @@index([senderId])
}

model profile_comment {
  id                                    Int      @id @default(autoincrement())
  message                               String   @db.VarChar(255)
  createdAt                             DateTime @default(now()) @db.DateTime(0)
  updatedAt                             DateTime @updatedAt @db.DateTime(0)
  senderId                              Int?
  receiverId                            Int?
  user_profile_comment_senderIdTouser   user?    @relation("profile_comment_senderIdTouser", fields: [senderId], references: [id])
  user_profile_comment_receiverIdTouser user?    @relation("profile_comment_receiverIdTouser", fields: [receiverId], references: [id])

  @@index([receiverId])
  @@index([senderId])
}

model push_token {
  id        Int      @id @default(autoincrement())
  token     String   @unique(map: "token") @db.VarChar(255)
  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)
  userId    Int?
  user      user?    @relation(fields: [userId], references: [id])

  @@index([userId])
}

model daily_quest {
  id                 Int                 @id @default(autoincrement())
  objectiveType      QuestObjectiveTypes
  target             Int?                @db.UnsignedInt
  targetAction       String?             @db.VarChar(255)
  quantity           Int?                @db.UnsignedInt
  questStatus        QuestProgressStatus @default(in_progress)
  count              Int                 @default(0) @db.UnsignedInt
  cashReward         Int                 @default(0) @db.UnsignedInt
  xpReward           Int                 @default(0) @db.UnsignedInt
  itemRewardQuantity Int?                @db.UnsignedInt
  createdAt          DateTime            @default(now()) @db.DateTime(0)
  updatedAt          DateTime            @updatedAt @db.DateTime(0)
  itemRewardId       Int?
  userId             Int?
  item               item?               @relation(fields: [itemRewardId], references: [id])
  user               user?               @relation(fields: [userId], references: [id])

  @@index([itemRewardId])
  @@index([userId])
}

model quest {
  id                Int      @id @default(autoincrement())
  name              String   @db.VarChar(255)
  description       String?  @db.VarChar(255)
  questInfo         String?  @db.VarChar(255)
  levelReq          Int      @default(0) @db.UnsignedInt
  disabled          Boolean? @default(false)
  questChainName    String?  @db.VarChar(255)
  xpReward          Int?     @default(1) @db.UnsignedInt
  cashReward        Int      @default(0) @db.UnsignedInt
  repReward         Float    @default(0) @db.Float
  talentPointReward Int      @default(0) @db.UnsignedInt
  shopId            Int?
  requiredQuestId   Int?

  isStoryQuest   Boolean        @default(false)
  chapterId      Int?
  story_chapter  story_chapter? @relation(fields: [chapterId], references: [id])
  orderInChapter Int?

  shop            shop?             @relation(fields: [shopId], references: [id])
  quest           quest?            @relation("questToquest", fields: [requiredQuestId], references: [id])
  other_quest     quest[]           @relation("questToquest")
  quest_progress  quest_progress[]
  quest_reward    quest_reward[]
  quest_objective quest_objective[]

  @@unique([chapterId, orderInChapter])
  @@index([requiredQuestId])
  @@index([shopId])
}

model quest_progress {
  id          Int                 @id @default(autoincrement())
  questStatus QuestProgressStatus @default(in_progress)
  createdAt   DateTime            @default(now()) @db.DateTime(0)
  updatedAt   DateTime            @updatedAt @db.DateTime(0)
  questId     Int?
  userId      Int?
  quest       quest?              @relation(fields: [questId], references: [id])
  user        user?               @relation(fields: [userId], references: [id])

  @@index([questId])
  @@index([userId])
}

model quest_objective {
  id                       Int                        @id @default(autoincrement())
  description              String?                    @db.VarChar(255)
  objectiveType            QuestObjectiveTypes
  target                   Int?                       @db.UnsignedInt
  targetAction             String?
  quantity                 Int?                       @db.UnsignedInt
  location                 LocationTypes?
  isRequired               Boolean                    @default(true)
  questId                  Int?
  creatureId               Int?
  itemId                   Int?
  quest                    quest?                     @relation(fields: [questId], references: [id], onDelete: Cascade)
  creature                 creature?                  @relation(fields: [creatureId], references: [id])
  item                     item?                      @relation(fields: [itemId], references: [id])
  quest_objective_progress quest_objective_progress[]
  story_episode            story_episode?

  @@index([questId])
  @@index([creatureId])
  @@index([itemId])
}

model quest_objective_progress {
  id               Int                 @id @default(autoincrement())
  count            Int                 @default(0) @db.UnsignedInt
  status           QuestProgressStatus @default(in_progress)
  userId           Int
  createdAt        DateTime            @default(now()) @db.DateTime(0)
  updatedAt        DateTime            @updatedAt @db.DateTime(0)
  questObjectiveId Int
  user             user                @relation(fields: [userId], references: [id], onDelete: Cascade)
  quest_objective  quest_objective     @relation(fields: [questObjectiveId], references: [id], onDelete: Cascade)

  @@unique([userId, questObjectiveId])
  @@index([userId])
  @@index([questObjectiveId])
}

model quest_reward {
  id           Int             @id @default(autoincrement())
  rewardType   QuestRewardType
  quantity     Int             @db.UnsignedInt
  isChoice     Boolean         @default(false)
  createdAt    DateTime        @default(now()) @db.DateTime(0)
  updatedAt    DateTime        @updatedAt @db.DateTime(0)
  questId      Int?
  dailyQuestId Int?
  itemId       Int?
  quest        quest?          @relation(fields: [questId], references: [id], onDelete: Cascade)
  item         item?           @relation(fields: [itemId], references: [id])

  @@index([questId])
  @@index([dailyQuestId])
  @@index([itemId])
}

model recipe_item {
  count            Int?
  itemType         RecipeItemTypes? @default(input)
  createdAt        DateTime         @default(now()) @db.DateTime(0)
  updatedAt        DateTime         @updatedAt @db.DateTime(0)
  craftingRecipeId Int
  itemId           Int
  crafting_recipe  crafting_recipe  @relation(fields: [craftingRecipeId], references: [id], onDelete: Cascade)
  item             item             @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@id([craftingRecipeId, itemId])
  @@index([itemId])
}

model registration_code {
  id                                      Int      @id @default(autoincrement())
  code                                    String   @db.VarChar(255)
  note                                    String   @db.VarChar(255)
  unlimitedUse                            Boolean? @default(false)
  createdAt                               DateTime @default(now()) @db.DateTime(0)
  updatedAt                               DateTime @updatedAt @db.DateTime(0)
  referrerId                              Int?
  claimerId                               Int?
  user_registration_code_referrerIdTouser user?    @relation("registration_code_referrerIdTouser", fields: [referrerId], references: [id])
  user_registration_code_claimerIdTouser  user?    @relation("registration_code_claimerIdTouser", fields: [claimerId], references: [id])

  @@index([claimerId])
  @@index([referrerId])
}

model shop {
  id                  Int                   @id @default(autoincrement())
  name                String                @db.VarChar(255)
  shopType            ShopTypes
  avatar              String?               @db.VarChar(255)
  description         String                @db.VarChar(255)
  disabled            Boolean?              @default(false)
  quest               quest[]
  shop_listing        shop_listing[]
  trader_rep          trader_rep[]
  explore_static_node explore_static_node[]
}

model shop_listing {
  id          Int                 @id @default(autoincrement())
  customCost  Int?                @db.UnsignedInt
  repRequired Int?                @db.UnsignedInt
  stock       Int?
  currency    ShopListingCurrency @default(yen)
  shopId      Int?
  itemId      Int?
  shop        shop?               @relation(fields: [shopId], references: [id])
  item        item?               @relation(fields: [itemId], references: [id])

  @@index([itemId])
  @@index([shopId])
}

model shrine_donation {
  id           Int          @id @default(autoincrement())
  amount       Int          @default(0) @db.UnsignedInt
  date         DateTime     @db.Date
  createdAt    DateTime     @default(now()) @db.DateTime(0)
  updatedAt    DateTime     @updatedAt @db.DateTime(0)
  shrineGoalId Int?
  userId       Int?
  shrine_goal  shrine_goal? @relation(fields: [shrineGoalId], references: [id])
  user         user?        @relation(fields: [userId], references: [id])

  @@index([shrineGoalId])
  @@index([userId])
}

model shrine_goal {
  id             Int      @id @default(autoincrement())
  donationGoal   Int      @db.UnsignedInt
  donationAmount Int      @default(0) @db.UnsignedInt
  goalReached    Boolean  @default(false)
  goalDate       DateTime @db.Date

  /// [ShrineBuffRewards]
  buffRewards Json

  createdAt       DateTime          @default(now()) @db.DateTime(0)
  updatedAt       DateTime          @updatedAt @db.DateTime(0)
  shrine_donation shrine_donation[]
}

model suggestion {
  id                 Int                  @id @default(autoincrement())
  title              String               @db.VarChar(255)
  content            String               @db.Text
  state              SuggestionStates?    @default(New)
  upvotes            Int?                 @default(0)
  downvotes          Int?                 @default(0)
  totalComments      Int?                 @default(0)
  createdAt          DateTime             @default(now()) @db.DateTime(0)
  updatedAt          DateTime             @updatedAt @db.DateTime(0)
  userId             Int?
  user               user?                @relation(fields: [userId], references: [id])
  suggestion_comment suggestion_comment[]
  suggestion_vote    suggestion_vote[]

  @@index([userId])
}

model suggestion_comment {
  id           Int         @id @default(autoincrement())
  message      String      @db.Text
  createdAt    DateTime    @default(now()) @db.DateTime(0)
  updatedAt    DateTime    @updatedAt @db.DateTime(0)
  userId       Int?
  suggestionId Int?
  user         user?       @relation(fields: [userId], references: [id])
  suggestion   suggestion? @relation(fields: [suggestionId], references: [id])

  @@index([suggestionId])
  @@index([userId])
}

model suggestion_vote {
  id           Int                 @id @default(autoincrement())
  voteType     SuggestionVoteTypes
  createdAt    DateTime            @default(now()) @db.DateTime(0)
  updatedAt    DateTime            @updatedAt @db.DateTime(0)
  userId       Int?
  suggestionId Int?
  user         user?               @relation(fields: [userId], references: [id])
  suggestion   suggestion?         @relation(fields: [suggestionId], references: [id])

  @@index([suggestionId])
  @@index([userId])
}

model talent {
  id                   Int                       @id @default(autoincrement())
  name                 String                    @db.VarChar(255)
  displayName          String                    @db.VarChar(255)
  tree                 TalentTree
  talentType           TalentType                @default(passive)
  description          String?                   @db.Text
  skillLevelRequired   Int                       @db.UnsignedInt
  pointsInTreeRequired Int                       @db.UnsignedInt
  pointsCost           Int                       @default(1) @db.UnsignedInt
  maxPoints            Int                       @db.UnsignedInt
  staminaCost          Int?                      @db.UnsignedInt
  tier1Modifier        Float?                    @db.Float
  tier2Modifier        Float?                    @db.Float
  tier3Modifier        Float?                    @db.Float
  secondaryModifier    Float?                    @db.Float
  usersWithAbility1    user_equipped_abilities[] @relation("userEquippedAbility1")
  usersWithAbility2    user_equipped_abilities[] @relation("userEquippedAbility2")
  usersWithAbility3    user_equipped_abilities[] @relation("userEquippedAbility3")
  usersWithAbility4    user_equipped_abilities[] @relation("userEquippedAbility4")
  user_talent          user_talent[]
}

model trader_rep {
  id              Int      @id @default(autoincrement())
  reputationLevel Float    @default(0) @db.Float
  createdAt       DateTime @default(now()) @db.DateTime(0)
  updatedAt       DateTime @updatedAt @db.DateTime(0)
  shopId          Int?
  userId          Int?
  shop            shop?    @relation(fields: [shopId], references: [id])
  user            user?    @relation(fields: [userId], references: [id])

  @@index([shopId])
  @@index([userId])
}

model user {
  id                 Int        @id @default(autoincrement())
  username           String     @db.VarChar(255)
  displayUsername    String     @db.VarChar(255)
  about              String?    @db.Text
  email              String     @db.VarChar(255)
  emailVerified      Boolean    @default(false)
  banned             Boolean?   @default(false)
  banReason          String?    @db.Text
  banExpires         DateTime?  @db.DateTime(0)
  usernameSet        Boolean?   @default(true)
  cash               Int        @default(100) @db.UnsignedInt
  bank_balance       Int        @default(200) @db.UnsignedInt
  last_activity      DateTime?  @db.DateTime(0)
  chatBannedUntil    BigInt?    @db.UnsignedBigInt
  userType           UserTypes? @default(student)
  avatar             String?    @db.VarChar(255)
  profileBanner      String?    @db.VarChar(255)
  jobLevel           Int?       @db.UnsignedInt
  jobPayoutHour      Int?       @db.UnsignedInt
  blockNextJobPayout Boolean    @default(false)

  /// [RoguelikeMapType]
  roguelikeMap Json?

  roguelikeLevel           Int                       @default(1) @db.UnsignedInt
  roguelikeHighscore       Int                       @default(1) @db.UnsignedInt
  jailedUntil              BigInt?                   @db.UnsignedBigInt
  jailReason               String?                   @db.VarChar(255)
  hospitalisedUntil        BigInt?                   @db.UnsignedBigInt
  hospitalisedHealingType  HospitalisedHealingTypes?
  hospitalisedReason       String?                   @db.VarChar(255)
  energy                   Int                       @default(100) @db.UnsignedInt
  lastEnergyTick           BigInt?                   @db.UnsignedBigInt
  focus                    Int                       @default(0) @db.UnsignedInt
  dailyFatigueUsed         Int                       @default(0) @db.UnsignedInt
  lastFatigueReset         DateTime?                 @db.Date
  level                    Int                       @default(1) @db.UnsignedInt
  xp                       Int                       @default(0) @db.UnsignedInt
  actionPoints             Int                       @default(10) @db.UnsignedInt
  nextAPTick               BigInt?                   @db.UnsignedBigInt
  maxActionPoints          Int                       @default(10) @db.UnsignedInt
  currentHealth            Int                       @default(200) @db.UnsignedInt
  nextHPTick               BigInt?                   @db.UnsignedBigInt
  health                   Int                       @default(200) @db.UnsignedInt
  talentPoints             Int                       @default(0) @db.UnsignedInt
  activeCourseId           Int?
  courseEnds               BigInt?                   @db.UnsignedBigInt
  class                    String?                   @db.VarChar(255)
  classPoints              Int                       @default(0) @db.UnsignedInt
  adminNotes               String?                   @db.VarChar(255)
  combatLevel              Int                       @default(1) @db.UnsignedInt
  discordID                String?                   @unique(map: "discordID") @db.VarChar(255)
  currentMission           Int?
  missionEnds              BigInt?                   @db.UnsignedBigInt
  profileDetailBanUntil    BigInt?                   @db.UnsignedBigInt
  weeklyBuyLimitRemaining  Int                       @default(4) @db.UnsignedInt
  dailyQuestsRewardClaimed DateTime?                 @db.Date
  currentMapLocation       ExploreNodeLocation?      @default(shibuya)

  // Travel-related fields
  travelStartTime DateTime?     @db.DateTime(0)
  travelEndTime   DateTime?     @db.DateTime(0)
  travelMethod    TravelMethod?

  /// [RooftopDefeatedNpcs]
  defeatedNpcs Json?

  pushNotificationsEnabled                             Boolean                    @default(true)
  gangCreds                                            Int                        @default(0) @db.UnsignedInt
  lastNewsIDRead                                       Int                        @default(0) @db.UnsignedInt
  createdAt                                            DateTime                   @default(now()) @db.DateTime(0)
  updatedAt                                            DateTime                   @updatedAt @db.DateTime(0)
  gangId                                               Int?
  jobId                                                Int?
  referrerId                                           Int?
  accounts                                             account[]
  actionsAsPlayer                                      action_log[]               @relation("actionsAsPlayer")
  actionsAsSecondParty                                 action_log[]               @relation("actionsAsSecondParty")
  auction_item                                         auction_item[]
  initiatedBankTransactions                            bank_transaction[]         @relation("initiatedBankTransactions")
  secondPartyBankTransactions                          bank_transaction[]         @relation("secondPartyBankTransactions")
  userBankTransactions                                 bank_transaction[]         @relation("userBankTransactions")
  battlesAsAttacker                                    battle_log[]               @relation("battlesAsAttacker")
  battlesAsDefender                                    battle_log[]               @relation("battlesAsDefender")
  placedBounties                                       bounty[]                   @relation("placedBounties")
  targetedBounties                                     bounty[]                   @relation("targetedBounties")
  claimedBounties                                      bounty[]                   @relation("claimedBounties")
  chat_message                                         chat_message[]
  daily_quest                                          daily_quest[]
  equipped_item                                        equipped_item[]
  game_stats                                           game_stats[]
  gang                                                 gang?                      @relation("gangMembers", fields: [gangId], references: [id])
  gang_invite_gang_invite_senderIdTouser               gang_invite[]              @relation("gang_invite_senderIdTouser")
  gang_invite_gang_invite_recipientIdTouser            gang_invite[]              @relation("gang_invite_recipientIdTouser")
  gang_log_gang_log_gangMemberIdTouser                 gang_log[]                 @relation("gang_log_gangMemberIdTouser")
  gang_log_gang_log_secondPartyIdTouser                gang_log[]                 @relation("gang_log_secondPartyIdTouser")
  gang_member                                          gang_member[]
  lottery                                              lottery[]
  lottery_entry                                        lottery_entry[]
  notification                                         notification[]
  poll_response                                        poll_response[]
  private_message_private_message_senderIdTouser       private_message[]          @relation("private_message_senderIdTouser")
  private_message_private_message_receiverIdTouser     private_message[]          @relation("private_message_receiverIdTouser")
  profile_comment_profile_comment_senderIdTouser       profile_comment[]          @relation("profile_comment_senderIdTouser")
  profile_comment_profile_comment_receiverIdTouser     profile_comment[]          @relation("profile_comment_receiverIdTouser")
  push_token                                           push_token[]
  quest_progress                                       quest_progress[]
  quest_objective_progress                             quest_objective_progress[]
  registration_code_registration_code_referrerIdTouser registration_code[]        @relation("registration_code_referrerIdTouser")
  registration_code_registration_code_claimerIdTouser  registration_code[]        @relation("registration_code_claimerIdTouser")
  session                                              session[]
  shrine_donation                                      shrine_donation[]
  suggestion                                           suggestion[]
  suggestion_comment                                   suggestion_comment[]
  suggestion_vote                                      suggestion_vote[]
  trader_rep                                           trader_rep[]
  job                                                  job?                       @relation(fields: [jobId], references: [id])
  user                                                 user?                      @relation("userTouser", fields: [referrerId], references: [id])
  other_user                                           user[]                     @relation("userTouser")
  user_achievements                                    user_achievements?
  user_completed_course                                user_completed_course[]
  user_crafting_queue                                  user_crafting_queue[]
  user_item                                            user_item[]
  user_recipe                                          user_recipe[]
  user_talent                                          user_talent[]
  user_property                                        user_property[]
  statusMessage                                        String?                    @db.VarChar(255)
  statusMessageUpdatedAt                               DateTime?                  @db.DateTime(0)
  showLastOnline                                       Boolean                    @default(true)
  sent_friend_requests                                 friend_request[]           @relation("friend_request_sender")
  received_friend_requests                             friend_request[]           @relation("friend_request_receiver")
  friends                                              friend[]                   @relation("user_friends")
  friendOf                                             friend[]                   @relation("friend_of")
  rivals                                               rival[]                    @relation("user_rivals")
  rivalOf                                              rival[]                    @relation("rival_of")
  user_status_effect                                   user_status_effect[]
  user_skills                                          user_skill[]
  exploreNodes                                         explore_player_node[]
  ownedGang                                            gang[]                     @relation("gangOwner")
  user_pet                                             user_pet[]

  user_equipped_abilities user_equipped_abilities?

  @@unique([email])
  @@unique([username, email], map: "user_unique")
  @@index([jobId])
  @@index([referrerId])
  @@index([gangId])
  @@map("user")
}

model user_equipped_abilities {
  userId                  Int     @id
  user                    user    @relation(fields: [userId], references: [id], onDelete: Cascade)
  equippedAbility1Id      Int?
  equippedAbility2Id      Int?
  equippedAbility3Id      Int?
  equippedAbility4Id      Int?
  talent_equippedAbility1 talent? @relation("userEquippedAbility1", fields: [equippedAbility1Id], references: [id])
  talent_equippedAbility2 talent? @relation("userEquippedAbility2", fields: [equippedAbility2Id], references: [id])
  talent_equippedAbility3 talent? @relation("userEquippedAbility3", fields: [equippedAbility3Id], references: [id])
  talent_equippedAbility4 talent? @relation("userEquippedAbility4", fields: [equippedAbility4Id], references: [id])

  @@index([equippedAbility1Id])
  @@index([equippedAbility2Id])
  @@index([equippedAbility3Id])
  @@index([equippedAbility4Id])
  @@map("user_equipped_abilities")
}

model session {
  id             Int      @id @default(autoincrement())
  expiresAt      DateTime
  token          String
  ipAddress      String?  @db.Text
  userAgent      String?  @db.Text
  userId         Int
  impersonatedBy String?  @db.Text
  createdAt      DateTime
  updatedAt      DateTime
  user           user     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model account {
  id                    Int       @id @default(autoincrement())
  accountId             String    @db.Text
  providerId            String    @db.Text
  userId                Int
  accessToken           String?   @db.Text
  refreshToken          String?   @db.Text
  idToken               String?   @db.Text
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?   @db.Text
  password              String?   @db.Text
  createdAt             DateTime
  updatedAt             DateTime
  user                  user      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("account")
}

model verification {
  id         Int      @id @default(autoincrement())
  identifier String   @db.Text
  value      String   @db.Text
  expiresAt  DateTime
  createdAt  DateTime
  updatedAt  DateTime

  @@map("verification")
}

model user_item {
  id             Int             @id @default(autoincrement())
  count          Int             @db.UnsignedInt
  upgradeLevel   Int             @default(0) @db.UnsignedInt
  quality        ItemQuality     @default(normal)
  isTradeable    Boolean         @default(false)
  createdAt      DateTime        @default(now()) @db.DateTime(0)
  updatedAt      DateTime        @updatedAt @db.DateTime(0)
  userId         Int?
  itemId         Int?
  equipped_items equipped_item[]
  user           user?           @relation(fields: [userId], references: [id])
  item           item?           @relation(fields: [itemId], references: [id])

  @@index([itemId])
  @@index([userId])
}

model user_recipe {
  createdAt        DateTime        @default(now()) @db.DateTime(0)
  updatedAt        DateTime        @updatedAt @db.DateTime(0)
  craftingRecipeId Int
  userId           Int
  crafting_recipe  crafting_recipe @relation(fields: [craftingRecipeId], references: [id], onDelete: Cascade)
  user             user            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([craftingRecipeId, userId])
  @@index([userId])
}

model user_completed_course {
  id          Int      @id @default(autoincrement())
  courseId    Int
  completedAt DateTime @db.DateTime(0)
  createdAt   DateTime @default(now()) @db.DateTime(0)
  updatedAt   DateTime @updatedAt @db.DateTime(0)
  userId      Int?
  user        user?    @relation(fields: [userId], references: [id])

  @@index([userId])
}

model user_talent {
  level     Int?
  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)
  talentId  Int
  userId    Int
  talent    talent   @relation(fields: [talentId], references: [id], onDelete: Cascade)
  user      user     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([talentId, userId])
  @@index([userId])
}

model user_achievements {
  userId                  Int  @id
  battleWins              Int? @default(0) @db.UnsignedInt
  npcBattleWins           Int? @default(0) @db.UnsignedInt
  craftsCompleted         Int? @default(0) @db.UnsignedInt
  marketItemsSold         Int? @default(0) @db.UnsignedInt
  marketMoneyMade         Int? @default(0) @db.UnsignedInt
  totalMuggingGain        Int? @default(0) @db.UnsignedInt
  totalMuggingLoss        Int? @default(0) @db.UnsignedInt
  totalCasinoProfitLoss   Int? @default(0)
  questsCompleted         Int? @default(0) @db.UnsignedInt
  dailyQuestsCompleted    Int? @default(0) @db.UnsignedInt
  coursesCompleted        Int? @default(0) @db.UnsignedInt
  roguelikeNodesCompleted Int? @default(0) @db.UnsignedInt
  roguelikeMapsCompleted  Int? @default(0) @db.UnsignedInt
  examsCompleted          Int? @default(0) @db.UnsignedInt
  totalBountyRewards      Int? @default(0) @db.UnsignedInt
  totalBountyPlaced       Int? @default(0) @db.UnsignedInt
  totalMissionHours       Int? @default(0) @db.UnsignedInt
  suggestionsVoted        Int? @default(0) @db.UnsignedInt
  encountersCompleted     Int? @default(0) @db.UnsignedInt
  user                    user @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model user_crafting_queue {
  id               Int              @id @default(autoincrement())
  startedAt        BigInt           @db.UnsignedBigInt
  endsAt           BigInt           @db.UnsignedBigInt
  createdAt        DateTime         @default(now()) @db.DateTime(0)
  updatedAt        DateTime         @updatedAt @db.DateTime(0)
  userId           Int?
  craftingRecipeId Int?
  user             user?            @relation(fields: [userId], references: [id])
  crafting_recipe  crafting_recipe? @relation(fields: [craftingRecipeId], references: [id])

  @@index([userId])
  @@index([craftingRecipeId])
}

model property {
  id            Int             @id @default(autoincrement())
  name          String
  propertyType  String          @default("housing")
  cost          Int             @db.UnsignedInt
  upkeep        Int             @db.UnsignedInt
  slots         Int             @db.UnsignedInt
  buffs         Json
  description   String          @db.Text
  image         String?         @db.VarChar(255)
  createdAt     DateTime        @default(now()) @db.DateTime(0)
  updatedAt     DateTime        @updatedAt @db.DateTime(0)
  user_property user_property[]
}

model user_property {
  id             Int      @id @default(autoincrement())
  purchaseDate   DateTime @default(now()) @db.DateTime(0)
  lastUpkeepPaid DateTime @default(now()) @db.DateTime(0)
  furniture      Json     @default("[]")
  customization  Json     @default("{}")
  createdAt      DateTime @default(now()) @db.DateTime(0)
  updatedAt      DateTime @updatedAt @db.DateTime(0)
  userId         Int
  propertyId     Int
  user           user     @relation(fields: [userId], references: [id], onDelete: Cascade)
  property       property @relation(fields: [propertyId], references: [id])

  @@index([userId])
  @@index([propertyId])
}

model pet {
  id       Int    @id @default(autoincrement())
  name     String @db.VarChar(255)
  species  String @db.VarChar(255)
  maxLevel Int    @default(100)

  /// [EvolutionStages]
  evolution_stages Json @default("[]")

  createdAt DateTime   @default(now()) @db.DateTime(0)
  updatedAt DateTime   @updatedAt @db.DateTime(0)
  user_pet  user_pet[]
  item      item[]
}

model user_pet {
  id          Int     @id @default(autoincrement())
  name        String  @db.VarChar(255)
  level       Int     @default(1)
  isActive    Boolean @default(false)
  xp          Int     @default(0)
  nextLevelXp Int     @default(100)
  happiness   Int     @default(50)
  energy      Int     @default(100)

  /// [Evolution]
  evolution Json @default("{\"current\":\"egg\",\"next\":\"baby\",\"progress\":0,\"requiredLevel\":10}")

  userId    Int
  petId     Int
  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)
  user      user     @relation(fields: [userId], references: [id], onDelete: Cascade)
  pet       pet      @relation(fields: [petId], references: [id])

  @@index([userId])
  @@index([petId])
}

model friend_request {
  id         Int      @id @default(autoincrement())
  createdAt  DateTime @default(now()) @db.DateTime(0)
  updatedAt  DateTime @updatedAt @db.DateTime(0)
  senderId   Int
  receiverId Int
  sender     user     @relation("friend_request_sender", fields: [senderId], references: [id], onDelete: Cascade)
  receiver   user     @relation("friend_request_receiver", fields: [receiverId], references: [id], onDelete: Cascade)

  @@unique([senderId, receiverId])
  @@index([senderId])
  @@index([receiverId])
}

model friend {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)
  userId    Int
  friendId  Int
  note      String?  @db.VarChar(255)
  user      user     @relation("user_friends", fields: [userId], references: [id], onDelete: Cascade)
  friend    user     @relation("friend_of", fields: [friendId], references: [id], onDelete: Cascade)

  @@unique([userId, friendId])
  @@index([userId])
  @@index([friendId])
}

model rival {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)
  userId    Int
  rivalId   Int
  note      String?  @db.VarChar(255)
  user      user     @relation("user_rivals", fields: [userId], references: [id], onDelete: Cascade)
  rival     user     @relation("rival_of", fields: [rivalId], references: [id], onDelete: Cascade)

  @@unique([userId, rivalId])
  @@index([userId])
  @@index([rivalId])
}

model status_effect {
  id                 Int                      @id @default(autoincrement())
  name               String                   @db.VarChar(255)
  source             String?                  @db.VarChar(255)
  effectType         StatusEffectType
  category           String                   @db.VarChar(255)
  tier               StatusEffectTier?
  duration           Int                      @db.UnsignedInt
  modifier           Float?                   @db.Float
  modifierType       StatusEffectModifierType
  stackable          Boolean                  @default(false)
  maxStacks          Int?                     @db.UnsignedInt
  disabled           Boolean                  @default(false)
  description        String?                  @db.Text
  user_status_effect user_status_effect[]
}

model user_status_effect {
  id         Int            @id @default(autoincrement())
  stacks     Int            @default(1) @db.UnsignedInt
  endsAt     BigInt         @db.UnsignedBigInt
  customName String?        @db.VarChar(255)
  createdAt  DateTime       @default(now()) @db.DateTime(0)
  updatedAt  DateTime       @updatedAt @db.DateTime(0)
  effectId   Int?
  userId     Int?
  effect     status_effect? @relation(fields: [effectId], references: [id], onDelete: Cascade)
  user       user?          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([effectId, userId, customName])
  @@index([userId])
}

model user_skill {
  id           Int       @id @default(autoincrement())
  skillType    SkillType
  level        Int       @default(1) @db.UnsignedInt
  experience   Int       @default(0) @db.UnsignedInt
  talentPoints Int?      @default(0) @db.UnsignedInt
  createdAt    DateTime  @default(now()) @db.DateTime(0)
  updatedAt    DateTime  @updatedAt @db.DateTime(0)
  userId       Int
  user         user      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, skillType])
  @@index([userId])
}

model explore_static_node {
  id          Int                 @id @default(autoincrement())
  nodeType    ExploreNodeType
  title       String              @db.VarChar(255)
  description String              @db.Text
  position    Json // { "x": number, "y": number }
  metadata    Json? // e.g., { "shopId": 1 }
  location    ExploreNodeLocation
  shopId      Int?
  shop        shop?               @relation(fields: [shopId], references: [id])
  createdAt   DateTime            @default(now()) @db.DateTime(0)
  updatedAt   DateTime            @updatedAt @db.DateTime(0)

  @@index([shopId])
}

model explore_player_node {
  id          Int                 @id @default(autoincrement())
  nodeType    ExploreNodeType
  title       String              @db.VarChar(255)
  description String              @db.Text
  position    Json // { "x": number, "y": number }
  metadata    Json? // e.g., { "creatureId": 5 }
  location    ExploreNodeLocation
  status      ExploreNodeStatus   @default(available)
  expiresAt   DateTime?           @db.DateTime(0)
  createdAt   DateTime            @default(now()) @db.DateTime(0)
  updatedAt   DateTime            @updatedAt @db.DateTime(0)
  userId      Int
  user        user                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([location])
  @@index([userId, location, expiresAt])
}

// ===============================================
// Story Mode Models
// ===============================================

model story_season {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(255)
  description String?  @db.Text
  startDate   DateTime @db.DateTime(0)

  requiredLevel Int @default(1) @db.UnsignedInt

  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)

  story_chapter story_chapter[]
}

model story_chapter {
  id          Int     @id @default(autoincrement())
  seasonId    Int
  name        String  @db.VarChar(255)
  description String? @db.Text
  order       Int

  unlockDate DateTime @db.DateTime(0)

  requiredLevel Int @default(1) @db.UnsignedInt

  /// [RequiredChapterIds]
  requiredChapterIds Json?

  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)

  story_season story_season @relation(fields: [seasonId], references: [id], onDelete: Cascade)
  quests       quest[]

  @@unique([seasonId, order])
  @@index([seasonId])
  @@index([unlockDate])
}

model story_episode {
  id              Int                 @id @default(autoincrement())
  name            String              @db.VarChar(255)
  description     String?             @db.Text
  episodeType     StoryEpisodeType
  exploreLocation ExploreNodeLocation @default(shibuya)

  /// [StoryEpisodeContent]
  content Json // Contains dialogue, scenes, etc.

  /// [StoryEpisodeChoices]
  choices Json? // Defines player options and consequences for CHOICE types

  objectiveId     Int             @unique
  quest_objective quest_objective @relation(fields: [objectiveId], references: [id])

  createdAt DateTime @default(now()) @db.DateTime(0)
  updatedAt DateTime @updatedAt @db.DateTime(0)

  @@index([episodeType])
  @@index([exploreLocation])
}

enum CraftingSkills {
  fabrication
  electronics
  chemistry
  outfitting
}

enum BankTransactionTypes {
  bank_deposit
  bank_withdrawal
  bank_transfer
  shop_purchase
  player_trade
}

enum QuestObjectiveTypes {
  // Battle objectives
  DEFEAT_NPC // defeat specific NPC (target=creatureId) or any NPC (target=null)
  DEFEAT_NPC_IN_TURNS // Defeat a specific NPC within a turn limit
  DEFEAT_NPC_WITH_LOW_DAMAGE // Defeat any NPC while taking minimal damage
  DEFEAT_BOSS // Defeat N boss NPCs.
  DEFEAT_PLAYER // Defeat N players. Data specifies a specific player if needed.
  PVP_POST_BATTLE_CHOICE // PVP battle won with specific post-battle choice
  DEFEAT_PLAYER_XNAME // player with x in name defeated x times
  DEFEAT_SPECIFIC_PLAYER // specific player defeated x times
  WIN_BATTLE // Win N battles of any type (PvE or PvP).
  USE_ABILITY // Use an ability N times. Data specifies ID for specific, or null for any.

  // Item objectives
  ACQUIRE_ITEM // Obtain N of item (from loot, shops, etc.). Data specifies the item.
  CRAFT_ITEM // craft specific item (target=itemId) or any item (target=null)
  DELIVER_ITEM // Hand in N items to a quest giver or location.
  GATHER_RESOURCES // Gather N resources through mining, scavenging, or foraging. targetAction specifies activity type.

  // Bounty objectives
  PLACE_BOUNTY // Place N bounties on other players
  COLLECT_BOUNTY_REWARD // Collect the reward for N bounties

  // Misc objectives
  COMPLETE_MISSIONS // Complete N missions
  DONATE_TO_SHRINE // Donate N amount to the shrine
  VOTE_ON_SUGGESTION // Cast a vote in N community suggestions
  CHARACTER_ENCOUNTERS // Complete N character encounters
  TRAIN_STATS // Train N stats
  GAMBLING_SLOTS // Gamble N amount on slots

  // Story objectives
  COMPLETE_STORY_EPISODE
  // MAKE_STORY_CHOICE // V2 Feature
  // REACH_RELATIONSHIP_LEVEL // V2 Feature
  UNIQUE_OBJECTIVE
}

enum GangInviteTypes {
  invite
  inviteRequest
}

enum QuestProgressStatus {
  complete
  in_progress
  ready_to_complete
}

enum RecipeItemTypes {
  input
  output
}

enum SuggestionVoteTypes {
  upvote
  downvote
}

enum DropChanceTypes {
  creature
  boss
  daily
  roguelike
  quest
  scavenge
  character_encounter
}

enum EquipSlots {
  head
  chest
  hands
  legs
  feet
  finger
  offhand
  shield
  weapon
  ranged
}

enum ItemQuality {
  shoddy
  normal
  fine
  excellent
  superior
  perfect
  masterwork
}

enum ItemTypes {
  weapon
  ranged
  head
  chest
  hands
  legs
  feet
  finger
  offhand
  shield
  consumable
  crafting
  junk
  quest
  special
  recipe
  upgrade
  pet
  pet_food
}

enum ShopTypes {
  general
  weapon
  armour
  food
  furniture
  gang
}

enum TalentTree {
  strength
  intelligence
  dexterity
  defence
  endurance
  vitality
  mining
  scavenging
  foraging
  fabrication
  outfitting
  chemistry
  electronics
}

enum ItemRarities {
  novice
  standard
  enhanced
  specialist
  military
  legendary
}

enum SuggestionStates {
  New
  Accepted
  Completed
  Denied
}

enum ShopListingCurrency {
  yen
  gangCreds
  classPoints
}

enum AuctionItemStatus {
  complete
  in_progress
  expired
  cancelled
}

enum QuestTargetAction {
  mug
  cripple
  leave
}

enum ExploreNodeLocation {
  shibuya
  shinjuku
  bunkyo
  chiyoda
  minato
}

enum TravelMethod {
  walk
  bus
}

enum LocationTypes {
  church
  shrine
  mall
  alley
  school
  sewers
  themepark
  shibuya
  shinjuku
  bunkyo
  chiyoda
  minato
  any
}

enum CreatureStatTypes {
  balanced
  tank
  dps
}

enum UserTypes {
  student
  prefect
  admin
  guest
}

enum HospitalisedHealingTypes {
  full
  injury
}

enum StatusEffectType {
  BUFF
  DEBUFF
  NEUTRAL
}

enum StatusEffectTier {
  Minor
  Moderate
  Severe
  Critical
}

enum StatusEffectModifierType {
  add
  multiply
  divide
}

enum QuestRewardType {
  ITEM
  TALENT_POINTS
  GANG_CREDS
  CLASS_POINTS
}

enum SkillType {
  mining
  scavenging
  foraging
  fabrication
  outfitting
  chemistry
  electronics
  strength
  intelligence
  dexterity
  defence
  endurance
  vitality
}

enum ExploreNodeType {
  SHOP
  HOUSING
  BATTLE
  CHARACTER_ENCOUNTER
  ACTION
  CONDITION
  CHOICE
  STORY
  MINING_NODE
  SCAVENGE_NODE
  FORAGING_NODE
}

enum ExploreNodeStatus {
  completed
  available
  locked
  current
}

enum TalentType {
  passive
  ability
}

enum StoryEpisodeType {
  NARRATIVE
  CHOICE
  BATTLE // Future story episode type
}
