# Project Structure

## Root Level

```
├── chikara-frontend/     # Main game client (React/Vite)
├── chikara-backend/      # Game server (Express/Bun)
├── admin-panel/          # Admin dashboard (React/Vite)
├── chikara-landing/      # Marketing site (Next.js)
├── assets/               # Game assets (images, icons, characters)
├── docs/                 # Documentation and guides
└── turbo.json           # Turborepo configuration
```

## Frontend Structure (`chikara-frontend/`)

```
src/
├── app/                 # App-level configuration and providers
├── components/          # Reusable UI components
├── features/            # Feature-specific components and logic
├── hooks/               # Custom React hooks
├── lib/                 # Utilities and configurations
├── pages/               # Route components
├── types/               # TypeScript type definitions
└── utils/               # Helper functions
```

## Backend Structure (`chikara-backend/`)

```
src/
├── config/              # App configuration (database, redis, etc.)
├── core/                # Core game systems and business logic
├── features/            # Feature-specific modules
├── lib/                 # Shared utilities and services
├── middleware/          # Express middleware
├── queues/              # BullMQ job definitions
├── repositories/        # Data access layer
├── types/               # TypeScript type definitions
├── utils/               # Helper functions
├── routes.ts            # Main API routes
├── admin.routes.ts      # Admin API routes
└── server.ts            # Application entry point
```

## Admin Panel Structure (`admin-panel/`)

```
src/
├── components/          # UI components (tables, forms, charts)
├── hooks/               # Admin-specific hooks
├── lib/                 # Utilities and API client
├── pages/               # Admin dashboard pages
├── types/               # TypeScript definitions
└── helpers/             # Helper functions
```

## Assets Structure (`assets/`)

```
├── AI/                  # AI-generated backgrounds and locations
├── characters/          # Character sprites organized by name
└── icons/               # Game item icons organized by category
```

## Key Conventions

### File Naming

- **Components**: PascalCase (e.g., `GameBoard.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useGameState.ts`)
- **Utilities**: camelCase (e.g., `formatCurrency.ts`)
- **Types**: PascalCase (e.g., `UserProfile.ts`)
- **API Routes**: kebab-case (e.g., `user-profile.ts`)

### Folder Organization

- **Features**: Group related components, hooks, and utilities together
- **Shared**: Common components and utilities in `lib/` or `components/`
- **Types**: Centralized in `types/` folders with clear naming
- **Tests**: Co-located with source files using `.test.ts` or `.spec.ts`

### Import Patterns

- Use absolute imports from `src/` root
- Group imports: external libraries, internal modules, relative imports
- Prefer named exports over default exports for utilities

### Database Schema

- **Prisma**: Single schema file at `chikara-backend/prisma/schema.prisma`
- **Migrations**: Versioned in `prisma/migrations/`
- **Seeds**: Environment-specific seeders in `prisma/seeders/`

### Configuration Files

- **Environment**: `.env` files per workspace with `.env.example` templates
- **TypeScript**: Workspace-specific `tsconfig.json` files
- **ESLint/Prettier**: Consistent configuration across workspaces
