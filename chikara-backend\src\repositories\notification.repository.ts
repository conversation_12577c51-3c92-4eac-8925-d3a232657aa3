import { NotificationModel, db } from "../lib/db.js";

export const findNotifications = async (userId: number, limit: number) => {
    return await db.notification.findMany({
        where: { userId },
        orderBy: { createdAt: "desc" },
        take: limit,
    });
};

export const countUnreadNotifications = async (userId: number) => {
    return await db.notification.count({
        where: {
            userId,
            read: false,
        },
    });
};

export const findNotificationById = async (notificationId: number) => {
    return await db.notification.findUnique({
        where: { id: notificationId },
    });
};

export const updateNotification = async (notification: NotificationModel) => {
    return await db.notification.update({
        where: { id: notification.id },
        data: notification,
    });
};

export const findPushTokenByToken = async (token: string) => {
    return await db.push_token.findUnique({
        where: { token },
    });
};

export const createPushToken = async (userId: number, token: string) => {
    return await db.push_token.create({
        data: { userId, token },
    });
};

export const deletePushToken = async (userId: number, token: string) => {
    return await db.push_token.delete({
        where: { token },
    });
};
