import * as NotificationService from "../../core/notification.service.js";
import * as StatusEffectService from "../../core/statuseffect.service.js";
import * as UserService from "../../core/user.service.js";
import * as <PERSON><PERSON>elper from "../gang/gang.helpers.js";
import {
    findAllHospitalizedUsers,
    findAllInjuredUsers,
    findUserStatusEffectById,
    findUserStatusEffects,
} from "../../repositories/infirmary.repository.js";
import * as TalentHelper from "../talents/talents.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { NotificationTypes } from "../../types/notification.js";
import { LogErrorStack } from "../../utils/log.js";
import { Prisma, StatusEffectTier, StatusEffectType } from "@prisma/client";
import * as UserRepository from "../../repositories/user.repository.js";

import gameConfig from "../../config/gameConfig.js";
import type { ExtUserModel } from "../../lib/db.js";

export const getHospitalList = async () => {
    try {
        const cripples = await findAllHospitalizedUsers();
        return { data: cripples };
    } catch (error) {
        LogErrorStack({ message: "Failed to fetch infirmary list:", error });
        return { error: "Failed to fetch infirmary list", statusCode: 400 };
    }
};

interface PartialUserStatusEffect {
    id: number;
    stacks: number;
    endsAt: bigint;
    customName: string | null;
    effect: {
        name: string;
        effectType: StatusEffectType;
        category: string;
        tier: StatusEffectTier | null;
    } | null;
}

const getLongestStatusEffect = (statusEffects: PartialUserStatusEffect[] | null) => {
    if (!statusEffects || statusEffects.length === 0) {
        return null;
    }
    const sortedEffects = [...statusEffects].sort((a, b) => (a.endsAt > b.endsAt ? 1 : -1));
    return sortedEffects[0].endsAt;
};

export const getInjuredList = async (userId: number) => {
    try {
        const users = await findAllInjuredUsers(userId);
        const currentUser = await UserRepository.getUserById(userId);
        const gangId = Number.parseInt(currentUser?.gangId?.toString() || "0");

        // Make users that aren't in your gang anonymous
        const filteredUsers = users.map((user) => {
            const userJson = user;
            const resultUser: {
                hospitalisedUntil: unknown;
                hospitalisedReason: unknown;
                endsAt: unknown;
                user_status_effect: unknown;
                id?: unknown;
                username?: unknown;
                avatar?: unknown;
                gangId?: unknown;
            } = {
                hospitalisedUntil: userJson.hospitalisedUntil,
                hospitalisedReason: userJson.hospitalisedReason,
                endsAt: getLongestStatusEffect(userJson.user_status_effect),
                user_status_effect: userJson.user_status_effect,
            };

            if (gangId && Number.parseInt(userJson.gangId?.toString() || "0") === gangId) {
                resultUser.id = userJson.id;
                resultUser.username = userJson.username;
                resultUser.avatar = userJson.avatar;
                resultUser.gangId = userJson.gangId;
            }

            return resultUser;
        });

        return { data: filteredUsers };
    } catch (error) {
        LogErrorStack({ message: "Failed to fetch infirmary list:", error });
        return { error: "Failed to fetch infirmary list", statusCode: 400 };
    }
};

const calculateHospitalCost = async (currentUser: ExtUserModel, effectsOnly: boolean) => {
    const maxHealth = await currentUser.maxHealth;
    const hpToHeal = maxHealth - (currentUser.currentHealth || 0);
    const effects = await findUserStatusEffects(currentUser.id);

    if (!effects || (effects.length === 0 && hpToHeal < 1)) return 0;

    const minorEffects = effects.filter((e) => e.effect?.tier === StatusEffectTier.Minor);
    const moderateEffects = effects.filter((e) => e.effect?.tier === StatusEffectTier.Moderate);
    const severeEffects = effects.filter((e) => e.effect?.tier === StatusEffectTier.Severe);

    const minorCost = minorEffects.length * ((currentUser.level || 1) * gameConfig.MINOR_COST_PER_LEVEL) || 0;
    const moderateCost = moderateEffects.length * ((currentUser.level || 1) * gameConfig.MODERATE_COST_PER_LEVEL) || 0;
    const severeCost = severeEffects.length * ((currentUser.level || 1) * gameConfig.SEVERE_COST_PER_LEVEL) || 0;
    const hpCost = hpToHeal * gameConfig.COST_PER_HP || 0;

    let total = minorCost + moderateCost + severeCost;
    if (!effectsOnly) total += hpCost;

    return Math.round(total);
};

export const hospitalCheckIn = async (userId: number, injuryOnly: boolean) => {
    try {
        const currentUser = await UserRepository.getUserById(userId);
        if (!currentUser) {
            return { error: "User does not exist", statusCode: 400 };
        }
        const totalCost = await calculateHospitalCost(currentUser, injuryOnly);

        if (totalCost === 0) {
            return { error: "You don't need healing!", statusCode: 400 };
        }

        if (totalCost > (currentUser.cash || 0)) {
            return { error: "You don't have enough cash to check in to hospital", statusCode: 400 };
        }

        const tenMinsInMs = 10 * 60 * 1000;
        const now = Date.now();

        const updatedUser: Prisma.userUpdateInput = {
            cash: (currentUser.cash || 0) - totalCost,
            hospitalisedUntil: BigInt(now) + BigInt(tenMinsInMs),
            hospitalisedHealingType: injuryOnly ? "injury" : "full",
        };

        await UserService.updateUser(currentUser.id, updatedUser);

        return { data: { success: true } };
    } catch (error) {
        LogErrorStack({ message: "Failed to check in to hospital:", error });
        return { error: "Failed to check in to hospital", statusCode: 400 };
    }
};

export const revivePlayer = async (userId: number, targetStatusEffectId: number) => {
    try {
        const currentUser = await UserRepository.getUserById(userId);

        if (!targetStatusEffectId) {
            return { error: "No healing target selected", statusCode: 400 };
        }

        // Status effect ID is used as targetId to keep the user anonymous
        const statusEffect = await findUserStatusEffectById(targetStatusEffectId);

        if (statusEffect === null) {
            return { error: "User doesn't need healing!", statusCode: 400 };
        }

        const target = await UserRepository.getUserById(statusEffect.userId || 0);

        if (target === null) {
            return { error: "User does not exist", statusCode: 400 };
        }

        if (target.id === userId) {
            return { error: "Can't heal yourself!", statusCode: 400 };
        }

        if (target.hospitalisedUntil) {
            return { error: "User is already in hospital", statusCode: 400 };
        }

        const reviveTalent = await TalentHelper.UserHasReviveTalent(userId);

        if (!reviveTalent) {
            return { error: "You don't have the talent to heal other players", statusCode: 400 };
        }

        await StatusEffectService.removeUserStatusEffects(target.id);

        const updatedUser: Prisma.userUpdateInput = {
            hospitalisedUntil: null,
            hospitalisedHealingType: null,
            hospitalisedReason: null,
            currentHealth: await target.maxHealth,
        };

        await UserService.updateUser(target.id, updatedUser);

        if (currentUser?.gangId) {
            try {
                await GangHelper.AddGangLifeEssence(currentUser, target, 5, "EssenceGainedRevive");
            } catch (error) {
                LogErrorStack({ message: "Failed to add gang life essence:", error });
            }
        }

        logAction({
            action: "HEALED_PLAYER",
            userId,
            info: {
                targetId: target.id,
                targetUsername: target.username,
                // dailyHealsUsed: 1,
            },
        });

        NotificationService.NotifyUser(target.id, NotificationTypes.life_noted, { initiatorId: userId });
        return { data: { success: true } };
    } catch (error) {
        LogErrorStack({ message: "Failed to heal user:", error });
        return { error: "Failed to heal user", statusCode: 400 };
    }
};
